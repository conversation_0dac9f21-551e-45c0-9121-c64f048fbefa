
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  price: 'price',
  stock: 'stock',
  minStock: 'minStock',
  category: 'category',
  supplier: 'supplier',
  barcode: 'barcode',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  address: 'address',
  taxId: 'taxId',
  companyName: 'companyName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  customerId: 'customerId',
  subtotal: 'subtotal',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  discountRate: 'discountRate',
  discountAmount: 'discountAmount',
  totalAmount: 'totalAmount',
  status: 'status',
  paymentMethod: 'paymentMethod',
  paymentStatus: 'paymentStatus',
  paidAmount: 'paidAmount',
  dueDate: 'dueDate',
  issueDate: 'issueDate',
  notes: 'notes',
  terms: 'terms',
  showImages: 'showImages',
  currency: 'currency',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total',
  productId: 'productId',
  invoiceId: 'invoiceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  invoiceId: 'invoiceId',
  amount: 'amount',
  method: 'method',
  reference: 'reference',
  notes: 'notes',
  paymentDate: 'paymentDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductImageScalarFieldEnum = {
  id: 'id',
  filename: 'filename',
  originalName: 'originalName',
  path: 'path',
  size: 'size',
  mimeType: 'mimeType',
  isMain: 'isMain',
  order: 'order',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AppSettingsScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  category: 'category',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyInfoScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  website: 'website',
  taxId: 'taxId',
  logo: 'logo',
  currency: 'currency',
  language: 'language',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StockMovementScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  type: 'type',
  quantity: 'quantity',
  reason: 'reason',
  reference: 'reference',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceSettingsScalarFieldEnum = {
  id: 'id',
  companyName: 'companyName',
  companyAddress: 'companyAddress',
  companyPhone: 'companyPhone',
  companyEmail: 'companyEmail',
  companyWebsite: 'companyWebsite',
  companyTaxId: 'companyTaxId',
  companyCurrency: 'companyCurrency',
  companyLogo: 'companyLogo',
  invoicePrefix: 'invoicePrefix',
  invoiceNumberLength: 'invoiceNumberLength',
  defaultTaxRate: 'defaultTaxRate',
  defaultPaymentTerms: 'defaultPaymentTerms',
  autoGenerateNumber: 'autoGenerateNumber',
  showImagesInInvoice: 'showImagesInInvoice',
  allowPartialPayments: 'allowPartialPayments',
  requireCustomerInfo: 'requireCustomerInfo',
  defaultDueDays: 'defaultDueDays',
  invoiceFooter: 'invoiceFooter',
  invoiceNotes: 'invoiceNotes',
  showCompanyLogo: 'showCompanyLogo',
  showCompanyName: 'showCompanyName',
  showCompanyAddress: 'showCompanyAddress',
  showCompanyPhone: 'showCompanyPhone',
  showCompanyEmail: 'showCompanyEmail',
  showCompanyWebsite: 'showCompanyWebsite',
  showTaxId: 'showTaxId',
  showInvoiceNumber: 'showInvoiceNumber',
  showInvoiceDate: 'showInvoiceDate',
  showDueDate: 'showDueDate',
  showPaymentTerms: 'showPaymentTerms',
  showCustomerName: 'showCustomerName',
  showCustomerAddress: 'showCustomerAddress',
  showCustomerPhone: 'showCustomerPhone',
  showCustomerEmail: 'showCustomerEmail',
  showProductImages: 'showProductImages',
  showProductCode: 'showProductCode',
  showProductDescription: 'showProductDescription',
  showQuantity: 'showQuantity',
  showUnitPrice: 'showUnitPrice',
  showDiscount: 'showDiscount',
  showTotalPrice: 'showTotalPrice',
  showItemNumbers: 'showItemNumbers',
  showSubtotal: 'showSubtotal',
  showTaxAmount: 'showTaxAmount',
  showDiscountAmount: 'showDiscountAmount',
  showTotalAmount: 'showTotalAmount',
  showNotes: 'showNotes',
  showFooter: 'showFooter',
  showSignature: 'showSignature',
  showQRCode: 'showQRCode',
  showBankDetails: 'showBankDetails',
  showPaymentInstructions: 'showPaymentInstructions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.ProductOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  category: 'category',
  supplier: 'supplier',
  barcode: 'barcode'
};

exports.Prisma.CustomerOrderByRelevanceFieldEnum = {
  name: 'name',
  email: 'email',
  phone: 'phone',
  address: 'address',
  taxId: 'taxId',
  companyName: 'companyName'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  email: 'email',
  name: 'name',
  password: 'password'
};

exports.Prisma.InvoiceOrderByRelevanceFieldEnum = {
  invoiceNumber: 'invoiceNumber',
  status: 'status',
  paymentMethod: 'paymentMethod',
  paymentStatus: 'paymentStatus',
  notes: 'notes',
  terms: 'terms',
  currency: 'currency'
};

exports.Prisma.PaymentOrderByRelevanceFieldEnum = {
  method: 'method',
  reference: 'reference',
  notes: 'notes'
};

exports.Prisma.ProductImageOrderByRelevanceFieldEnum = {
  filename: 'filename',
  originalName: 'originalName',
  path: 'path',
  mimeType: 'mimeType'
};

exports.Prisma.AppSettingsOrderByRelevanceFieldEnum = {
  key: 'key',
  value: 'value',
  description: 'description',
  category: 'category'
};

exports.Prisma.CompanyInfoOrderByRelevanceFieldEnum = {
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  website: 'website',
  taxId: 'taxId',
  logo: 'logo',
  currency: 'currency',
  language: 'language'
};

exports.Prisma.StockMovementOrderByRelevanceFieldEnum = {
  type: 'type',
  reason: 'reason',
  reference: 'reference',
  notes: 'notes'
};

exports.Prisma.InvoiceSettingsOrderByRelevanceFieldEnum = {
  companyName: 'companyName',
  companyAddress: 'companyAddress',
  companyPhone: 'companyPhone',
  companyEmail: 'companyEmail',
  companyWebsite: 'companyWebsite',
  companyTaxId: 'companyTaxId',
  companyCurrency: 'companyCurrency',
  companyLogo: 'companyLogo',
  invoicePrefix: 'invoicePrefix',
  defaultPaymentTerms: 'defaultPaymentTerms',
  invoiceFooter: 'invoiceFooter',
  invoiceNotes: 'invoiceNotes'
};


exports.Prisma.ModelName = {
  Product: 'Product',
  Customer: 'Customer',
  User: 'User',
  Invoice: 'Invoice',
  InvoiceItem: 'InvoiceItem',
  Payment: 'Payment',
  ProductImage: 'ProductImage',
  AppSettings: 'AppSettings',
  CompanyInfo: 'CompanyInfo',
  StockMovement: 'StockMovement',
  InvoiceSettings: 'InvoiceSettings'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
