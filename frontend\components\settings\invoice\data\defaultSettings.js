// frontend/components/settings/invoice/data/defaultSettings.js

export const defaultInvoiceDisplaySettings = {
  // عناصر الرأس
  showCompanyLogo: true,
  showCompanyName: true,
  showCompanyAddress: true,
  showCompanyPhone: true,
  showCompanyEmail: true,
  showCompanyWebsite: true, // اختياري - يظهر فقط إذا كان متوفراً
  showTaxId: true,

  // معلومات الفاتورة
  showInvoiceNumber: true,
  showInvoiceDate: true,
  showDueDate: true,
  showPaymentTerms: true,

  // معلومات العميل
  showCustomerName: true,
  showCustomerAddress: true,
  showCustomerPhone: true,
  showCustomerEmail: true,

  // عناصر الجدول
  showProductImages: true,
  showProductCode: true,
  showProductDescription: true,
  showQuantity: true,
  showUnitPrice: true,
  showDiscount: false,
  showTotalPrice: true,

  // المجاميع
  showSubtotal: true,
  showTaxAmount: true,
  showDiscountAmount: false,
  showTotalAmount: true,

  // التذييل
  showNotes: true,
  showFooter: true,
  showSignature: false,
  showQRCode: false,

  // إعدادات إضافية
  showItemNumbers: true,
  showBankDetails: false,
  showPaymentInstructions: true
};

export const sectionFields = {
  header: ['showCompanyLogo', 'showCompanyName', 'showCompanyAddress', 'showCompanyPhone', 'showCompanyEmail', 'showCompanyWebsite', 'showTaxId'],
  invoice: ['showInvoiceNumber', 'showInvoiceDate', 'showDueDate', 'showPaymentTerms'],
  customer: ['showCustomerName', 'showCustomerAddress', 'showCustomerPhone', 'showCustomerEmail'],
  items: ['showProductImages', 'showProductCode', 'showProductDescription', 'showQuantity', 'showUnitPrice', 'showDiscount', 'showTotalPrice', 'showItemNumbers'],
  totals: ['showSubtotal', 'showTaxAmount', 'showDiscountAmount', 'showTotalAmount'],
  footer: ['showNotes', 'showFooter', 'showSignature', 'showQRCode', 'showBankDetails', 'showPaymentInstructions']
};
