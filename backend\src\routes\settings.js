// backend/src/routes/settings.js
const express = require('express');
const { PrismaClient } = require('@prisma/client');
const router = express.Router();

const prisma = new PrismaClient();

// الحصول على جميع الإعدادات
router.get('/', async (req, res) => {
  try {
    // البحث عن إعدادات الفواتير (يجب أن يكون هناك سجل واحد فقط)
    let settings = await prisma.invoiceSettings.findFirst();
    
    // إذا لم توجد إعدادات، إنشاء إعدادات افتراضية
    if (!settings) {
      settings = await prisma.invoiceSettings.create({
        data: {
          // القيم الافتراضية ستأتي من schema.prisma
        }
      });
    }
    
    // تحويل البيانات إلى التنسيق المطلوب
    const formattedSettings = {
      company: {
        companyName: settings.companyName,
        companyAddress: settings.companyAddress,
        companyPhone: settings.companyPhone,
        companyEmail: settings.companyEmail,
        companyWebsite: settings.companyWebsite,
        taxId: settings.companyTaxId,
        currency: settings.companyCurrency,
        logoUrl: settings.companyLogo
      },
      invoice: {
        invoicePrefix: settings.invoicePrefix,
        invoiceNumberLength: settings.invoiceNumberLength,
        defaultTaxRate: parseFloat(settings.defaultTaxRate),
        defaultPaymentTerms: settings.defaultPaymentTerms,
        autoGenerateInvoiceNumber: settings.autoGenerateNumber,
        showImagesInInvoice: settings.showImagesInInvoice,
        allowPartialPayments: settings.allowPartialPayments,
        requireCustomerInfo: settings.requireCustomerInfo,
        defaultDueDays: settings.defaultDueDays,
        invoiceFooter: settings.invoiceFooter,
        invoiceNotes: settings.invoiceNotes,
        display: {
          // رأس الفاتورة
          showCompanyLogo: settings.showCompanyLogo,
          showCompanyName: settings.showCompanyName,
          showCompanyAddress: settings.showCompanyAddress,
          showCompanyPhone: settings.showCompanyPhone,
          showCompanyEmail: settings.showCompanyEmail,
          showCompanyWebsite: settings.showCompanyWebsite,
          showTaxId: settings.showTaxId,
          
          // معلومات الفاتورة
          showInvoiceNumber: settings.showInvoiceNumber,
          showInvoiceDate: settings.showInvoiceDate,
          showDueDate: settings.showDueDate,
          showPaymentTerms: settings.showPaymentTerms,
          
          // معلومات العميل
          showCustomerName: settings.showCustomerName,
          showCustomerAddress: settings.showCustomerAddress,
          showCustomerPhone: settings.showCustomerPhone,
          showCustomerEmail: settings.showCustomerEmail,
          
          // عناصر الجدول
          showProductImages: settings.showProductImages,
          showProductCode: settings.showProductCode,
          showProductDescription: settings.showProductDescription,
          showQuantity: settings.showQuantity,
          showUnitPrice: settings.showUnitPrice,
          showDiscount: settings.showDiscount,
          showTotalPrice: settings.showTotalPrice,
          showItemNumbers: settings.showItemNumbers,
          
          // المجاميع
          showSubtotal: settings.showSubtotal,
          showTaxAmount: settings.showTaxAmount,
          showDiscountAmount: settings.showDiscountAmount,
          showTotalAmount: settings.showTotalAmount,
          
          // التذييل
          showNotes: settings.showNotes,
          showFooter: settings.showFooter,
          showSignature: settings.showSignature,
          showQRCode: settings.showQRCode,
          showBankDetails: settings.showBankDetails,
          showPaymentInstructions: settings.showPaymentInstructions
        }
      }
    };
    
    res.json(formattedSettings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ error: 'فشل في جلب الإعدادات' });
  }
});

// حفظ إعدادات الشركة
router.post('/company', async (req, res) => {
  try {
    const {
      companyName,
      companyAddress,
      companyPhone,
      companyEmail,
      companyWebsite,
      taxId,
      currency,
      logoUrl
    } = req.body;
    
    // البحث عن الإعدادات الحالية أو إنشاء جديدة
    let settings = await prisma.invoiceSettings.findFirst();
    
    if (settings) {
      // تحديث الإعدادات الموجودة
      settings = await prisma.invoiceSettings.update({
        where: { id: settings.id },
        data: {
          companyName: companyName || '',
          companyAddress: companyAddress || '',
          companyPhone: companyPhone || '',
          companyEmail: companyEmail || '',
          companyWebsite: companyWebsite || '',
          companyTaxId: taxId || '',
          companyCurrency: currency || 'SAR',
          companyLogo: logoUrl || null
        }
      });
    } else {
      // إنشاء إعدادات جديدة
      settings = await prisma.invoiceSettings.create({
        data: {
          companyName: companyName || '',
          companyAddress: companyAddress || '',
          companyPhone: companyPhone || '',
          companyEmail: companyEmail || '',
          companyWebsite: companyWebsite || '',
          companyTaxId: taxId || '',
          companyCurrency: currency || 'SAR',
          companyLogo: logoUrl || null
        }
      });
    }
    
    res.json({ message: 'تم حفظ إعدادات الشركة بنجاح', settings });
  } catch (error) {
    console.error('Error saving company settings:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات الشركة' });
  }
});

// حفظ إعدادات الفواتير العامة
router.post('/invoice', async (req, res) => {
  try {
    const {
      invoicePrefix,
      invoiceNumberLength,
      defaultTaxRate,
      defaultPaymentTerms,
      autoGenerateInvoiceNumber,
      showImagesInInvoice,
      allowPartialPayments,
      requireCustomerInfo,
      defaultDueDays,
      invoiceFooter,
      invoiceNotes
    } = req.body;
    
    // البحث عن الإعدادات الحالية أو إنشاء جديدة
    let settings = await prisma.invoiceSettings.findFirst();
    
    if (settings) {
      // تحديث الإعدادات الموجودة
      settings = await prisma.invoiceSettings.update({
        where: { id: settings.id },
        data: {
          invoicePrefix: invoicePrefix || 'INV',
          invoiceNumberLength: invoiceNumberLength || 6,
          defaultTaxRate: defaultTaxRate || 15,
          defaultPaymentTerms: defaultPaymentTerms || '',
          autoGenerateNumber: autoGenerateInvoiceNumber ?? true,
          showImagesInInvoice: showImagesInInvoice ?? true,
          allowPartialPayments: allowPartialPayments ?? true,
          requireCustomerInfo: requireCustomerInfo ?? true,
          defaultDueDays: defaultDueDays || 30,
          invoiceFooter: invoiceFooter || '',
          invoiceNotes: invoiceNotes || ''
        }
      });
    } else {
      // إنشاء إعدادات جديدة
      settings = await prisma.invoiceSettings.create({
        data: {
          invoicePrefix: invoicePrefix || 'INV',
          invoiceNumberLength: invoiceNumberLength || 6,
          defaultTaxRate: defaultTaxRate || 15,
          defaultPaymentTerms: defaultPaymentTerms || '',
          autoGenerateNumber: autoGenerateInvoiceNumber ?? true,
          showImagesInInvoice: showImagesInInvoice ?? true,
          allowPartialPayments: allowPartialPayments ?? true,
          requireCustomerInfo: requireCustomerInfo ?? true,
          defaultDueDays: defaultDueDays || 30,
          invoiceFooter: invoiceFooter || '',
          invoiceNotes: invoiceNotes || ''
        }
      });
    }
    
    res.json({ message: 'تم حفظ إعدادات الفواتير بنجاح', settings });
  } catch (error) {
    console.error('Error saving invoice settings:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات الفواتير' });
  }
});

// حفظ إعدادات عرض الفاتورة
router.post('/invoice/display', async (req, res) => {
  try {
    const displaySettings = req.body;
    
    // البحث عن الإعدادات الحالية أو إنشاء جديدة
    let settings = await prisma.invoiceSettings.findFirst();
    
    if (settings) {
      // تحديث الإعدادات الموجودة
      settings = await prisma.invoiceSettings.update({
        where: { id: settings.id },
        data: {
          // رأس الفاتورة
          showCompanyLogo: displaySettings.showCompanyLogo ?? true,
          showCompanyName: displaySettings.showCompanyName ?? true,
          showCompanyAddress: displaySettings.showCompanyAddress ?? true,
          showCompanyPhone: displaySettings.showCompanyPhone ?? true,
          showCompanyEmail: displaySettings.showCompanyEmail ?? true,
          showCompanyWebsite: displaySettings.showCompanyWebsite ?? true,
          showTaxId: displaySettings.showTaxId ?? true,
          
          // معلومات الفاتورة
          showInvoiceNumber: displaySettings.showInvoiceNumber ?? true,
          showInvoiceDate: displaySettings.showInvoiceDate ?? true,
          showDueDate: displaySettings.showDueDate ?? true,
          showPaymentTerms: displaySettings.showPaymentTerms ?? true,
          
          // معلومات العميل
          showCustomerName: displaySettings.showCustomerName ?? true,
          showCustomerAddress: displaySettings.showCustomerAddress ?? true,
          showCustomerPhone: displaySettings.showCustomerPhone ?? true,
          showCustomerEmail: displaySettings.showCustomerEmail ?? true,
          
          // عناصر الجدول
          showProductImages: displaySettings.showProductImages ?? true,
          showProductCode: displaySettings.showProductCode ?? true,
          showProductDescription: displaySettings.showProductDescription ?? true,
          showQuantity: displaySettings.showQuantity ?? true,
          showUnitPrice: displaySettings.showUnitPrice ?? true,
          showDiscount: displaySettings.showDiscount ?? false,
          showTotalPrice: displaySettings.showTotalPrice ?? true,
          showItemNumbers: displaySettings.showItemNumbers ?? true,
          
          // المجاميع
          showSubtotal: displaySettings.showSubtotal ?? true,
          showTaxAmount: displaySettings.showTaxAmount ?? true,
          showDiscountAmount: displaySettings.showDiscountAmount ?? false,
          showTotalAmount: displaySettings.showTotalAmount ?? true,
          
          // التذييل
          showNotes: displaySettings.showNotes ?? true,
          showFooter: displaySettings.showFooter ?? true,
          showSignature: displaySettings.showSignature ?? false,
          showQRCode: displaySettings.showQRCode ?? false,
          showBankDetails: displaySettings.showBankDetails ?? false,
          showPaymentInstructions: displaySettings.showPaymentInstructions ?? true
        }
      });
    } else {
      // إنشاء إعدادات جديدة مع إعدادات العرض
      settings = await prisma.invoiceSettings.create({
        data: {
          // رأس الفاتورة
          showCompanyLogo: displaySettings.showCompanyLogo ?? true,
          showCompanyName: displaySettings.showCompanyName ?? true,
          showCompanyAddress: displaySettings.showCompanyAddress ?? true,
          showCompanyPhone: displaySettings.showCompanyPhone ?? true,
          showCompanyEmail: displaySettings.showCompanyEmail ?? true,
          showCompanyWebsite: displaySettings.showCompanyWebsite ?? true,
          showTaxId: displaySettings.showTaxId ?? true,
          
          // معلومات الفاتورة
          showInvoiceNumber: displaySettings.showInvoiceNumber ?? true,
          showInvoiceDate: displaySettings.showInvoiceDate ?? true,
          showDueDate: displaySettings.showDueDate ?? true,
          showPaymentTerms: displaySettings.showPaymentTerms ?? true,
          
          // معلومات العميل
          showCustomerName: displaySettings.showCustomerName ?? true,
          showCustomerAddress: displaySettings.showCustomerAddress ?? true,
          showCustomerPhone: displaySettings.showCustomerPhone ?? true,
          showCustomerEmail: displaySettings.showCustomerEmail ?? true,
          
          // عناصر الجدول
          showProductImages: displaySettings.showProductImages ?? true,
          showProductCode: displaySettings.showProductCode ?? true,
          showProductDescription: displaySettings.showProductDescription ?? true,
          showQuantity: displaySettings.showQuantity ?? true,
          showUnitPrice: displaySettings.showUnitPrice ?? true,
          showDiscount: displaySettings.showDiscount ?? false,
          showTotalPrice: displaySettings.showTotalPrice ?? true,
          showItemNumbers: displaySettings.showItemNumbers ?? true,
          
          // المجاميع
          showSubtotal: displaySettings.showSubtotal ?? true,
          showTaxAmount: displaySettings.showTaxAmount ?? true,
          showDiscountAmount: displaySettings.showDiscountAmount ?? false,
          showTotalAmount: displaySettings.showTotalAmount ?? true,
          
          // التذييل
          showNotes: displaySettings.showNotes ?? true,
          showFooter: displaySettings.showFooter ?? true,
          showSignature: displaySettings.showSignature ?? false,
          showQRCode: displaySettings.showQRCode ?? false,
          showBankDetails: displaySettings.showBankDetails ?? false,
          showPaymentInstructions: displaySettings.showPaymentInstructions ?? true
        }
      });
    }
    
    res.json({ message: 'تم حفظ إعدادات عرض الفاتورة بنجاح', settings });
  } catch (error) {
    console.error('Error saving display settings:', error);
    res.status(500).json({ error: 'فشل في حفظ إعدادات العرض' });
  }
});

module.exports = router;
