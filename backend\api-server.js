// backend/api-server.js
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5001;

// إعداد CORS والمعالجة
app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل خادم API...');

// بيانات تجريبية
let settings = {
  company: {
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    companyWebsite: '',
    taxId: '',
    currency: 'SAR',
    logoUrl: null
  },
  invoice: {
    invoicePrefix: 'INV',
    invoiceNumberLength: 6,
    defaultTaxRate: 15,
    defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    autoGenerateInvoiceNumber: true,
    showImagesInInvoice: true,
    allowPartialPayments: true,
    requireCustomerInfo: true,
    defaultDueDays: 30,
    invoiceFooter: '',
    invoiceNotes: '',
    display: {
      showCompanyLogo: true,
      showCompanyName: true,
      showCompanyAddress: true,
      showCompanyPhone: true,
      showCompanyEmail: true,
      showCompanyWebsite: true,
      showTaxId: true,
      showInvoiceNumber: true,
      showInvoiceDate: true,
      showDueDate: true,
      showPaymentTerms: true,
      showCustomerName: true,
      showCustomerAddress: true,
      showCustomerPhone: true,
      showCustomerEmail: true,
      showProductImages: true,
      showProductCode: true,
      showProductDescription: true,
      showQuantity: true,
      showUnitPrice: true,
      showDiscount: false,
      showTotalPrice: true,
      showItemNumbers: true,
      showSubtotal: true,
      showTaxAmount: true,
      showDiscountAmount: false,
      showTotalAmount: true,
      showNotes: true,
      showFooter: true,
      showSignature: false,
      showQRCode: false,
      showBankDetails: false,
      showPaymentInstructions: true
    }
  }
};

// بيانات تجريبية للمنتجات
const products = [
  {
    id: 1,
    name: 'خدمات تطوير البرمجيات',
    description: 'تطوير تطبيقات ويب ومحمولة متقدمة',
    price: 5000,
    stock: 10,
    category: 'خدمات تقنية',
    sku: 'DEV-001',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    images: []
  },
  {
    id: 2,
    name: 'استشارات تقنية',
    description: 'استشارات متخصصة في التحول الرقمي',
    price: 300,
    stock: 50,
    category: 'استشارات',
    sku: 'CONS-001',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    images: []
  },
  {
    id: 3,
    name: 'دعم فني متخصص',
    description: 'دعم فني شامل للأنظمة والتطبيقات',
    price: 150,
    stock: 25,
    category: 'دعم فني',
    sku: 'SUPP-001',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    images: []
  }
];

// بيانات تجريبية للفواتير
const invoices = [
  {
    id: 1,
    invoiceNumber: 'INV-001',
    total: 500,
    status: 'paid',
    date: '2024-01-15',
    dueDate: '2024-02-15',
    customer: {
      id: 1,
      name: 'شركة التقنية المتطورة',
      email: '<EMAIL>',
      phone: '+966 11 555 0123',
      address: 'الرياض، المملكة العربية السعودية'
    },
    items: [
      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 500, total: 500 }
    ],
    subtotal: 500,
    tax: 75,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    invoiceNumber: 'INV-002',
    total: 750,
    status: 'pending',
    date: '2024-01-20',
    dueDate: '2024-02-20',
    customer: {
      id: 2,
      name: 'مؤسسة الابتكار التجاري',
      email: '<EMAIL>',
      phone: '+966 12 555 0456',
      address: 'جدة، المملكة العربية السعودية'
    },
    items: [
      { id: 1, description: 'استشارات تقنية', quantity: 5, price: 150, total: 750 }
    ],
    subtotal: 750,
    tax: 112.5,
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 3,
    invoiceNumber: 'INV-003',
    total: 300,
    status: 'draft',
    date: '2024-01-25',
    dueDate: '2024-02-25',
    customer: {
      id: 3,
      name: 'شركة الحلول الذكية',
      email: '<EMAIL>',
      phone: '+966 13 555 0789',
      address: 'الدمام، المملكة العربية السعودية'
    },
    items: [
      { id: 1, description: 'دعم فني', quantity: 2, price: 150, total: 300 }
    ],
    subtotal: 300,
    tax: 45,
    createdAt: '2024-01-25T09:15:00Z',
    updatedAt: '2024-01-25T09:15:00Z'
  }
];

// مسارات API

// اختبار الخادم
app.get('/api', (req, res) => {
  console.log('✅ GET /api');
  res.json({ message: 'API Server is working!', timestamp: new Date().toISOString() });
});

// إعدادات الفواتير
app.get('/api/settings', (req, res) => {
  console.log('📥 GET /api/settings');
  res.json(settings);
});

app.post('/api/settings/company', (req, res) => {
  console.log('📤 POST /api/settings/company:', req.body);
  settings.company = { ...settings.company, ...req.body };
  res.json({ message: 'تم حفظ إعدادات الشركة بنجاح', settings: settings.company });
});

app.post('/api/settings/invoice', (req, res) => {
  console.log('📤 POST /api/settings/invoice:', req.body);
  settings.invoice = { ...settings.invoice, ...req.body, display: settings.invoice.display };
  res.json({ message: 'تم حفظ إعدادات الفواتير بنجاح', settings: settings.invoice });
});

app.post('/api/settings/invoice/display', (req, res) => {
  console.log('📤 POST /api/settings/invoice/display:', req.body);
  settings.invoice.display = { ...settings.invoice.display, ...req.body };
  res.json({ message: 'تم حفظ إعدادات العرض بنجاح', settings: settings.invoice.display });
});

// مسارات المنتجات
app.get('/api/products', (req, res) => {
  console.log('📥 GET /api/products');
  res.json({ products, total: products.length });
});

app.get('/api/products/low-stock', (req, res) => {
  console.log('📥 GET /api/products/low-stock');
  const lowStockProducts = products.filter(p => p.stock < 20);
  res.json({ products: lowStockProducts, total: lowStockProducts.length });
});

// الحصول على منتج محدد
app.get('/api/products/:id', (req, res) => {
  console.log(`📥 GET /api/products/${req.params.id}`);
  const product = products.find(p => p.id === parseInt(req.params.id));
  if (product) {
    res.json(product);
  } else {
    res.status(404).json({ error: 'المنتج غير موجود' });
  }
});

// مسارات الفواتير
app.get('/api/invoices', (req, res) => {
  console.log('📥 GET /api/invoices');
  res.json({
    invoices,
    total: invoices.length,
    pagination: { page: 1, limit: 10, totalPages: 1 }
  });
});

app.get('/api/invoices/stats', (req, res) => {
  console.log('📥 GET /api/invoices/stats');
  const stats = {
    total: invoices.length,
    paid: invoices.filter(i => i.status === 'paid').length,
    pending: invoices.filter(i => i.status === 'pending').length,
    draft: invoices.filter(i => i.status === 'draft').length,
    totalAmount: invoices.reduce((sum, i) => sum + i.total, 0),
    paidAmount: invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.total, 0)
  };
  res.json(stats);
});

// الحصول على فاتورة محددة
app.get('/api/invoices/:id', (req, res) => {
  console.log(`📥 GET /api/invoices/${req.params.id}`);
  const invoice = invoices.find(i => i.id === parseInt(req.params.id));
  if (invoice) {
    res.json(invoice);
  } else {
    res.status(404).json({ error: 'الفاتورة غير موجودة' });
  }
});

// الحصول على عميل محدد
app.get('/api/customers/:id', (req, res) => {
  console.log(`📥 GET /api/customers/${req.params.id}`);
  const customers = [
    {
      id: 1,
      name: 'شركة التقنية المتطورة',
      email: '<EMAIL>',
      phone: '+966 11 555 0123',
      address: 'الرياض، المملكة العربية السعودية',
      taxId: '300123456789003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'مؤسسة الابتكار التجاري',
      email: '<EMAIL>',
      phone: '+966 12 555 0456',
      address: 'جدة، المملكة العربية السعودية',
      taxId: '300987654321003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'شركة الحلول الذكية',
      email: '<EMAIL>',
      phone: '+966 13 555 0789',
      address: 'الدمام، المملكة العربية السعودية',
      taxId: '300555666777003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];
  const customer = customers.find(c => c.id === parseInt(req.params.id));
  if (customer) {
    res.json(customer);
  } else {
    res.status(404).json({ error: 'العميل غير موجود' });
  }
});

// مسارات العملاء
app.get('/api/customers', (req, res) => {
  console.log('📥 GET /api/customers');
  const customers = [
    {
      id: 1,
      name: 'شركة التقنية المتطورة',
      email: '<EMAIL>',
      phone: '+966 11 555 0123',
      address: 'الرياض، المملكة العربية السعودية',
      taxId: '300123456789003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'مؤسسة الابتكار التجاري',
      email: '<EMAIL>',
      phone: '+966 12 555 0456',
      address: 'جدة، المملكة العربية السعودية',
      taxId: '300987654321003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'شركة الحلول الذكية',
      email: '<EMAIL>',
      phone: '+966 13 555 0789',
      address: 'الدمام، المملكة العربية السعودية',
      taxId: '300555666777003',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];
  res.json({ customers, total: customers.length });
});

// مسارات المصادقة (تجريبية)
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 POST /api/auth/login:', req.body);
  // مصادقة تجريبية - قبول أي بيانات
  const { email, password } = req.body;
  if (email && password) {
    res.json({
      user: { id: 1, email, name: 'مستخدم تجريبي' },
      token: 'fake-jwt-token-for-testing'
    });
  } else {
    res.status(400).json({ error: 'البريد الإلكتروني وكلمة المرور مطلوبان' });
  }
});

app.post('/api/auth/register', (req, res) => {
  console.log('📝 POST /api/auth/register:', req.body);
  // تسجيل تجريبي - قبول أي بيانات
  const { name, email, password } = req.body;
  if (name && email && password) {
    res.json({
      user: { id: 1, email, name },
      token: 'fake-jwt-token-for-testing'
    });
  } else {
    res.status(400).json({ error: 'جميع الحقول مطلوبة' });
  }
});

// معالج الأخطاء
app.use((err, req, res, next) => {
  console.error('❌ خطأ في الخادم:', err);
  res.status(500).json({ error: 'حدث خطأ في الخادم', message: err.message });
});

// معالج المسارات غير الموجودة
app.use((req, res) => {
  console.log(`❓ مسار غير موجود: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'المسار غير موجود', path: req.originalUrl });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 خادم API يعمل على المنفذ ${PORT}`);
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`⚙️ الإعدادات: http://localhost:${PORT}/api/settings`);
  console.log(`📦 المنتجات: http://localhost:${PORT}/api/products`);
  console.log(`📄 الفواتير: http://localhost:${PORT}/api/invoices`);
  console.log('✅ جاهز لاستقبال الطلبات');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إنهاء الخادم...');
  process.exit(0);
});
