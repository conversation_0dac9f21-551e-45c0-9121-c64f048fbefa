// backend/api-server.js
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5001;

// إعداد CORS والمعالجة
app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل خادم API...');

// بيانات تجريبية
let settings = {
  company: {
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    companyWebsite: '',
    taxId: '',
    currency: 'SAR',
    logoUrl: null
  },
  invoice: {
    invoicePrefix: 'INV',
    invoiceNumberLength: 6,
    defaultTaxRate: 15,
    defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    autoGenerateInvoiceNumber: true,
    showImagesInInvoice: true,
    allowPartialPayments: true,
    requireCustomerInfo: true,
    defaultDueDays: 30,
    invoiceFooter: '',
    invoiceNotes: '',
    display: {
      showCompanyLogo: true,
      showCompanyName: true,
      showCompanyAddress: true,
      showCompanyPhone: true,
      showCompanyEmail: true,
      showCompanyWebsite: true,
      showTaxId: true,
      showInvoiceNumber: true,
      showInvoiceDate: true,
      showDueDate: true,
      showPaymentTerms: true,
      showCustomerName: true,
      showCustomerAddress: true,
      showCustomerPhone: true,
      showCustomerEmail: true,
      showProductImages: true,
      showProductCode: true,
      showProductDescription: true,
      showQuantity: true,
      showUnitPrice: true,
      showDiscount: false,
      showTotalPrice: true,
      showItemNumbers: true,
      showSubtotal: true,
      showTaxAmount: true,
      showDiscountAmount: false,
      showTotalAmount: true,
      showNotes: true,
      showFooter: true,
      showSignature: false,
      showQRCode: false,
      showBankDetails: false,
      showPaymentInstructions: true
    }
  }
};

// بيانات تجريبية للمنتجات
const products = [
  { id: 1, name: 'منتج تجريبي 1', price: 100, stock: 5 },
  { id: 2, name: 'منتج تجريبي 2', price: 200, stock: 3 },
  { id: 3, name: 'منتج تجريبي 3', price: 150, stock: 8 }
];

// بيانات تجريبية للفواتير
const invoices = [
  { id: 1, invoiceNumber: 'INV-001', total: 500, status: 'paid' },
  { id: 2, invoiceNumber: 'INV-002', total: 750, status: 'pending' },
  { id: 3, invoiceNumber: 'INV-003', total: 300, status: 'draft' }
];

// مسارات API

// اختبار الخادم
app.get('/api', (req, res) => {
  console.log('✅ GET /api');
  res.json({ message: 'API Server is working!', timestamp: new Date().toISOString() });
});

// إعدادات الفواتير
app.get('/api/settings', (req, res) => {
  console.log('📥 GET /api/settings');
  res.json(settings);
});

app.post('/api/settings/company', (req, res) => {
  console.log('📤 POST /api/settings/company:', req.body);
  settings.company = { ...settings.company, ...req.body };
  res.json({ message: 'تم حفظ إعدادات الشركة بنجاح', settings: settings.company });
});

app.post('/api/settings/invoice', (req, res) => {
  console.log('📤 POST /api/settings/invoice:', req.body);
  settings.invoice = { ...settings.invoice, ...req.body, display: settings.invoice.display };
  res.json({ message: 'تم حفظ إعدادات الفواتير بنجاح', settings: settings.invoice });
});

app.post('/api/settings/invoice/display', (req, res) => {
  console.log('📤 POST /api/settings/invoice/display:', req.body);
  settings.invoice.display = { ...settings.invoice.display, ...req.body };
  res.json({ message: 'تم حفظ إعدادات العرض بنجاح', settings: settings.invoice.display });
});

// مسارات المنتجات
app.get('/api/products', (req, res) => {
  console.log('📥 GET /api/products');
  res.json({ products, total: products.length });
});

app.get('/api/products/low-stock', (req, res) => {
  console.log('📥 GET /api/products/low-stock');
  const lowStockProducts = products.filter(p => p.stock < 10);
  res.json({ products: lowStockProducts, total: lowStockProducts.length });
});

// مسارات الفواتير
app.get('/api/invoices', (req, res) => {
  console.log('📥 GET /api/invoices');
  res.json({
    invoices,
    total: invoices.length,
    pagination: { page: 1, limit: 10, totalPages: 1 }
  });
});

app.get('/api/invoices/stats', (req, res) => {
  console.log('📥 GET /api/invoices/stats');
  const stats = {
    total: invoices.length,
    paid: invoices.filter(i => i.status === 'paid').length,
    pending: invoices.filter(i => i.status === 'pending').length,
    draft: invoices.filter(i => i.status === 'draft').length,
    totalAmount: invoices.reduce((sum, i) => sum + i.total, 0),
    paidAmount: invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.total, 0)
  };
  res.json(stats);
});

// مسارات العملاء
app.get('/api/customers', (req, res) => {
  console.log('📥 GET /api/customers');
  const customers = [
    { id: 1, name: 'عميل تجريبي 1', email: '<EMAIL>' },
    { id: 2, name: 'عميل تجريبي 2', email: '<EMAIL>' }
  ];
  res.json({ customers, total: customers.length });
});

// معالج الأخطاء
app.use((err, req, res, next) => {
  console.error('❌ خطأ في الخادم:', err);
  res.status(500).json({ error: 'حدث خطأ في الخادم', message: err.message });
});

// معالج المسارات غير الموجودة
app.use((req, res) => {
  console.log(`❓ مسار غير موجود: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'المسار غير موجود', path: req.originalUrl });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 خادم API يعمل على المنفذ ${PORT}`);
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`⚙️ الإعدادات: http://localhost:${PORT}/api/settings`);
  console.log(`📦 المنتجات: http://localhost:${PORT}/api/products`);
  console.log(`📄 الفواتير: http://localhost:${PORT}/api/invoices`);
  console.log('✅ جاهز لاستقبال الطلبات');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إنهاء الخادم...');
  process.exit(0);
});
