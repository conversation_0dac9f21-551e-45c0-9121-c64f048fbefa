{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoices/page.js"], "sourcesContent": ["// frontend/app/dashboard/invoices/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { getInvoices, deleteInvoice, getInvoiceStats } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\n\nfunction InvoicesPage() {\n  const [invoices, setInvoices] = useState([]);\n  const [stats, setStats] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: 'all',\n    search: ''\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadInvoices();\n    loadStats();\n  }, [filters]);\n\n  const loadInvoices = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getInvoices(filters);\n      setInvoices(data.invoices);\n      setPagination(data.pagination);\n    } catch (err) {\n      setError('Failed to load invoices. Please try again later.');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStats = async () => {\n    try {\n      const statsData = await getInvoiceStats();\n      setStats(statsData);\n    } catch (err) {\n      console.error('Failed to load stats:', err);\n    }\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('Are you sure you want to delete this invoice?')) {\n      try {\n        await deleteInvoice(id);\n        setInvoices(invoices.filter(invoice => invoice.id !== id));\n        loadStats(); // إعادة تحميل الإحصائيات\n      } catch (err) {\n        setError('Failed to delete invoice. Please try again.');\n        console.error(err);\n      }\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n      page: 1 // إعادة تعيين الصفحة عند تغيير الفلاتر\n    }));\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      draft: 'bg-gray-100 text-gray-800',\n      sent: 'bg-blue-100 text-blue-800',\n      paid: 'bg-green-100 text-green-800',\n      overdue: 'bg-red-100 text-red-800',\n      cancelled: 'bg-red-100 text-red-800'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  const getPaymentStatusColor = (status) => {\n    const colors = {\n      unpaid: 'bg-red-100 text-red-800',\n      partial: 'bg-yellow-100 text-yellow-800',\n      paid: 'bg-green-100 text-green-800'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const formatDate = (date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  if (loading && invoices.length === 0) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-xl\">Loading invoices...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الفواتير</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/dashboard/invoices/create\" \n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-lg hover:shadow-xl\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                </svg>\n                إنشاء فاتورة جديدة\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Statistics Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">إجمالي الفواتير</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalInvoices || 0}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">الإيرادات المحصلة</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(stats.totalRevenue || 0)}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">في انتظار الدفع</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{formatCurrency(stats.pendingRevenue || 0)}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">متأخرة السداد</p>\n                  <p className=\"text-2xl font-bold text-red-600\">{stats.overdueInvoices || 0}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">البحث</label>\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={filters.search}\n                  onChange={(e) => handleFilterChange('search', e.target.value)}\n                  placeholder=\"رقم الفاتورة أو اسم العميل...\"\n                  className=\"w-full px-4 py-2 pl-10 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">الحالة</label>\n              <select\n                value={filters.status}\n                onChange={(e) => handleFilterChange('status', e.target.value)}\n                className=\"w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n              >\n                <option value=\"all\">جميع الحالات</option>\n                <option value=\"draft\">مسودة</option>\n                <option value=\"sent\">مرسلة</option>\n                <option value=\"paid\">مدفوعة</option>\n                <option value=\"overdue\">متأخرة</option>\n                <option value=\"cancelled\">ملغية</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">عدد النتائج</label>\n              <select\n                value={filters.limit}\n                onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}\n                className=\"w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n              >\n                <option value={10}>10</option>\n                <option value={25}>25</option>\n                <option value={50}>50</option>\n                <option value={100}>100</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Invoices Table */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">قائمة الفواتير</h3>\n          </div>\n\n          {invoices.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <p className=\"text-gray-600 text-lg mb-4\">لا توجد فواتير</p>\n              <Link \n                href=\"/dashboard/invoices/create\" \n                className=\"text-indigo-600 hover:text-indigo-800 font-medium\"\n              >\n                إنشاء أول فاتورة\n              </Link>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      رقم الفاتورة\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      العميل\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      المبلغ\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      الحالة\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      حالة الدفع\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      تاريخ الإنشاء\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      الإجراءات\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {invoices.map((invoice) => (\n                    <tr key={invoice.id} className=\"hover:bg-gray-50 transition-colors\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {invoice.invoiceNumber}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{invoice.customer.name}</div>\n                        <div className=\"text-sm text-gray-500\">{invoice.customer.email}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {formatCurrency(invoice.totalAmount)}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>\n                          {invoice.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(invoice.paymentStatus)}`}>\n                          {invoice.paymentStatus}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatDate(invoice.createdAt)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <div className=\"flex space-x-2\">\n                          <Link\n                            href={`/dashboard/invoices/${invoice.id}`}\n                            className=\"text-indigo-600 hover:text-indigo-900\"\n                          >\n                            عرض\n                          </Link>\n                          <Link\n                            href={`/dashboard/invoices/edit/${invoice.id}`}\n                            className=\"text-blue-600 hover:text-blue-900\"\n                          >\n                            تعديل\n                          </Link>\n                          <button\n                            onClick={() => handleDelete(invoice.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                            disabled={invoice.status === 'paid'}\n                          >\n                            حذف\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n\n          {/* Pagination */}\n          {pagination.pages > 1 && (\n            <div className=\"px-6 py-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm text-gray-700\">\n                  عرض {((pagination.page - 1) * pagination.limit) + 1} إلى {Math.min(pagination.page * pagination.limit, pagination.total)} من {pagination.total} نتيجة\n                </div>\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => handleFilterChange('page', pagination.page - 1)}\n                    disabled={pagination.page === 1}\n                    className=\"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    السابق\n                  </button>\n                  <span className=\"px-3 py-1 text-sm font-medium text-gray-700\">\n                    صفحة {pagination.page} من {pagination.pages}\n                  </span>\n                  <button\n                    onClick={() => handleFilterChange('page', pagination.page + 1)}\n                    disabled={pagination.page === pagination.pages}\n                    className=\"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    التالي\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(InvoicesPage);\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AAG1C;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;YACA;QACF;iCAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE;YAC/B,YAAY,KAAK,QAAQ;YACzB,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,6GAAA,CAAA,kBAAe,AAAD;YACtC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,kDAAkD;YACnE,IAAI;gBACF,MAAM,CAAA,GAAA,6GAAA,CAAA,gBAAa,AAAD,EAAE;gBACpB,YAAY,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;gBACtD,aAAa,yBAAyB;YACxC,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAK;QAC/B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;gBACP,MAAM,EAAE,uCAAuC;YACjD,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,OAAO;YACP,MAAM;YACN,MAAM;YACN,SAAS;YACT,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,SAAS;YACb,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;QACpC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMhF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAqC,eAAe,MAAM,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM/F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAsC,eAAe,MAAM,cAAc,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMlG,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAmC,MAAM,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO,QAAQ,MAAM;oDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7E,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAI9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,KAAK;4CACpB,UAAU,CAAC,IAAM,mBAAmB,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;4CACpE,WAAU;;8DAEV,6LAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,6LAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,6LAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,6LAAC;oDAAO,OAAO;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAM3B,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;kCAOzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;4BAGrD,SAAS,MAAM,KAAK,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAuC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9F,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;kDAEvE,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,6LAAC;4CAAM,WAAU;sDACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAAoB,WAAU;;sEAC7B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,aAAa;;;;;;;;;;;sEAG1B,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;8EAAyB,QAAQ,QAAQ,CAAC,IAAI;;;;;;8EAC7D,6LAAC;oEAAI,WAAU;8EAAyB,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sEAEhE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EACZ,eAAe,QAAQ,WAAW;;;;;;;;;;;sEAGvC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,QAAQ,MAAM,GAAG;0EAC1G,QAAQ,MAAM;;;;;;;;;;;sEAGnB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EAAE,sBAAsB,QAAQ,aAAa,GAAG;0EACxH,QAAQ,aAAa;;;;;;;;;;;sEAG1B,6LAAC;4DAAG,WAAU;sEACX,WAAW,QAAQ,SAAS;;;;;;sEAE/B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,oBAAoB,EAAE,QAAQ,EAAE,EAAE;wEACzC,WAAU;kFACX;;;;;;kFAGD,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE,EAAE;wEAC9C,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,aAAa,QAAQ,EAAE;wEACtC,WAAU;wEACV,UAAU,QAAQ,MAAM,KAAK;kFAC9B;;;;;;;;;;;;;;;;;;mDA9CE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;4BA2D5B,WAAW,KAAK,GAAG,mBAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC/B,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;gDAAE;gDAAM,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;gDAAE;gDAAK,WAAW,KAAK;gDAAC;;;;;;;sDAEjJ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,mBAAmB,QAAQ,WAAW,IAAI,GAAG;oDAC5D,UAAU,WAAW,IAAI,KAAK;oDAC9B,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAK,WAAU;;wDAA8C;wDACtD,WAAW,IAAI;wDAAC;wDAAK,WAAW,KAAK;;;;;;;8DAE7C,6LAAC;oDACC,SAAS,IAAM,mBAAmB,QAAQ,WAAW,IAAI,GAAG;oDAC5D,UAAU,WAAW,IAAI,KAAK,WAAW,KAAK;oDAC9C,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA9ZS;KAAA;6CAgaM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}