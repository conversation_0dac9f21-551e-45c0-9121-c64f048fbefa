// frontend/components/invoice-templates/InvoiceRenderer.js
'use client';

import React from 'react';
import ClassicTemplate from './ClassicTemplate';
import ModernTemplate from './ModernTemplate';
import ElegantTemplate from './ElegantTemplate';
import MinimalTemplate from './MinimalTemplate';

const templateComponents = {
  classic: ClassicTemplate,
  modern: ModernTemplate,
  elegant: ElegantTemplate,
  minimal: MinimalTemplate
};

export default function InvoiceRenderer({ invoice, templateId, preview = false }) {
  // Get template from props or localStorage
  const selectedTemplate = templateId || localStorage.getItem('selectedInvoiceTemplate') || 'classic';
  
  // Get the template component
  const TemplateComponent = templateComponents[selectedTemplate] || ClassicTemplate;
  
  if (!TemplateComponent) {
    return (
      <div className="bg-white p-8 text-center">
        <p className="text-red-600">خطأ: القالب المحدد غير موجود</p>
      </div>
    );
  }

  return (
    <div className="invoice-container">
      <TemplateComponent invoice={invoice} preview={preview} />
    </div>
  );
}

// Export template components for direct use
export {
  ClassicTemplate,
  ModernTemplate,
  ElegantTemplate,
  MinimalTemplate,
  templateComponents
};
