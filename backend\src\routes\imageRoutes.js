// backend/src/routes/imageRoutes.js
const express = require('express');
const router = express.Router();
const { protect } = require('../middlewares/authMiddleware');
const { upload } = require('../middlewares/uploadMiddleware');
const {
  uploadProductImage,
  getProductImages,
  setMainImage,
  reorderImages,
  deleteProductImage,
} = require('../controllers/imageController');

// رفع صورة للمنتج
router.post('/products/:id/images', protect, upload.single('image'), uploadProductImage);

// الحصول على صور المنتج
router.get('/products/:id/images', protect, getProductImages);

// تحديد الصورة الرئيسية
router.put('/images/:imageId/main', protect, setMainImage);

// تحديث ترتيب الصور
router.put('/images/reorder', protect, reorderImages);

// حذف صورة
router.delete('/images/:imageId', protect, deleteProductImage);

module.exports = router;
