{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العرض التوضيحي',\n      href: '/dashboard/demo',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,6LAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,6LAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAtQwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/apiSettingsService.js"], "sourcesContent": ["// frontend/lib/apiSettingsService.js\n\n// خدمة إدارة الإعدادات عبر API\nclass ApiSettingsService {\n  constructor() {\n    this.apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';\n    this.fallbackService = null; // للعودة إلى localStorage في حالة فشل API\n  }\n\n  // تحميل الإعدادات من API\n  async loadSettings() {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const settings = await response.json();\n\n      // حفظ نسخة احتياطية في localStorage\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(settings));\n\n      return settings;\n    } catch (error) {\n      console.error('Error loading settings from API:', error);\n\n      // محاولة تحميل من localStorage كبديل\n      try {\n        const backup = localStorage.getItem('invoiceAppSettings_backup');\n        if (backup) {\n          console.log('Using backup settings from localStorage');\n          return JSON.parse(backup);\n        }\n      } catch (localError) {\n        console.error('Error loading backup settings:', localError);\n      }\n\n      // إرجاع إعدادات افتراضية\n      return this.getDefaultSettings();\n    }\n  }\n\n  // حفظ إعدادات الشركة\n  async saveCompanySettings(companySettings) {\n    try {\n      // معالجة رفع الصورة إذا كانت موجودة\n      if (companySettings.logoFile) {\n        const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);\n        companySettings.logoUrl = logoUrl;\n        delete companySettings.logoFile;\n      }\n\n      const response = await fetch(`${this.apiUrl}/settings/company`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(companySettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      currentSettings.company = { ...currentSettings.company, ...companySettings };\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n\n      return result;\n    } catch (error) {\n      console.error('Error saving company settings:', error);\n      throw error;\n    }\n  }\n\n  // حفظ إعدادات الفواتير العامة\n  async saveInvoiceSettings(invoiceSettings) {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings/invoice`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(invoiceSettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      currentSettings.invoice = { ...currentSettings.invoice, ...invoiceSettings };\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n\n      return result;\n    } catch (error) {\n      console.error('Error saving invoice settings:', error);\n      throw error;\n    }\n  }\n\n  // حفظ إعدادات عرض الفاتورة\n  async saveInvoiceDisplaySettings(displaySettings) {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings/invoice/display`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(displaySettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      if (!currentSettings.invoice) currentSettings.invoice = {};\n      currentSettings.invoice.display = displaySettings;\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n\n      return result;\n    } catch (error) {\n      console.error('Error saving display settings:', error);\n      throw error;\n    }\n  }\n\n  // رفع شعار الشركة\n  async uploadCompanyLogo(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const logoUrl = e.target.result;\n        // حفظ في localStorage مؤقتاً\n        localStorage.setItem('companyLogo', logoUrl);\n        resolve(logoUrl);\n      };\n      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));\n      reader.readAsDataURL(file);\n    });\n  }\n\n  // الحصول على شعار الشركة\n  getCompanyLogo() {\n    return localStorage.getItem('companyLogo');\n  }\n\n  // الحصول على إعدادات الشركة\n  async getCompanySettings() {\n    const settings = await this.loadSettings();\n    return settings.company || {};\n  }\n\n  // الحصول على إعدادات الفواتير\n  async getInvoiceSettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice || {};\n  }\n\n  // الحصول على إعدادات عرض الفاتورة\n  async getInvoiceDisplaySettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice?.display || {};\n  }\n\n  // الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      company: {\n        companyName: '',\n        companyAddress: '',\n        companyPhone: '',\n        companyEmail: '',\n        companyWebsite: '', // اختياري\n        taxId: '',\n        currency: 'SAR',\n        logoUrl: null\n      },\n      invoice: {\n        invoicePrefix: 'INV',\n        invoiceNumberLength: 6,\n        defaultTaxRate: 15,\n        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n        autoGenerateInvoiceNumber: true,\n        showImagesInInvoice: true,\n        allowPartialPayments: true,\n        requireCustomerInfo: true,\n        defaultDueDays: 30,\n        invoiceFooter: '',\n        invoiceNotes: '',\n        display: {\n          // رأس الفاتورة\n          showCompanyLogo: true,\n          showCompanyName: true,\n          showCompanyAddress: true,\n          showCompanyPhone: true,\n          showCompanyEmail: true,\n          showCompanyWebsite: true,\n          showTaxId: true,\n\n          // معلومات الفاتورة\n          showInvoiceNumber: true,\n          showInvoiceDate: true,\n          showDueDate: true,\n          showPaymentTerms: true,\n\n          // معلومات العميل\n          showCustomerName: true,\n          showCustomerAddress: true,\n          showCustomerPhone: true,\n          showCustomerEmail: true,\n\n          // عناصر الجدول\n          showProductImages: true,\n          showProductCode: true,\n          showProductDescription: true,\n          showQuantity: true,\n          showUnitPrice: true,\n          showDiscount: false,\n          showTotalPrice: true,\n          showItemNumbers: true,\n\n          // المجاميع\n          showSubtotal: true,\n          showTaxAmount: true,\n          showDiscountAmount: false,\n          showTotalAmount: true,\n\n          // التذييل\n          showNotes: true,\n          showFooter: true,\n          showSignature: false,\n          showQRCode: false,\n          showBankDetails: false,\n          showPaymentInstructions: true\n        }\n      }\n    };\n  }\n\n  // فحص حالة الاتصال بـ API\n  async checkApiConnection() {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings`);\n      return response.ok;\n    } catch (error) {\n      return false;\n    }\n  }\n}\n\n// إنشاء مثيل واحد للخدمة\nconst apiSettingsService = new ApiSettingsService();\n\nexport default apiSettingsService;\n"], "names": [], "mappings": "AAAA,qCAAqC;AAErC,+BAA+B;;;;AAGb;AAFlB,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;QACjD,IAAI,CAAC,eAAe,GAAG,MAAM,0CAA0C;IACzE;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,WAAW,MAAM,SAAS,IAAI;YAEpC,oCAAoC;YACpC,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,qCAAqC;YACrC,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC;oBACZ,OAAO,KAAK,KAAK,CAAC;gBACpB;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;YAClD;YAEA,yBAAyB;YACzB,OAAO,IAAI,CAAC,kBAAkB;QAChC;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,eAAe,EAAE;QACzC,IAAI;YACF,oCAAoC;YACpC,IAAI,gBAAgB,QAAQ,EAAE;gBAC5B,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,QAAQ;gBACrE,gBAAgB,OAAO,GAAG;gBAC1B,OAAO,gBAAgB,QAAQ;YACjC;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,gBAAgB,OAAO,GAAG;gBAAE,GAAG,gBAAgB,OAAO;gBAAE,GAAG,eAAe;YAAC;YAC3E,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,oBAAoB,eAAe,EAAE;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,gBAAgB,OAAO,GAAG;gBAAE,GAAG,gBAAgB,OAAO;gBAAE,GAAG,eAAe;YAAC;YAC3E,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,2BAA2B,eAAe,EAAE;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,IAAI,CAAC,gBAAgB,OAAO,EAAE,gBAAgB,OAAO,GAAG,CAAC;YACzD,gBAAgB,OAAO,CAAC,OAAO,GAAG;YAClC,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,CAAC,MAAM;gBAC/B,6BAA6B;gBAC7B,aAAa,OAAO,CAAC,eAAe;gBACpC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,yBAAyB;IACzB,iBAAiB;QACf,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,IAAI,CAAC;IAC9B;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,IAAI,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,4BAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,EAAE,WAAW,CAAC;IACvC;IAEA,uBAAuB;IACvB,qBAAqB;QACnB,OAAO;YACL,SAAS;gBACP,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;gBACV,SAAS;YACX;YACA,SAAS;gBACP,eAAe;gBACf,qBAAqB;gBACrB,gBAAgB;gBAChB,qBAAqB;gBACrB,2BAA2B;gBAC3B,qBAAqB;gBACrB,sBAAsB;gBACtB,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe;gBACf,cAAc;gBACd,SAAS;oBACP,eAAe;oBACf,iBAAiB;oBACjB,iBAAiB;oBACjB,oBAAoB;oBACpB,kBAAkB;oBAClB,kBAAkB;oBAClB,oBAAoB;oBACpB,WAAW;oBAEX,mBAAmB;oBACnB,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;oBACb,kBAAkB;oBAElB,iBAAiB;oBACjB,kBAAkB;oBAClB,qBAAqB;oBACrB,mBAAmB;oBACnB,mBAAmB;oBAEnB,eAAe;oBACf,mBAAmB;oBACnB,iBAAiB;oBACjB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,iBAAiB;oBAEjB,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,oBAAoB;oBACpB,iBAAiB;oBAEjB,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,YAAY;oBACZ,iBAAiB;oBACjB,yBAAyB;gBAC3B;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAEA,yBAAyB;AACzB,MAAM,qBAAqB,IAAI;uCAEhB", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ClassicTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // بيانات العينة للمعاينة فقط\n  const sampleData = preview ? {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة',\n    companyPhone: '+966 50 123 4567',\n    companyEmail: '<EMAIL>',\n    companyWebsite: 'www.company.com',\n    taxId: '*********'\n  } : invoice;\n\n  const data = sampleData;\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header with Teal Design */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              <p className=\"text-teal-100\">{data.companyDetails}</p>\n            </div>\n\n            {/* Logo Area */}\n            <div className=\"bg-white p-4 rounded-lg shadow-md\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                <div className=\"text-white font-bold text-xl\">شعار</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">رقم الفاتورة:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">اسم العميل:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">التاريخ:</span>\n          </div>\n\n          <div>{data.invoiceNumber}</div>\n          <div>{data.customerName}</div>\n          <div>{data.date}</div>\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"bg-yellow-100\">\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">البيان</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Totals */}\n      <div className=\"flex justify-between items-end mb-8\">\n        <div className=\"text-right\">\n          <p className=\"text-lg font-semibold mb-4\">{data.notes}</p>\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">السعر</span>\n              <span>{(Number(data.subtotal) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">الضريبة</span>\n              <span>{(Number(data.tax) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"border-t border-gray-300 pt-2\">\n              <div className=\"flex justify-between font-bold text-lg\">\n                <span>الإجمالي</span>\n                <span>{(Number(data.total) || 0).toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"flex justify-between items-end\">\n        <div>\n          <h4 className=\"font-semibold mb-2\">ملاحظات</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• البضاعة المباعة لا ترد ولا تستبدل</li>\n            <li>• التأكد من استلام جميع بنود الفاتورة</li>\n          </ul>\n        </div>\n\n        <div className=\"text-center\">\n          <h4 className=\"font-semibold mb-4\">توقيع البائع</h4>\n          <div className=\"w-32 border-b-2 border-gray-400 mb-2\"></div>\n        </div>\n      </div>\n\n      {/* Contact Info */}\n      <div className=\"mt-8 pt-4 border-t border-gray-300\">\n        <div className=\"flex justify-between text-sm text-gray-600\">\n          <div>\n            <p>+123-456-7890</p>\n            <p>www.reallygreatsite.com</p>\n            <p><EMAIL></p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"w-32 h-8 bg-gray-800 flex items-center justify-center\">\n              <div className=\"text-white text-xs\">|||||||||||||||||||||||</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IACxF,6BAA6B;IAC7B,MAAM,aAAa,UAAU;QAC3B,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;IACT,IAAI;IAEJ,MAAM,OAAO;IAEb,qBACE,6LAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAiB,KAAK,cAAc;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAGlC,6LAAC;0CAAK,KAAK,aAAa;;;;;;0CACxB,6LAAC;0CAAK,KAAK,YAAY;;;;;;0CACvB,6LAAC;0CAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAGxE,6LAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;0DACxE,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;0DAC3E,6LAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCALxD,KAAK,EAAE;;;;;gCAUjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCALpC,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAa7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA8B,KAAK,KAAK;;;;;;;;;;;kCAGvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;;;;;;;sCAEL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;KAvKwB", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/EnhancedClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/EnhancedClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function EnhancedClassicTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // استخدام البيانات الحقيقية فقط - لا توجد بيانات افتراضية\n  const data = invoice || {};\n\n  // استخدام إعدادات العرض من البيانات إذا لم تُمرر كخاصية منفصلة\n  const settings = displaySettings || data.displaySettings || {};\n\n  // إذا لم تكن هناك بيانات، عرض رسالة\n  if (!data.companyName && !preview) {\n    return (\n      <div className=\"bg-white p-8 text-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"flex flex-col items-center justify-center h-full\">\n          <svg className=\"w-16 h-16 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">لا توجد بيانات للعرض</h3>\n          <p className=\"text-gray-500 text-center max-w-md\">\n            يرجى إدخال معلومات الشركة في الإعدادات وإنشاء فاتورة جديدة لعرض البيانات الحقيقية.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              {settings.showCompanyName !== false && data.companyName && (\n                <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              )}\n              {displaySettings.showCompanyAddress !== false && data.companyAddress && (\n                <p className=\"text-teal-100 text-sm\">{data.companyAddress}</p>\n              )}\n              {displaySettings.showCompanyPhone !== false && data.companyPhone && (\n                <p className=\"text-teal-100 text-sm\">📞 {data.companyPhone}</p>\n              )}\n              {displaySettings.showCompanyEmail !== false && data.companyEmail && (\n                <p className=\"text-teal-100 text-sm\">📧 {data.companyEmail}</p>\n              )}\n              {displaySettings.showCompanyWebsite !== false && data.companyWebsite && (\n                <p className=\"text-teal-100 text-sm\">🌐 {data.companyWebsite}</p>\n              )}\n              {displaySettings.showTaxId !== false && data.taxId && (\n                <p className=\"text-teal-100 text-sm\">الرقم الضريبي: {data.taxId}</p>\n              )}\n            </div>\n\n            {/* Logo Area */}\n            {displaySettings.showCompanyLogo !== false && (\n              <div className=\"bg-white p-4 rounded-lg shadow-md\">\n                {data.logoUrl ? (\n                  <img\n                    src={data.logoUrl}\n                    alt=\"شعار الشركة\"\n                    className=\"w-16 h-16 object-contain rounded-lg\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-white font-bold text-sm\">شعار</div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title and Info */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          {displaySettings.showInvoiceNumber !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">رقم الفاتورة: {data.invoiceNumber}</span>\n            </div>\n          )}\n          {displaySettings.showCustomerName !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">اسم العميل: {data.customerName}</span>\n            </div>\n          )}\n          {displaySettings.showInvoiceDate !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">التاريخ: {data.date}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Customer Information */}\n      <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n        <h3 className=\"font-semibold text-gray-800 mb-3\">معلومات العميل:</h3>\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          {displaySettings.showCustomerName !== false && (\n            <div><span className=\"font-semibold\">الاسم:</span> {data.customerName}</div>\n          )}\n          {displaySettings.showCustomerAddress !== false && data.customerAddress && (\n            <div><span className=\"font-semibold\">العنوان:</span> {data.customerAddress}</div>\n          )}\n          {displaySettings.showCustomerPhone !== false && data.customerPhone && (\n            <div><span className=\"font-semibold\">الهاتف:</span> {data.customerPhone}</div>\n          )}\n          {displaySettings.showCustomerEmail !== false && data.customerEmail && (\n            <div><span className=\"font-semibold\">البريد:</span> {data.customerEmail}</div>\n          )}\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-6\">\n        <table className=\"w-full border-collapse border border-gray-300\">\n          <thead className=\"bg-teal-600 text-white\">\n            <tr>\n              {displaySettings.showTotalPrice !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              )}\n              {displaySettings.showUnitPrice !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              )}\n              {displaySettings.showQuantity !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              )}\n              {displaySettings.showProductDescription !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الوصف</th>\n              )}\n              {displaySettings.showProductCode !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكود</th>\n              )}\n              {displaySettings.showItemNumbers !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n              )}\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                {displaySettings.showTotalPrice !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                )}\n                {displaySettings.showUnitPrice !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                )}\n                {displaySettings.showQuantity !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                )}\n                {displaySettings.showProductDescription !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                )}\n                {displaySettings.showProductCode !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.code || ''}</td>\n                )}\n                {displaySettings.showItemNumbers !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n                )}\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                {displaySettings.showTotalPrice !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showUnitPrice !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showQuantity !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showProductDescription !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showProductCode !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showItemNumbers !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Summary and Notes */}\n      <div className=\"flex justify-between items-start mb-6\">\n        <div className=\"w-1/2 pr-4\">\n          {displaySettings.showNotes !== false && data.notes && (\n            <div className=\"mb-4\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">ملاحظات:</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n            </div>\n          )}\n\n          {displaySettings.showPaymentTerms !== false && data.paymentTerms && (\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h4>\n              <p className=\"text-gray-700 text-sm\">{data.paymentTerms}</p>\n            </div>\n          )}\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            {displaySettings.showSubtotal !== false && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">المجموع الفرعي:</span>\n                <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n            )}\n            {displaySettings.showTaxAmount !== false && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">الضريبة:</span>\n                <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n            )}\n            {displaySettings.showTotalAmount !== false && (\n              <div className=\"border-t border-gray-300 pt-2\">\n                <div className=\"flex justify-between font-bold text-lg\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      {displaySettings.showFooter !== false && data.invoiceFooter && (\n        <div className=\"text-center border-t border-gray-300 pt-4\">\n          <p className=\"text-gray-600\">{data.invoiceFooter}</p>\n        </div>\n      )}\n\n      {/* Signature Area */}\n      {displaySettings.showSignature === true && (\n        <div className=\"mt-8 flex justify-end\">\n          <div className=\"text-center\">\n            <div className=\"w-32 h-16 border-b border-gray-400 mb-2\"></div>\n            <p className=\"text-sm text-gray-600\">التوقيع</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AAGnE;AAFA;;;AAIe,SAAS,wBAAwB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IAChG,0DAA0D;IAC1D,MAAM,OAAO,WAAW,CAAC;IAEzB,+DAA+D;IAC/D,MAAM,WAAW,mBAAmB,KAAK,eAAe,IAAI,CAAC;IAE7D,oCAAoC;IACpC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,SAAS;QACjC,qBACE,6LAAC;YAAI,WAAU;YAA2B,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACpF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAM1D;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,SAAS,eAAe,KAAK,SAAS,KAAK,WAAW,kBACrD,6LAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;wCAE1D,gBAAgB,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAClE,6LAAC;4CAAE,WAAU;sDAAyB,KAAK,cAAc;;;;;;wCAE1D,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,gBAAgB,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAClE,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,cAAc;;;;;;;wCAE7D,gBAAgB,SAAS,KAAK,SAAS,KAAK,KAAK,kBAChD,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,KAAK,KAAK;;;;;;;;;;;;;gCAKlE,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,OAAO,iBACX,6LAAC;wCACC,KAAK,KAAK,OAAO;wCACjB,KAAI;wCACJ,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,iBAAiB,KAAK,uBACrC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAe,KAAK,aAAa;;;;;;;;;;;;4BAGpE,gBAAgB,gBAAgB,KAAK,uBACpC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAa,KAAK,YAAY;;;;;;;;;;;;4BAGjE,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAU,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,gBAAgB,KAAK,uBACpC,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;oCAAE,KAAK,YAAY;;;;;;;4BAEtE,gBAAgB,mBAAmB,KAAK,SAAS,KAAK,eAAe,kBACpE,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAe;oCAAE,KAAK,eAAe;;;;;;;4BAE3E,gBAAgB,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBAChE,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;4BAExE,gBAAgB,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBAChE,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;oCACE,gBAAgB,cAAc,KAAK,uBAClC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,aAAa,KAAK,uBACjC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,YAAY,KAAK,uBAChC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,sBAAsB,KAAK,uBAC1C,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAI1E,6LAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;;4CACE,gBAAgB,cAAc,KAAK,uBAClC,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;4CAE1F,gBAAgB,aAAa,KAAK,uBACjC,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;4CAE1F,gBAAgB,YAAY,KAAK,uBAChC,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;4CAEzE,gBAAgB,sBAAsB,KAAK,uBAC1C,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;4CAE5E,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,IAAI,IAAI;;;;;;4CAErE,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCAjB1D,KAAK,EAAE;;;;;gCAuBjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;;4CACE,gBAAgB,cAAc,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACvF,gBAAgB,aAAa,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACtF,gBAAgB,YAAY,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACrF,gBAAgB,sBAAsB,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;4CAC/F,gBAAgB,eAAe,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACxF,gBAAgB,eAAe,KAAK,uBAAS,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCANlF,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAc7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,SAAS,KAAK,SAAS,KAAK,KAAK,kBAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;4BAI3C,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,YAAY;;;;;;;;;;;;;;;;;;kCAK7D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,gBAAgB,YAAY,KAAK,uBAChC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;gDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;gCAGlD,gBAAgB,aAAa,KAAK,uBACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;gDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;gCAG7C,gBAAgB,eAAe,KAAK,uBACnC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASvD,gBAAgB,UAAU,KAAK,SAAS,KAAK,aAAa,kBACzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAiB,KAAK,aAAa;;;;;;;;;;;YAKnD,gBAAgB,aAAa,KAAK,sBACjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAMjD;KAlPwB", "debugId": null}}, {"offset": {"line": 2603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/RealDataTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/RealDataTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function RealDataTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // استخدام البيانات الحقيقية فقط - لا توجد بيانات افتراضية\n  const data = invoice || {};\n  \n  // استخدام إعدادات العرض من البيانات إذا لم تُمرر كخاصية منفصلة\n  const settings = displaySettings || data.displaySettings || {};\n\n  // إذا لم تكن هناك بيانات، عرض رسالة\n  if (!data.companyName && !preview) {\n    return (\n      <div className=\"bg-white p-8 text-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"flex flex-col items-center justify-center h-full\">\n          <svg className=\"w-16 h-16 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">لا توجد بيانات للعرض</h3>\n          <p className=\"text-gray-500 text-center max-w-md\">\n            يرجى إدخال معلومات الشركة في الإعدادات وإنشاء فاتورة جديدة لعرض البيانات الحقيقية.\n          </p>\n          <div className=\"mt-4\">\n            <a href=\"/dashboard/settings\" className=\"text-blue-500 hover:text-blue-700 underline\">\n              انتقل إلى الإعدادات\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              {settings.showCompanyName !== false && data.companyName && (\n                <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              )}\n              {settings.showCompanyAddress !== false && data.companyAddress && (\n                <p className=\"text-teal-100 text-sm\">{data.companyAddress}</p>\n              )}\n              {settings.showCompanyPhone !== false && data.companyPhone && (\n                <p className=\"text-teal-100 text-sm\">📞 {data.companyPhone}</p>\n              )}\n              {settings.showCompanyEmail !== false && data.companyEmail && (\n                <p className=\"text-teal-100 text-sm\">📧 {data.companyEmail}</p>\n              )}\n              {settings.showCompanyWebsite !== false && data.companyWebsite && (\n                <p className=\"text-teal-100 text-sm\">🌐 {data.companyWebsite}</p>\n              )}\n              {settings.showTaxId !== false && data.taxId && (\n                <p className=\"text-teal-100 text-sm\">الرقم الضريبي: {data.taxId}</p>\n              )}\n            </div>\n\n            {/* Logo Area */}\n            {settings.showCompanyLogo !== false && (\n              <div className=\"bg-white p-4 rounded-lg shadow-md\">\n                {data.logoUrl ? (\n                  <img\n                    src={data.logoUrl}\n                    alt=\"شعار الشركة\"\n                    className=\"w-16 h-16 object-contain rounded-lg\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-white font-bold text-sm\">شعار</div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title and Info */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          {settings.showInvoiceNumber !== false && data.invoiceNumber && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">رقم الفاتورة: {data.invoiceNumber}</span>\n            </div>\n          )}\n          {settings.showCustomerName !== false && data.customerName && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">اسم العميل: {data.customerName}</span>\n            </div>\n          )}\n          {settings.showInvoiceDate !== false && data.date && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">التاريخ: {data.date}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Customer Information */}\n      {(settings.showCustomerName !== false || settings.showCustomerAddress !== false || \n        settings.showCustomerPhone !== false || settings.showCustomerEmail !== false) && (\n        <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n          <h3 className=\"font-semibold text-gray-800 mb-3\">معلومات العميل:</h3>\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            {settings.showCustomerName !== false && data.customerName && (\n              <div><span className=\"font-semibold\">الاسم:</span> {data.customerName}</div>\n            )}\n            {settings.showCustomerAddress !== false && data.customerAddress && (\n              <div><span className=\"font-semibold\">العنوان:</span> {data.customerAddress}</div>\n            )}\n            {settings.showCustomerPhone !== false && data.customerPhone && (\n              <div><span className=\"font-semibold\">الهاتف:</span> {data.customerPhone}</div>\n            )}\n            {settings.showCustomerEmail !== false && data.customerEmail && (\n              <div><span className=\"font-semibold\">البريد:</span> {data.customerEmail}</div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Items Table */}\n      {data.items && data.items.length > 0 && (\n        <div className=\"mb-6\">\n          <table className=\"w-full border-collapse border border-gray-300\">\n            <thead className=\"bg-teal-600 text-white\">\n              <tr>\n                {settings.showItemNumbers !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n                )}\n                {settings.showProductCode !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكود</th>\n                )}\n                {settings.showProductDescription !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الوصف</th>\n                )}\n                {settings.showQuantity !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n                )}\n                {settings.showUnitPrice !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n                )}\n                {settings.showTotalPrice !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n                )}\n              </tr>\n            </thead>\n            <tbody>\n              {data.items.map((item, index) => (\n                <tr key={item.id || index}>\n                  {settings.showItemNumbers !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n                  )}\n                  {settings.showProductCode !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.code || ''}</td>\n                  )}\n                  {settings.showProductDescription !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                  )}\n                  {settings.showQuantity !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                  )}\n                  {settings.showUnitPrice !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                  )}\n                  {settings.showTotalPrice !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                  )}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n\n      {/* Summary and Notes */}\n      <div className=\"flex justify-between items-start mb-6\">\n        <div className=\"w-1/2 pr-4\">\n          {settings.showNotes !== false && data.notes && (\n            <div className=\"mb-4\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">ملاحظات:</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n            </div>\n          )}\n          \n          {settings.showPaymentTerms !== false && data.paymentTerms && (\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h4>\n              <p className=\"text-gray-700 text-sm\">{data.paymentTerms}</p>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            {settings.showSubtotal !== false && data.subtotal !== undefined && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">المجموع الفرعي:</span>\n                <span>{(Number(data.subtotal) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n              </div>\n            )}\n            {settings.showTaxAmount !== false && data.tax !== undefined && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">الضريبة:</span>\n                <span>{(Number(data.tax) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n              </div>\n            )}\n            {settings.showTotalAmount !== false && data.total !== undefined && (\n              <div className=\"border-t border-gray-300 pt-2\">\n                <div className=\"flex justify-between font-bold text-lg\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      {settings.showFooter !== false && data.invoiceFooter && (\n        <div className=\"text-center border-t border-gray-300 pt-4\">\n          <p className=\"text-gray-600\">{data.invoiceFooter}</p>\n        </div>\n      )}\n\n      {/* Signature Area */}\n      {settings.showSignature === true && (\n        <div className=\"mt-8 flex justify-end\">\n          <div className=\"text-center\">\n            <div className=\"w-32 h-16 border-b border-gray-400 mb-2\"></div>\n            <p className=\"text-sm text-gray-600\">التوقيع</p>\n          </div>\n        </div>\n      )}\n\n      {/* QR Code Area */}\n      {settings.showQRCode === true && (\n        <div className=\"mt-4 flex justify-center\">\n          <div className=\"w-24 h-24 border border-gray-300 rounded-lg flex items-center justify-center\">\n            <span className=\"text-xs text-gray-500\">QR Code</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;AAG5D;AAFA;;;AAIe,SAAS,iBAAiB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IACzF,0DAA0D;IAC1D,MAAM,OAAO,WAAW,CAAC;IAEzB,+DAA+D;IAC/D,MAAM,WAAW,mBAAmB,KAAK,eAAe,IAAI,CAAC;IAE7D,oCAAoC;IACpC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,SAAS;QACjC,qBACE,6LAAC;YAAI,WAAU;YAA2B,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACpF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,MAAK;4BAAsB,WAAU;sCAA8C;;;;;;;;;;;;;;;;;;;;;;IAOhG;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,SAAS,eAAe,KAAK,SAAS,KAAK,WAAW,kBACrD,6LAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;wCAE1D,SAAS,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAC3D,6LAAC;4CAAE,WAAU;sDAAyB,KAAK,cAAc;;;;;;wCAE1D,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,SAAS,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAC3D,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,cAAc;;;;;;;wCAE7D,SAAS,SAAS,KAAK,SAAS,KAAK,KAAK,kBACzC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,KAAK,KAAK;;;;;;;;;;;;;gCAKlE,SAAS,eAAe,KAAK,uBAC5B,6LAAC;oCAAI,WAAU;8CACZ,KAAK,OAAO,iBACX,6LAAC;wCACC,KAAK,KAAK,OAAO;wCACjB,KAAI;wCACJ,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAe,KAAK,aAAa;;;;;;;;;;;;4BAGpE,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAa,KAAK,YAAY;;;;;;;;;;;;4BAGjE,SAAS,eAAe,KAAK,SAAS,KAAK,IAAI,kBAC9C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAgB;wCAAU,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAO1D,CAAC,SAAS,gBAAgB,KAAK,SAAS,SAAS,mBAAmB,KAAK,SACxE,SAAS,iBAAiB,KAAK,SAAS,SAAS,iBAAiB,KAAK,KAAK,mBAC5E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;oCAAE,KAAK,YAAY;;;;;;;4BAEtE,SAAS,mBAAmB,KAAK,SAAS,KAAK,eAAe,kBAC7D,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAe;oCAAE,KAAK,eAAe;;;;;;;4BAE3E,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;4BAExE,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,6LAAC;;kDAAI,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;YAO9E,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;oCACE,SAAS,eAAe,KAAK,uBAC5B,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,eAAe,KAAK,uBAC5B,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,sBAAsB,KAAK,uBACnC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,YAAY,KAAK,uBACzB,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,aAAa,KAAK,uBAC1B,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,cAAc,KAAK,uBAC3B,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAI1E,6LAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;;wCACE,SAAS,eAAe,KAAK,uBAC5B,6LAAC;4CAAG,WAAU;sDAAyC,QAAQ;;;;;;wCAEhE,SAAS,eAAe,KAAK,uBAC5B,6LAAC;4CAAG,WAAU;sDAAyC,KAAK,IAAI,IAAI;;;;;;wCAErE,SAAS,sBAAsB,KAAK,uBACnC,6LAAC;4CAAG,WAAU;sDAAyC,KAAK,WAAW,IAAI;;;;;;wCAE5E,SAAS,YAAY,KAAK,uBACzB,6LAAC;4CAAG,WAAU;sDAAyC,KAAK,QAAQ,IAAI;;;;;;wCAEzE,SAAS,aAAa,KAAK,uBAC1B,6LAAC;4CAAG,WAAU;sDAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;wCAE1F,SAAS,cAAc,KAAK,uBAC3B,6LAAC;4CAAG,WAAU;sDAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;mCAjBpF,KAAK,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;0BA2B9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,SAAS,KAAK,SAAS,KAAK,KAAK,kBACzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;4BAI3C,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,YAAY;;;;;;;;;;;;;;;;;;kCAK7D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,YAAY,KAAK,SAAS,KAAK,QAAQ,KAAK,2BACpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;gDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;gDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;gCAGrE,SAAS,aAAa,KAAK,SAAS,KAAK,GAAG,KAAK,2BAChD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;gDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;gDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;gCAGhE,SAAS,eAAe,KAAK,SAAS,KAAK,KAAK,KAAK,2BACpD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;oDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1E,SAAS,UAAU,KAAK,SAAS,KAAK,aAAa,kBAClD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAiB,KAAK,aAAa;;;;;;;;;;;YAKnD,SAAS,aAAa,KAAK,sBAC1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM1C,SAAS,UAAU,KAAK,sBACvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMpD;KAzPwB", "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ModernTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ModernTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ModernTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Modern Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 text-white\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold mb-2\">{data.companyName}</h1>\n            <p className=\"text-blue-100 text-lg\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4\">\n              <h2 className=\"text-2xl font-bold mb-2\">فاتورة</h2>\n              <p className=\"text-blue-100\">#{data.invoiceNumber}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-8\">\n        {/* Customer Info Card */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8\">\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات العميل</h3>\n              <div className=\"space-y-2\">\n                <p className=\"text-gray-700 font-medium\">{data.customerName}</p>\n                <p className=\"text-gray-600\">{data.customerDetails}</p>\n              </div>\n            </div>\n\n            <div className=\"text-right\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">تفاصيل الفاتورة</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">التاريخ:</span>\n                  <span className=\"font-medium\">{data.date}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                  <span className=\"font-medium\">{data.invoiceNumber}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Items Table */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">تفاصيل الخدمات</h3>\n\n          <div className=\"overflow-hidden rounded-xl border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n                <tr>\n                  <th className=\"p-4 text-right font-semibold\">الإجمالي</th>\n                  <th className=\"p-4 text-right font-semibold\">السعر</th>\n                  <th className=\"p-4 text-right font-semibold\">الكمية</th>\n                  <th className=\"p-4 text-right font-semibold\">الوصف</th>\n                  <th className=\"p-4 text-right font-semibold\">#</th>\n                </tr>\n              </thead>\n              <tbody>\n                {data.items.map((item, index) => (\n                  <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>\n                    <td className=\"p-4 text-right font-semibold text-blue-600\">\n                      {(Number(item.total) || 0).toFixed(2)} ر.س\n                    </td>\n                    <td className=\"p-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm\">\n                        {item.quantity || 0}\n                      </span>\n                    </td>\n                    <td className=\"p-4 text-right font-medium\">{item.description || ''}</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm font-medium\">\n                        {index + 1}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Modern Totals */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div className=\"w-1/2\">\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6\">\n              <h4 className=\"text-lg font-semibold text-gray-800 mb-3\">ملاحظات</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h5 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h5>\n                <ul className=\"text-sm text-gray-600 space-y-1\">\n                  <li>• الدفع خلال 30 يوم من تاريخ الفاتورة</li>\n                  <li>• جميع الأسعار شاملة ضريبة القيمة المضافة</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"w-80\">\n            <div className=\"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white\">\n              <h4 className=\"text-lg font-semibold mb-4\">ملخص الفاتورة</h4>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"border-t border-white border-opacity-30 pt-3\">\n                  <div className=\"flex justify-between text-xl font-bold\">\n                    <span>الإجمالي النهائي:</span>\n                    <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Footer */}\n        <div className=\"bg-gray-50 rounded-xl p-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">معلومات التواصل</h4>\n              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                <span>📞 +966 50 123 4567</span>\n                <span>📧 <EMAIL></span>\n                <span>🌐 www.company.com</span>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-32 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold\">\n                شعار الشركة\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;AAFA;;;AAIe,SAAS,eAAe,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IACjE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAEpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B,KAAK,WAAW;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAyB,KAAK,cAAc;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAE,WAAU;;4CAAgB;4CAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B,KAAK,YAAY;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,IAAI;;;;;;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;sDACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;oDAAiB,WAAW,QAAQ,MAAM,IAAI,eAAe;;sEAC5D,6LAAC;4DAAG,WAAU;;gEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,6LAAC;4DAAG,WAAU;;gEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACrE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;sEAGtB,6LAAC;4DAAG,WAAU;sEAA8B,KAAK,WAAW,IAAI;;;;;;sEAChE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;;;;;;;mDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwB1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAE5C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvJ;KA3KwB", "debugId": null}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ElegantTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ElegantTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ElegantTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'مؤسسة الأناقة التجارية',\n    customerDetails: 'للتجارة والمقاولات',\n    items: [\n      { id: 1, description: 'أثاث مكتبي فاخر', quantity: 5, price: 800.00, total: 4000.00 },\n      { id: 2, description: 'ديكورات داخلية', quantity: 1, price: 2500.00, total: 2500.00 },\n      { id: 3, description: 'إكسسوارات مكتبية', quantity: 10, price: 150.00, total: 1500.00 }\n    ],\n    subtotal: 8000.00,\n    tax: 1200.00,\n    total: 9200.00,\n    notes: 'نتطلع لخدمتكم مرة أخرى',\n    companyName: 'مؤسسة الأناقة التجارية',\n    companyDetails: 'للتجارة والمقاولات'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Elegant Header */}\n      <div className=\"relative\">\n        <div className=\"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600\"></div>\n\n        <div className=\"p-8 pt-12\">\n          <div className=\"flex justify-between items-start mb-8\">\n            <div>\n              <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n              <p className=\"text-gray-600 text-lg italic\">{data.companyDetails}</p>\n\n              <div className=\"mt-6 w-24 h-1 bg-gradient-to-r from-amber-400 to-yellow-500\"></div>\n            </div>\n\n            <div className=\"text-right\">\n              <div className=\"border-2 border-amber-400 rounded-lg p-4 bg-amber-50\">\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-1\">فاتورة</h2>\n                <p className=\"text-amber-600 font-semibold\">#{data.invoiceNumber}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Customer Section */}\n          <div className=\"border border-gray-200 rounded-lg p-6 mb-8 bg-gray-50\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  فواتير إلى\n                </h3>\n                <div className=\"space-y-2\">\n                  <p className=\"text-gray-800 font-semibold text-lg\">{data.customerName}</p>\n                  <p className=\"text-gray-600\">{data.customerDetails}</p>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  تفاصيل الفاتورة\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                    <span className=\"font-semibold\">{data.date}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">رقم المرجع:</span>\n                    <span className=\"font-semibold\">{data.invoiceNumber}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Items Table */}\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-serif font-semibold text-gray-800 mb-4 border-b-2 border-amber-400 pb-2\">\n              بنود الفاتورة\n            </h3>\n\n            <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gradient-to-r from-gray-100 to-amber-50\">\n                  <tr>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">المبلغ</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">سعر الوحدة</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الكمية</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الوصف</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">م</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {data.items.map((item, index) => (\n                    <tr key={item.id} className=\"border-b border-gray-100 hover:bg-amber-25\">\n                      <td className=\"p-4 text-right font-semibold text-amber-600\">\n                        {(Number(item.total) || 0).toFixed(2)} ر.س\n                      </td>\n                      <td className=\"p-4 text-right text-gray-700\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium\">\n                          {item.quantity || 0}\n                        </span>\n                      </td>\n                      <td className=\"p-4 text-right font-medium text-gray-800\">{item.description || ''}</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                          {index + 1}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Elegant Summary */}\n          <div className=\"flex justify-between items-start mb-8\">\n            <div className=\"w-1/2 pr-8\">\n              <div className=\"border-l-4 border-amber-400 pl-6\">\n                <h4 className=\"text-lg font-serif font-semibold text-gray-800 mb-3\">ملاحظات خاصة</h4>\n                <p className=\"text-gray-700 italic mb-4\">{data.notes}</p>\n\n                <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n                  <h5 className=\"font-semibold text-gray-800 mb-2\">شروط وأحكام:</h5>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• الدفع خلال 15 يوم من تاريخ الفاتورة</li>\n                    <li>• ضمان جودة لمدة سنة كاملة</li>\n                    <li>• خدمة ما بعد البيع متاحة</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"w-80\">\n              <div className=\"border-2 border-amber-300 rounded-lg bg-gradient-to-br from-amber-50 to-yellow-50\">\n                <div className=\"bg-gradient-to-r from-amber-400 to-yellow-500 text-white p-4 rounded-t-lg\">\n                  <h4 className=\"text-lg font-serif font-semibold\">الملخص المالي</h4>\n                </div>\n\n                <div className=\"p-6 space-y-4\">\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>المجموع الفرعي:</span>\n                    <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>ضريبة القيمة المضافة:</span>\n                    <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"border-t-2 border-amber-300 pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold text-gray-800\">\n                      <span className=\"font-serif\">الإجمالي النهائي:</span>\n                      <span className=\"text-amber-600\">{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Footer */}\n          <div className=\"border-t-2 border-amber-300 pt-6\">\n            <div className=\"flex justify-between items-end\">\n              <div>\n                <h4 className=\"font-serif font-semibold text-gray-800 mb-3\">معلومات التواصل</h4>\n                <div className=\"space-y-1 text-sm text-gray-600\">\n                  <p>📱 الهاتف: +966 50 123 4567</p>\n                  <p>📧 البريد: <EMAIL></p>\n                  <p>🌐 الموقع: www.elegant-business.com</p>\n                  <p>📍 العنوان: الرياض، المملكة العربية السعودية</p>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <h5 className=\"font-serif font-semibold text-gray-800 mb-4\">توقيع مخول</h5>\n                <div className=\"w-32 h-16 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-amber-500 text-sm\">التوقيع</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAmB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAoB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;SACvF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;kBAEpE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoD,KAAK,WAAW;;;;;;sDAClF,6LAAC;4CAAE,WAAU;sDAAgC,KAAK,cAAc;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;;oDAA+B;oDAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAMtE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuC,KAAK,YAAY;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kDAItD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAIrG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAGnG,6LAAC;0DACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEAAG,WAAU;;oEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EAExC,6LAAC;gEAAG,WAAU;;oEAAgC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACnF,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;0EAGtB,6LAAC;gEAAG,WAAU;0EAA4C,KAAK,WAAW,IAAI;;;;;;0EAC9E,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EACb,QAAQ;;;;;;;;;;;;uDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE3E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAa;;;;;;8EAC7B,6LAAC;oEAAK,WAAU;;wEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;kDAIP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;KA1LwB", "debugId": null}}, {"offset": {"line": 4928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/MinimalTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/MinimalTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function MinimalTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة البساطة للأعمال',\n    customerDetails: 'الحلول البسيطة والفعالة',\n    items: [\n      { id: 1, description: 'خدمة استشارية', quantity: 2, price: 500.00, total: 1000.00 },\n      { id: 2, description: 'تطوير موقع إلكتروني', quantity: 1, price: 3000.00, total: 3000.00 },\n      { id: 3, description: 'صيانة شهرية', quantity: 6, price: 200.00, total: 1200.00 }\n    ],\n    subtotal: 5200.00,\n    tax: 780.00,\n    total: 5980.00,\n    notes: 'شكراً لاختياركم خدماتنا',\n    companyName: 'شركة البساطة للأعمال',\n    companyDetails: 'الحلول البسيطة والفعالة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white p-8\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Minimal Header */}\n      <div className=\"border-b-4 border-gray-800 pb-6 mb-8\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n            <p className=\"text-gray-600\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <h2 className=\"text-4xl font-bold text-gray-800 mb-2\">فاتورة</h2>\n            <p className=\"text-gray-600 text-lg\">#{data.invoiceNumber}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Customer and Invoice Info */}\n      <div className=\"grid grid-cols-2 gap-12 mb-8\">\n        <div>\n          <h3 className=\"text-lg font-bold text-gray-800 mb-3\">إلى:</h3>\n          <div className=\"space-y-1\">\n            <p className=\"text-gray-800 font-semibold\">{data.customerName}</p>\n            <p className=\"text-gray-600\">{data.customerDetails}</p>\n          </div>\n        </div>\n\n        <div className=\"text-right\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">تاريخ الفاتورة:</span>\n              <span className=\"font-semibold\">{data.date}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">رقم الفاتورة:</span>\n              <span className=\"font-semibold\">{data.invoiceNumber}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"border-b-2 border-gray-800\">\n              <th className=\"py-3 text-right font-bold text-gray-800\">المبلغ</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">السعر</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الكمية</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الوصف</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id} className=\"border-b border-gray-200\">\n                <td className=\"py-4 text-right font-semibold\">{(Number(item.total) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{item.quantity || 0}</td>\n                <td className=\"py-4 text-right\">{item.description || ''}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Simple Totals */}\n      <div className=\"flex justify-between items-start mb-12\">\n        <div className=\"w-1/2\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3\">ملاحظات:</h4>\n          <p className=\"text-gray-700\">{data.notes}</p>\n\n          <div className=\"mt-6\">\n            <h5 className=\"font-bold text-gray-800 mb-2\">شروط الدفع:</h5>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• الدفع خلال 30 يوم</li>\n              <li>• تطبق غرامة تأخير 2% شهرياً</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"w-80\">\n          <div className=\"border-2 border-gray-800 p-6\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">المجموع الفرعي:</span>\n                <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">الضريبة (15%):</span>\n                <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"border-t-2 border-gray-800 pt-3\">\n                <div className=\"flex justify-between text-xl font-bold\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Footer */}\n      <div className=\"border-t border-gray-300 pt-6\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-600\">\n            <p>الهاتف: +966 50 123 4567 | البريد: <EMAIL> | الموقع: www.simple.com</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-24 h-12 border border-gray-400 flex items-center justify-center text-gray-500 text-sm\">\n              الشعار\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAiB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YAClF;gBAAE,IAAI;gBAAG,aAAa;gBAAuB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACzF;gBAAE,IAAI;gBAAG,aAAa;gBAAe,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;SACjF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAExE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC,KAAK,WAAW;;;;;;8CACvE,6LAAC;oCAAE,WAAU;8CAAiB,KAAK,cAAc;;;;;;;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B,KAAK,YAAY;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;sDAAiB,KAAK,IAAI;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;sDAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;;;;;;;;;;;;sCAG5D,6LAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAG,WAAU;;gDAAiC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACpF,6LAAC;4CAAG,WAAU;;gDAAmB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACtE,6LAAC;4CAAG,WAAU;sDAAmB,KAAK,QAAQ,IAAI;;;;;;sDAClD,6LAAC;4CAAG,WAAU;sDAAmB,KAAK,WAAW,IAAI;;;;;;;mCAJ9C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAYxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;sCAGL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAA0F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrH;KA3IwB", "debugId": null}}, {"offset": {"line": 5531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/settingsService.js"], "sourcesContent": ["// frontend/lib/settingsService.js\n\n// خدمة إدارة الإعدادات\nclass SettingsService {\n  constructor() {\n    this.storageKey = 'invoiceAppSettings';\n    this.defaultSettings = this.getDefaultSettings();\n  }\n\n  // الحصول على الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      company: {\n        companyName: '',\n        companyAddress: '',\n        companyPhone: '',\n        companyEmail: '',\n        companyWebsite: '', // اختياري\n        taxId: '',\n        currency: 'SAR',\n        logoUrl: null\n      },\n      invoice: {\n        invoicePrefix: 'INV',\n        invoiceNumberLength: 6,\n        defaultTaxRate: 15,\n        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n        autoGenerateInvoiceNumber: true,\n        showImagesInInvoice: true,\n        allowPartialPayments: true,\n        requireCustomerInfo: true,\n        defaultDueDays: 30,\n        invoiceFooter: '',\n        invoiceNotes: '',\n        // إعدادات العرض\n        display: {\n          // عناصر الرأس\n          showCompanyLogo: true,\n          showCompanyName: true,\n          showCompanyAddress: true,\n          showCompanyPhone: true,\n          showCompanyEmail: true,\n          showCompanyWebsite: true,\n          showTaxId: true,\n\n          // معلومات الفاتورة\n          showInvoiceNumber: true,\n          showInvoiceDate: true,\n          showDueDate: true,\n          showPaymentTerms: true,\n\n          // معلومات العميل\n          showCustomerName: true,\n          showCustomerAddress: true,\n          showCustomerPhone: true,\n          showCustomerEmail: true,\n\n          // عناصر الجدول\n          showProductImages: true,\n          showProductCode: true,\n          showProductDescription: true,\n          showQuantity: true,\n          showUnitPrice: true,\n          showDiscount: false,\n          showTotalPrice: true,\n          showItemNumbers: true,\n\n          // المجاميع\n          showSubtotal: true,\n          showTaxAmount: true,\n          showDiscountAmount: false,\n          showTotalAmount: true,\n\n          // التذييل\n          showNotes: true,\n          showFooter: true,\n          showSignature: false,\n          showQRCode: false,\n          showBankDetails: false,\n          showPaymentInstructions: true\n        }\n      }\n    };\n  }\n\n  // تحميل الإعدادات\n  async loadSettings() {\n    try {\n      // محاولة تحميل من localStorage أولاً\n      const localSettings = localStorage.getItem(this.storageKey);\n      if (localSettings) {\n        const parsed = JSON.parse(localSettings);\n        return this.mergeWithDefaults(parsed);\n      }\n\n      // في المستقبل: تحميل من API\n      // const response = await fetch('/api/settings');\n      // const settings = await response.json();\n      // return this.mergeWithDefaults(settings);\n\n      return this.defaultSettings;\n    } catch (error) {\n      console.error('Error loading settings:', error);\n      return this.defaultSettings;\n    }\n  }\n\n  // حفظ الإعدادات\n  async saveSettings(settings) {\n    try {\n      const mergedSettings = this.mergeWithDefaults(settings);\n\n      // حفظ في localStorage\n      localStorage.setItem(this.storageKey, JSON.stringify(mergedSettings));\n\n      // في المستقبل: حفظ في API\n      // await fetch('/api/settings', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(mergedSettings)\n      // });\n\n      return mergedSettings;\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      throw error;\n    }\n  }\n\n  // دمج الإعدادات مع الافتراضية\n  mergeWithDefaults(settings) {\n    return {\n      company: { ...this.defaultSettings.company, ...settings.company },\n      invoice: {\n        ...this.defaultSettings.invoice,\n        ...settings.invoice,\n        display: {\n          ...this.defaultSettings.invoice.display,\n          ...settings.invoice?.display\n        }\n      }\n    };\n  }\n\n  // الحصول على إعدادات الشركة\n  async getCompanySettings() {\n    const settings = await this.loadSettings();\n    return settings.company;\n  }\n\n  // الحصول على إعدادات الفواتير\n  async getInvoiceSettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice;\n  }\n\n  // الحصول على إعدادات عرض الفواتير\n  async getInvoiceDisplaySettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice.display;\n  }\n\n  // حفظ إعدادات الشركة\n  async saveCompanySettings(companySettings) {\n    const currentSettings = await this.loadSettings();\n\n    // معالجة رفع الصورة إذا كانت موجودة\n    if (companySettings.logoFile) {\n      try {\n        const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);\n        companySettings.logoUrl = logoUrl;\n        delete companySettings.logoFile; // إزالة الملف من البيانات\n      } catch (error) {\n        console.error('Error uploading logo:', error);\n        throw new Error('فشل في رفع شعار الشركة');\n      }\n    }\n\n    return this.saveSettings({\n      ...currentSettings,\n      company: companySettings\n    });\n  }\n\n  // رفع شعار الشركة\n  async uploadCompanyLogo(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const logoUrl = e.target.result;\n        // حفظ في localStorage مؤقتاً\n        localStorage.setItem('companyLogo', logoUrl);\n        resolve(logoUrl);\n      };\n      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));\n      reader.readAsDataURL(file);\n    });\n  }\n\n  // الحصول على شعار الشركة\n  getCompanyLogo() {\n    return localStorage.getItem('companyLogo');\n  }\n\n  // حفظ إعدادات الفواتير\n  async saveInvoiceSettings(invoiceSettings) {\n    const currentSettings = await this.loadSettings();\n    return this.saveSettings({\n      ...currentSettings,\n      invoice: {\n        ...currentSettings.invoice,\n        ...invoiceSettings\n      }\n    });\n  }\n\n  // حفظ إعدادات عرض الفواتير\n  async saveInvoiceDisplaySettings(displaySettings) {\n    const currentSettings = await this.loadSettings();\n    return this.saveSettings({\n      ...currentSettings,\n      invoice: {\n        ...currentSettings.invoice,\n        display: displaySettings\n      }\n    });\n  }\n}\n\n// إنشاء مثيل واحد للخدمة\nconst settingsService = new SettingsService();\n\nexport default settingsService;\n"], "names": [], "mappings": "AAAA,kCAAkC;AAElC,uBAAuB;;;;AACvB,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB;IAChD;IAEA,kCAAkC;IAClC,qBAAqB;QACnB,OAAO;YACL,SAAS;gBACP,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;gBACV,SAAS;YACX;YACA,SAAS;gBACP,eAAe;gBACf,qBAAqB;gBACrB,gBAAgB;gBAChB,qBAAqB;gBACrB,2BAA2B;gBAC3B,qBAAqB;gBACrB,sBAAsB;gBACtB,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,SAAS;oBACP,cAAc;oBACd,iBAAiB;oBACjB,iBAAiB;oBACjB,oBAAoB;oBACpB,kBAAkB;oBAClB,kBAAkB;oBAClB,oBAAoB;oBACpB,WAAW;oBAEX,mBAAmB;oBACnB,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;oBACb,kBAAkB;oBAElB,iBAAiB;oBACjB,kBAAkB;oBAClB,qBAAqB;oBACrB,mBAAmB;oBACnB,mBAAmB;oBAEnB,eAAe;oBACf,mBAAmB;oBACnB,iBAAiB;oBACjB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,iBAAiB;oBAEjB,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,oBAAoB;oBACpB,iBAAiB;oBAEjB,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,YAAY;oBACZ,iBAAiB;oBACjB,yBAAyB;gBAC3B;YACF;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe;QACnB,IAAI;YACF,qCAAqC;YACrC,MAAM,gBAAgB,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;YAC1D,IAAI,eAAe;gBACjB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC;YAEA,4BAA4B;YAC5B,iDAAiD;YACjD,0CAA0C;YAC1C,2CAA2C;YAE3C,OAAO,IAAI,CAAC,eAAe;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,IAAI,CAAC,eAAe;QAC7B;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa,QAAQ,EAAE;QAC3B,IAAI;YACF,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC;YAE9C,sBAAsB;YACtB,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;YAErD,0BAA0B;YAC1B,iCAAiC;YACjC,oBAAoB;YACpB,qDAAqD;YACrD,yCAAyC;YACzC,MAAM;YAEN,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,kBAAkB,QAAQ,EAAE;QAC1B,OAAO;YACL,SAAS;gBAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO;gBAAE,GAAG,SAAS,OAAO;YAAC;YAChE,SAAS;gBACP,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO;gBAC/B,GAAG,SAAS,OAAO;gBACnB,SAAS;oBACP,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO;oBACvC,GAAG,SAAS,OAAO,EAAE,OAAO;gBAC9B;YACF;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO;IACzB;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO;IACzB;IAEA,kCAAkC;IAClC,MAAM,4BAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,CAAC,OAAO;IACjC;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,eAAe,EAAE;QACzC,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAE/C,oCAAoC;QACpC,IAAI,gBAAgB,QAAQ,EAAE;YAC5B,IAAI;gBACF,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,QAAQ;gBACrE,gBAAgB,OAAO,GAAG;gBAC1B,OAAO,gBAAgB,QAAQ,EAAE,0BAA0B;YAC7D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,CAAC,MAAM;gBAC/B,6BAA6B;gBAC7B,aAAa,OAAO,CAAC,eAAe;gBACpC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,yBAAyB;IACzB,iBAAiB;QACf,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,eAAe,EAAE;QACzC,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAC/C,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;gBACP,GAAG,gBAAgB,OAAO;gBAC1B,GAAG,eAAe;YACpB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,2BAA2B,eAAe,EAAE;QAChD,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAC/C,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;gBACP,GAAG,gBAAgB,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;AACF;AAEA,yBAAyB;AACzB,MAAM,kBAAkB,IAAI;uCAEb", "debugId": null}}, {"offset": {"line": 5752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/hooks/useInvoiceSettings.js"], "sourcesContent": ["// frontend/components/invoice-templates/hooks/useInvoiceSettings.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport settingsService from '@/lib/settingsService';\n\nexport function useInvoiceSettings() {\n  const [companySettings, setCompanySettings] = useState(null);\n  const [invoiceSettings, setInvoiceSettings] = useState(null);\n  const [displaySettings, setDisplaySettings] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  const loadSettings = async () => {\n    try {\n      setLoading(true);\n      const [company, invoice, display] = await Promise.all([\n        settingsService.getCompanySettings(),\n        settingsService.getInvoiceSettings(),\n        settingsService.getInvoiceDisplaySettings()\n      ]);\n\n      setCompanySettings(company);\n      setInvoiceSettings(invoice);\n      setDisplaySettings(display);\n    } catch (error) {\n      console.error('Error loading settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    companySettings,\n    invoiceSettings,\n    displaySettings,\n    loading,\n    refreshSettings: loadSettings\n  };\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;AAGpE;AACA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,SAAS,SAAS,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,yHAAA,CAAA,UAAe,CAAC,kBAAkB;gBAClC,yHAAA,CAAA,UAAe,CAAC,kBAAkB;gBAClC,yHAAA,CAAA,UAAe,CAAC,yBAAyB;aAC1C;YAED,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB;IACnB;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 5808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/SmartTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/SmartTemplate.js\n'use client';\n\nimport React from 'react';\nimport { useInvoiceSettings } from './hooks/useInvoiceSettings';\nimport settingsService from '@/lib/settingsService';\n\nexport default function SmartTemplate({ invoice, templateComponent: TemplateComponent, preview = false }) {\n  const { companySettings, invoiceSettings, displaySettings, loading } = useInvoiceSettings();\n\n  if (loading) {\n    return (\n      <div className=\"bg-white p-8 flex items-center justify-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري تحميل الإعدادات...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // دمج بيانات الفاتورة مع إعدادات الشركة\n  const enhancedInvoice = {\n    ...invoice,\n    // معلومات الشركة من الإعدادات (بدون قيم افتراضية)\n    companyName: companySettings?.companyName || '',\n    companyDetails: companySettings?.companyAddress || '',\n    companyAddress: companySettings?.companyAddress || '',\n    companyPhone: companySettings?.companyPhone || '',\n    companyEmail: companySettings?.companyEmail || '',\n    companyWebsite: companySettings?.companyWebsite || '',\n    taxId: companySettings?.taxId || '',\n    logoUrl: companySettings?.logoUrl || settingsService.getCompanyLogo() || null,\n\n    // إعدادات الفاتورة\n    currency: companySettings?.currency || 'SAR',\n    taxRate: invoiceSettings?.defaultTaxRate || 15,\n    paymentTerms: invoiceSettings?.defaultPaymentTerms || '',\n    invoiceFooter: invoiceSettings?.invoiceFooter || '',\n    invoiceNotes: invoice?.notes || invoiceSettings?.invoiceNotes || '',\n\n    // إعدادات العرض\n    displaySettings: displaySettings || {}\n  };\n\n  return (\n    <TemplateComponent\n      invoice={enhancedInvoice}\n      preview={preview}\n      displaySettings={displaySettings}\n    />\n  );\n}\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;AAGzD;AACA;AACA;;;AAJA;;;;AAMe,SAAS,cAAc,EAAE,OAAO,EAAE,mBAAmB,iBAAiB,EAAE,UAAU,KAAK,EAAE;;IACtG,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD;IAExF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;YAAgD,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACzG,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,GAAG,OAAO;QACV,kDAAkD;QAClD,aAAa,iBAAiB,eAAe;QAC7C,gBAAgB,iBAAiB,kBAAkB;QACnD,gBAAgB,iBAAiB,kBAAkB;QACnD,cAAc,iBAAiB,gBAAgB;QAC/C,cAAc,iBAAiB,gBAAgB;QAC/C,gBAAgB,iBAAiB,kBAAkB;QACnD,OAAO,iBAAiB,SAAS;QACjC,SAAS,iBAAiB,WAAW,yHAAA,CAAA,UAAe,CAAC,cAAc,MAAM;QAEzE,mBAAmB;QACnB,UAAU,iBAAiB,YAAY;QACvC,SAAS,iBAAiB,kBAAkB;QAC5C,cAAc,iBAAiB,uBAAuB;QACtD,eAAe,iBAAiB,iBAAiB;QACjD,cAAc,SAAS,SAAS,iBAAiB,gBAAgB;QAEjE,gBAAgB;QAChB,iBAAiB,mBAAmB,CAAC;IACvC;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,SAAS;QACT,iBAAiB;;;;;;AAGvB;GA7CwB;;QACiD,oKAAA,CAAA,qBAAkB;;;KADnE", "debugId": null}}, {"offset": {"line": 5910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/InvoiceRenderer.js"], "sourcesContent": ["// frontend/components/invoice-templates/InvoiceRenderer.js\n'use client';\n\nimport React from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport EnhancedClassicTemplate from './EnhancedClassicTemplate';\nimport RealDataTemplate from './RealDataTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\nimport SmartTemplate from './SmartTemplate';\n\nconst templateComponents = {\n  classic: RealDataTemplate, // استخدام القالب الذي يعتمد على البيانات الحقيقية\n  modern: ModernTemplate,\n  elegant: ElegantTemplate,\n  minimal: MinimalTemplate\n};\n\nexport default function InvoiceRenderer({ invoice, templateId, preview = false }) {\n  // Get template from props or localStorage\n  const selectedTemplate = templateId || localStorage.getItem('selectedInvoiceTemplate') || 'classic';\n\n  // Get the template component\n  const TemplateComponent = templateComponents[selectedTemplate] || RealDataTemplate;\n\n  if (!TemplateComponent) {\n    return (\n      <div className=\"bg-white p-8 text-center\">\n        <p className=\"text-red-600\">خطأ: القالب المحدد غير موجود</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"invoice-container\">\n      <SmartTemplate\n        invoice={invoice}\n        templateComponent={TemplateComponent}\n        preview={preview}\n      />\n    </div>\n  );\n}\n\n// Export template components for direct use\nexport {\n  ClassicTemplate,\n  ModernTemplate,\n  ElegantTemplate,\n  MinimalTemplate,\n  templateComponents\n};\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,qBAAqB;IACzB,SAAS,yJAAA,CAAA,UAAgB;IACzB,QAAQ,uJAAA,CAAA,UAAc;IACtB,SAAS,wJAAA,CAAA,UAAe;IACxB,SAAS,wJAAA,CAAA,UAAe;AAC1B;AAEe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,KAAK,EAAE;IAC9E,0CAA0C;IAC1C,MAAM,mBAAmB,cAAc,aAAa,OAAO,CAAC,8BAA8B;IAE1F,6BAA6B;IAC7B,MAAM,oBAAoB,kBAAkB,CAAC,iBAAiB,IAAI,yJAAA,CAAA,UAAgB;IAElF,IAAI,CAAC,mBAAmB;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAE,WAAU;0BAAe;;;;;;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sJAAA,CAAA,UAAa;YACZ,SAAS;YACT,mBAAmB;YACnB,SAAS;;;;;;;;;;;AAIjB;KAxBwB", "debugId": null}}, {"offset": {"line": 6009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/demo/InvoiceSettingsDemo.js"], "sourcesContent": ["// frontend/components/demo/InvoiceSettingsDemo.js\n'use client';\n\nimport { useState } from 'react';\nimport apiSettingsService from '@/lib/apiSettingsService';\nimport InvoiceRenderer from '@/components/invoice-templates/InvoiceRenderer';\n\nexport default function InvoiceSettingsDemo() {\n  const [settings, setSettings] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const loadSettings = async () => {\n    setLoading(true);\n    try {\n      const data = await apiSettingsService.loadSettings();\n      setSettings(data);\n      console.log('Settings loaded successfully:', data);\n    } catch (error) {\n      console.error('Error loading settings:', error);\n      alert('❌ حدث خطأ أثناء تحميل الإعدادات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const saveTestCompanySettings = async () => {\n    setLoading(true);\n    try {\n      await apiSettingsService.saveCompanySettings({\n        companyName: 'شركة الابتكار التقني المحدودة',\n        companyAddress: 'الرياض، حي الملك فهد، المملكة العربية السعودية',\n        companyPhone: '+966 11 555 0123',\n        companyEmail: '<EMAIL>',\n        companyWebsite: 'www.innovation-tech.com', // اختياري\n        taxId: '300*********003',\n        currency: 'SAR'\n      });\n\n      alert('✅ تم حفظ معلومات الشركة بنجاح!');\n      loadSettings();\n    } catch (error) {\n      console.error('Error saving company settings:', error);\n      alert('❌ حدث خطأ أثناء حفظ معلومات الشركة');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const saveTestDisplaySettings = async () => {\n    setLoading(true);\n    try {\n      await apiSettingsService.saveInvoiceDisplaySettings({\n        // رأس الفاتورة\n        showCompanyLogo: true,\n        showCompanyName: true,\n        showCompanyAddress: true,\n        showCompanyPhone: true,\n        showCompanyEmail: true,\n        showCompanyWebsite: true,\n        showTaxId: true,\n\n        // معلومات الفاتورة\n        showInvoiceNumber: true,\n        showInvoiceDate: true,\n        showDueDate: true,\n        showPaymentTerms: true,\n\n        // معلومات العميل\n        showCustomerName: true,\n        showCustomerAddress: true,\n        showCustomerPhone: true,\n        showCustomerEmail: true,\n\n        // عناصر الجدول\n        showProductImages: true,\n        showProductCode: true,\n        showProductDescription: true,\n        showQuantity: true,\n        showUnitPrice: true,\n        showDiscount: false,\n        showTotalPrice: true,\n        showItemNumbers: true,\n\n        // المجاميع\n        showSubtotal: true,\n        showTaxAmount: true,\n        showDiscountAmount: false,\n        showTotalAmount: true,\n\n        // التذييل\n        showNotes: true,\n        showFooter: true,\n        showSignature: false,\n        showQRCode: false,\n        showBankDetails: false,\n        showPaymentInstructions: true\n      });\n\n      alert('✅ تم حفظ إعدادات العرض بنجاح!');\n      loadSettings();\n    } catch (error) {\n      console.error('Error saving display settings:', error);\n      alert('❌ حدث خطأ أثناء حفظ إعدادات العرض');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const hideCompanyInfo = async () => {\n    setLoading(true);\n    try {\n      await apiSettingsService.saveInvoiceDisplaySettings({\n        showCompanyLogo: false,\n        showCompanyName: true, // نبقي الاسم فقط\n        showCompanyAddress: false,\n        showCompanyPhone: false,\n        showCompanyEmail: false,\n        showCompanyWebsite: false, // إخفاء الموقع الإلكتروني (اختياري)\n        showTaxId: false,\n\n        showInvoiceNumber: true,\n        showInvoiceDate: true,\n        showCustomerName: true,\n        showProductDescription: true,\n        showQuantity: true,\n        showUnitPrice: true,\n        showTotalPrice: true,\n        showSubtotal: true,\n        showTaxAmount: true,\n        showTotalAmount: true,\n        showNotes: false,\n        showFooter: false\n      });\n\n      alert('✅ تم إخفاء معلومات الشركة من الفاتورة!');\n      loadSettings();\n    } catch (error) {\n      console.error('Error hiding company info:', error);\n      alert('❌ حدث خطأ أثناء إخفاء معلومات الشركة');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const saveCompanyWithoutWebsite = async () => {\n    setLoading(true);\n    try {\n      await apiSettingsService.saveCompanySettings({\n        companyName: 'شركة بدون موقع إلكتروني',\n        companyAddress: 'الدمام، المملكة العربية السعودية',\n        companyPhone: '+966 13 555 7890',\n        companyEmail: '<EMAIL>',\n        companyWebsite: '', // بدون موقع إلكتروني\n        taxId: '300987654321003',\n        currency: 'SAR'\n      });\n\n      alert('✅ تم حفظ معلومات الشركة بدون موقع إلكتروني!');\n      loadSettings();\n    } catch (error) {\n      console.error('Error saving company without website:', error);\n      alert('❌ حدث خطأ أثناء حفظ معلومات الشركة');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // بيانات فاتورة تجريبية - ستُدمج مع إعدادات الشركة\n  const getSampleInvoice = () => {\n    if (!settings) return {};\n\n    return {\n      invoiceNumber: 'INV-2024-001',\n      date: new Date().toLocaleDateString('ar-SA'),\n      customerName: 'شركة العميل التجاري',\n      customerAddress: 'جدة، المملكة العربية السعودية',\n      customerPhone: '+966 12 555 9876',\n      customerEmail: '<EMAIL>',\n      items: [\n        {\n          id: 1,\n          code: 'SOFT-001',\n          description: 'تطوير نظام إدارة المخزون',\n          quantity: 1,\n          price: 15000.00,\n          total: 15000.00\n        },\n        {\n          id: 2,\n          code: 'CONS-002',\n          description: 'استشارات تقنية متخصصة',\n          quantity: 20,\n          price: 300.00,\n          total: 6000.00\n        },\n        {\n          id: 3,\n          code: 'SUPP-003',\n          description: 'دعم فني لمدة سنة',\n          quantity: 1,\n          price: 5000.00,\n          total: 5000.00\n        }\n      ],\n      subtotal: 26000.00,\n      tax: 3900.00,\n      total: 29900.00,\n      notes: 'شكراً لثقتكم بنا ونتطلع للعمل معكم مستقبلاً',\n      paymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n\n      // دمج معلومات الشركة من الإعدادات\n      companyName: settings.company?.companyName || '',\n      companyAddress: settings.company?.companyAddress || '',\n      companyPhone: settings.company?.companyPhone || '',\n      companyEmail: settings.company?.companyEmail || '',\n      companyWebsite: settings.company?.companyWebsite || '',\n      taxId: settings.company?.taxId || '',\n      logoUrl: settings.company?.logoUrl || apiSettingsService.getCompanyLogo(),\n      currency: settings.company?.currency || 'ر.س',\n\n      // إعدادات العرض\n      displaySettings: settings.invoice?.display || {}\n    };\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto p-6\">\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mb-8\">\n        <div className=\"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4\">\n          <h2 className=\"text-xl font-bold text-white\">🧪 عرض توضيحي لنظام إعدادات الفواتير</h2>\n          <p className=\"text-purple-100 text-sm mt-1\">\n            اختبر كيفية تأثير الإعدادات على شكل الفاتورة في الوقت الفعلي\n          </p>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6\">\n            <button\n              onClick={loadSettings}\n              disabled={loading}\n              className=\"px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 transition-colors\"\n            >\n              {loading ? '⏳ جاري التحميل...' : '📥 تحميل الإعدادات'}\n            </button>\n\n            <button\n              onClick={saveTestCompanySettings}\n              disabled={loading}\n              className=\"px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 transition-colors\"\n            >\n              {loading ? '⏳ جاري الحفظ...' : '🏢 شركة مع موقع'}\n            </button>\n\n            <button\n              onClick={saveCompanyWithoutWebsite}\n              disabled={loading}\n              className=\"px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:bg-gray-400 transition-colors\"\n            >\n              {loading ? '⏳ جاري الحفظ...' : '🏢 شركة بدون موقع'}\n            </button>\n\n            <button\n              onClick={saveTestDisplaySettings}\n              disabled={loading}\n              className=\"px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-400 transition-colors\"\n            >\n              {loading ? '⏳ جاري الحفظ...' : '👁️ إظهار كل العناصر'}\n            </button>\n\n            <button\n              onClick={hideCompanyInfo}\n              disabled={loading}\n              className=\"px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:bg-gray-400 transition-colors\"\n            >\n              {loading ? '⏳ جاري الحفظ...' : '🙈 إخفاء معلومات الشركة'}\n            </button>\n          </div>\n\n          {settings && (\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* عرض الإعدادات */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">📋 الإعدادات المحملة</h3>\n                <div className=\"bg-gray-50 rounded-lg p-4 max-h-96 overflow-auto\">\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h4 className=\"font-semibold text-gray-700 mb-2\">🏢 معلومات الشركة:</h4>\n                      <div className=\"text-sm space-y-1\">\n                        <p><strong>الاسم:</strong> {settings.company?.companyName || 'غير محدد'}</p>\n                        <p><strong>العنوان:</strong> {settings.company?.companyAddress || 'غير محدد'}</p>\n                        <p><strong>الهاتف:</strong> {settings.company?.companyPhone || 'غير محدد'}</p>\n                        <p><strong>البريد:</strong> {settings.company?.companyEmail || 'غير محدد'}</p>\n                        <p><strong>الموقع:</strong> {settings.company?.companyWebsite || 'غير محدد'}</p>\n                        <p><strong>الرقم الضريبي:</strong> {settings.company?.taxId || 'غير محدد'}</p>\n                      </div>\n                    </div>\n\n                    <div>\n                      <h4 className=\"font-semibold text-gray-700 mb-2\">👁️ إعدادات العرض:</h4>\n                      <div className=\"text-xs grid grid-cols-2 gap-1\">\n                        <span className={settings.invoice?.display?.showCompanyName ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showCompanyName ? '✅' : '❌'} اسم الشركة\n                        </span>\n                        <span className={settings.invoice?.display?.showCompanyAddress ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showCompanyAddress ? '✅' : '❌'} عنوان الشركة\n                        </span>\n                        <span className={settings.invoice?.display?.showCompanyPhone ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showCompanyPhone ? '✅' : '❌'} هاتف الشركة\n                        </span>\n                        <span className={settings.invoice?.display?.showCompanyEmail ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showCompanyEmail ? '✅' : '❌'} بريد الشركة\n                        </span>\n                        <span className={settings.invoice?.display?.showTaxId ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showTaxId ? '✅' : '❌'} الرقم الضريبي\n                        </span>\n                        <span className={settings.invoice?.display?.showNotes ? 'text-green-600' : 'text-red-600'}>\n                          {settings.invoice?.display?.showNotes ? '✅' : '❌'} الملاحظات\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* معاينة الفاتورة */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">📄 معاينة الفاتورة</h3>\n                <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n                  <div className=\"transform scale-50 origin-top-left\" style={{ width: '200%', height: '200%' }}>\n                    <InvoiceRenderer\n                      invoice={getSampleInvoice()}\n                      templateId=\"classic\"\n                      preview={false}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {!settings && (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 mb-4\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <p className=\"text-gray-600\">اضغط على \"تحميل الإعدادات\" لبدء العرض التوضيحي</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;AAGlD;AACA;AACA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,4HAAA,CAAA,UAAkB,CAAC,YAAY;YAClD,YAAY;YACZ,QAAQ,GAAG,CAAC,iCAAiC;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,WAAW;QACX,IAAI;YACF,MAAM,4HAAA,CAAA,UAAkB,CAAC,mBAAmB,CAAC;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;YAEA,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,WAAW;QACX,IAAI;YACF,MAAM,4HAAA,CAAA,UAAkB,CAAC,0BAA0B,CAAC;gBAClD,eAAe;gBACf,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,kBAAkB;gBAClB,kBAAkB;gBAClB,oBAAoB;gBACpB,WAAW;gBAEX,mBAAmB;gBACnB,mBAAmB;gBACnB,iBAAiB;gBACjB,aAAa;gBACb,kBAAkB;gBAElB,iBAAiB;gBACjB,kBAAkB;gBAClB,qBAAqB;gBACrB,mBAAmB;gBACnB,mBAAmB;gBAEnB,eAAe;gBACf,mBAAmB;gBACnB,iBAAiB;gBACjB,wBAAwB;gBACxB,cAAc;gBACd,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBAEjB,WAAW;gBACX,cAAc;gBACd,eAAe;gBACf,oBAAoB;gBACpB,iBAAiB;gBAEjB,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,yBAAyB;YAC3B;YAEA,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,MAAM,4HAAA,CAAA,UAAkB,CAAC,0BAA0B,CAAC;gBAClD,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,kBAAkB;gBAClB,kBAAkB;gBAClB,oBAAoB;gBACpB,WAAW;gBAEX,mBAAmB;gBACnB,iBAAiB;gBACjB,kBAAkB;gBAClB,wBAAwB;gBACxB,cAAc;gBACd,eAAe;gBACf,gBAAgB;gBAChB,cAAc;gBACd,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,YAAY;YACd;YAEA,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B;QAChC,WAAW;QACX,IAAI;YACF,MAAM,4HAAA,CAAA,UAAkB,CAAC,mBAAmB,CAAC;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;YAEA,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,mDAAmD;IACnD,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU,OAAO,CAAC;QAEvB,OAAO;YACL,eAAe;YACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,OAAO;gBACT;aACD;YACD,UAAU;YACV,KAAK;YACL,OAAO;YACP,OAAO;YACP,cAAc;YAEd,kCAAkC;YAClC,aAAa,SAAS,OAAO,EAAE,eAAe;YAC9C,gBAAgB,SAAS,OAAO,EAAE,kBAAkB;YACpD,cAAc,SAAS,OAAO,EAAE,gBAAgB;YAChD,cAAc,SAAS,OAAO,EAAE,gBAAgB;YAChD,gBAAgB,SAAS,OAAO,EAAE,kBAAkB;YACpD,OAAO,SAAS,OAAO,EAAE,SAAS;YAClC,SAAS,SAAS,OAAO,EAAE,WAAW,4HAAA,CAAA,UAAkB,CAAC,cAAc;YACvE,UAAU,SAAS,OAAO,EAAE,YAAY;YAExC,gBAAgB;YAChB,iBAAiB,SAAS,OAAO,EAAE,WAAW,CAAC;QACjD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAA+B;;;;;;;;;;;;8BAK9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,sBAAsB;;;;;;8CAGnC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,oBAAoB;;;;;;8CAGjC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,oBAAoB;;;;;;8CAGjC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,oBAAoB;;;;;;8CAGjC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,oBAAoB;;;;;;;;;;;;wBAIlC,0BACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAe;4EAAE,SAAS,OAAO,EAAE,eAAe;;;;;;;kFAC7D,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAiB;4EAAE,SAAS,OAAO,EAAE,kBAAkB;;;;;;;kFAClE,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAgB;4EAAE,SAAS,OAAO,EAAE,gBAAgB;;;;;;;kFAC/D,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAgB;4EAAE,SAAS,OAAO,EAAE,gBAAgB;;;;;;;kFAC/D,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAgB;4EAAE,SAAS,OAAO,EAAE,kBAAkB;;;;;;;kFACjE,6LAAC;;0FAAE,6LAAC;0FAAO;;;;;;4EAAuB;4EAAE,SAAS,OAAO,EAAE,SAAS;;;;;;;;;;;;;;;;;;;kEAInE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,kBAAkB,mBAAmB;;4EAC9E,SAAS,OAAO,EAAE,SAAS,kBAAkB,MAAM;4EAAI;;;;;;;kFAE1D,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,qBAAqB,mBAAmB;;4EACjF,SAAS,OAAO,EAAE,SAAS,qBAAqB,MAAM;4EAAI;;;;;;;kFAE7D,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,mBAAmB,mBAAmB;;4EAC/E,SAAS,OAAO,EAAE,SAAS,mBAAmB,MAAM;4EAAI;;;;;;;kFAE3D,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,mBAAmB,mBAAmB;;4EAC/E,SAAS,OAAO,EAAE,SAAS,mBAAmB,MAAM;4EAAI;;;;;;;kFAE3D,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,YAAY,mBAAmB;;4EACxE,SAAS,OAAO,EAAE,SAAS,YAAY,MAAM;4EAAI;;;;;;;kFAEpD,6LAAC;wEAAK,WAAW,SAAS,OAAO,EAAE,SAAS,YAAY,mBAAmB;;4EACxE,SAAS,OAAO,EAAE,SAAS,YAAY,MAAM;4EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS9D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqC,OAAO;oDAAE,OAAO;oDAAQ,QAAQ;gDAAO;0DACzF,cAAA,6LAAC,wKAAA,CAAA,UAAe;oDACd,SAAS;oDACT,YAAW;oDACX,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQpB,CAAC,0BACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAoB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC3E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GA3VwB;KAAA", "debugId": null}}, {"offset": {"line": 6692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/demo/page.js"], "sourcesContent": ["'use client';\n\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport InvoiceSettingsDemo from '@/components/demo/InvoiceSettingsDemo';\n\nfunction DemoPage() {\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">🧪 العرض التوضيحي</h1>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  اختبر كيفية عمل نظام إعدادات الفواتير وتأثيرها على القوالب\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"py-8\">\n          <InvoiceSettingsDemo />\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(DemoPage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,SAAS;IACP,qBACE,6LAAC,gIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4IAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;AAK9B;KAvBS;6CAyBM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}