// frontend/components/invoice-templates/ClassicTemplate.js
'use client';

import React from 'react';

export default function ClassicTemplate({ invoice, preview = false }) {
  const sampleData = {
    invoiceNumber: 'INV-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customerName: 'شركة الجبر للمقاولات',
    customerDetails: 'المقاولات العامة د.م.م',
    items: [
      { id: 1, description: 'الصنف الأول', quantity: 2, price: 10.00, total: 20.00 },
      { id: 2, description: 'الصنف الثاني', quantity: 4, price: 10.00, total: 40.00 },
      { id: 3, description: 'الصنف الثالث', quantity: 6, price: 10.00, total: 60.00 },
      { id: 4, description: 'الصنف الرابع', quantity: 8, price: 10.00, total: 80.00 }
    ],
    subtotal: 200.00,
    tax: 0.00,
    total: 200.00,
    notes: 'شكراً لكم...',
    companyName: 'شركة الجبر للمقاولات',
    companyDetails: 'المقاولات العامة د.م.م'
  };

  const data = preview ? sampleData : invoice;

  return (
    <div className="bg-white p-8 shadow-lg" style={{ minHeight: '297mm', width: '210mm' }}>
      {/* Header with Teal Design */}
      <div className="relative mb-8">
        <div className="bg-teal-600 h-16 w-full absolute top-0 left-0"></div>
        <div className="bg-teal-600 h-8 w-3/4 absolute top-16 left-0"></div>
        
        <div className="relative z-10 pt-6 pb-4">
          <div className="flex justify-between items-start">
            <div className="text-white">
              <h1 className="text-2xl font-bold mb-2">{data.companyName}</h1>
              <p className="text-teal-100">{data.companyDetails}</p>
            </div>
            
            {/* Logo Area */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                <div className="text-white font-bold text-xl">شعار</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Title */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-teal-700 mb-4">فاتورة مبيعات</h2>
        
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-right">
            <span className="font-semibold">رقم الفاتورة:</span>
          </div>
          <div className="text-right">
            <span className="font-semibold">اسم العميل:</span>
          </div>
          <div className="text-right">
            <span className="font-semibold">التاريخ:</span>
          </div>
          
          <div>{data.invoiceNumber}</div>
          <div>{data.customerName}</div>
          <div>{data.date}</div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-yellow-100">
              <th className="border border-gray-300 p-3 text-right font-semibold">الإجمالي</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">السعر</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">الكمية</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">البيان</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">م</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id}>
                <td className="border border-gray-300 p-3 text-right">{item.total.toFixed(2)}</td>
                <td className="border border-gray-300 p-3 text-right">{item.price.toFixed(2)}</td>
                <td className="border border-gray-300 p-3 text-right">{item.quantity}</td>
                <td className="border border-gray-300 p-3 text-right">{item.description}</td>
                <td className="border border-gray-300 p-3 text-right">{index + 1}</td>
              </tr>
            ))}
            
            {/* Empty rows for spacing */}
            {[...Array(3)].map((_, i) => (
              <tr key={`empty-${i}`}>
                <td className="border border-gray-300 p-3">-</td>
                <td className="border border-gray-300 p-3">-</td>
                <td className="border border-gray-300 p-3">-</td>
                <td className="border border-gray-300 p-3">-</td>
                <td className="border border-gray-300 p-3">-</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-between items-end mb-8">
        <div className="text-right">
          <p className="text-lg font-semibold mb-4">{data.notes}</p>
        </div>
        
        <div className="w-64">
          <div className="bg-yellow-100 p-4 rounded-lg">
            <div className="flex justify-between mb-2">
              <span className="font-semibold">السعر</span>
              <span>{data.subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-semibold">الضريبة</span>
              <span>{data.tax.toFixed(2)}</span>
            </div>
            <div className="border-t border-gray-300 pt-2">
              <div className="flex justify-between font-bold text-lg">
                <span>الإجمالي</span>
                <span>{data.total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-between items-end">
        <div>
          <h4 className="font-semibold mb-2">ملاحظات</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• البضاعة المباعة لا ترد ولا تستبدل</li>
            <li>• التأكد من استلام جميع بنود الفاتورة</li>
          </ul>
        </div>
        
        <div className="text-center">
          <h4 className="font-semibold mb-4">توقيع البائع</h4>
          <div className="w-32 border-b-2 border-gray-400 mb-2"></div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="mt-8 pt-4 border-t border-gray-300">
        <div className="flex justify-between text-sm text-gray-600">
          <div>
            <p>+123-456-7890</p>
            <p>www.reallygreatsite.com</p>
            <p><EMAIL></p>
          </div>
          <div className="text-right">
            <div className="w-32 h-8 bg-gray-800 flex items-center justify-center">
              <div className="text-white text-xs">|||||||||||||||||||||||</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
