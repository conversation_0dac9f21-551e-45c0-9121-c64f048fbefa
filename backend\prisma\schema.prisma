// backend/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Product {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  price       Decimal  @db.Decimal(10, 2)
  stock       Int
  imageUrl    String? // سيتم تخزين رابط الصورة من خدمة التخزين السحابي هنا
  category    String?
  supplier    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoiceItems InvoiceItem[]
}

model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  email       String?  @unique
  phone       String?
  address     String?
  taxId       String?
  companyName String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoices    Invoice[]
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Invoice {
  id          Int      @id @default(autoincrement())
  invoiceNumber String   @unique
  issueDate   DateTime @db.Date
  dueDate     DateTime @db.Date
  status      String   // "Pending", "Paid", "Canceled"
  totalAmount Decimal  @db.Decimal(10, 2)
  taxAmount   Decimal  @db.Decimal(10, 2)
  discount    Decimal  @db.Decimal(10, 2) @default(0.00)
  currency    String
  notes       String?
  paymentTerms String?
  companyName String?
  companyAddress String?
  companyPhone String?
  companyEmail String?
  companyTaxId String?
  customer    Customer @relation(fields: [customerId], references: [id])
  customerId  Int
  invoiceItems InvoiceItem[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model InvoiceItem {
  id          Int      @id @default(autoincrement())
  quantity    Int
  unitPrice   Decimal  @db.Decimal(10, 2)
  productId   Int
  invoiceId   Int
  product     Product  @relation(fields: [productId], references: [id])
  invoice     Invoice  @relation(fields: [invoiceId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
