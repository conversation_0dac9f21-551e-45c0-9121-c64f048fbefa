// backend/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Product {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  price       Decimal  @db.Decimal(10, 2)
  stock       Int
  minStock    Int      @default(0) // الحد الأدنى للمخزون
  category    String?
  supplier    String?
  barcode     String?  @unique
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  images      ProductImage[]
  invoiceItems InvoiceItem[]
  stockMovements StockMovement[]
}

model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  email       String?  @unique
  phone       String?
  address     String?
  taxId       String?
  companyName String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoices    Invoice[]
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Invoice {
  id            Int      @id @default(autoincrement())
  invoiceNumber String   @unique
  customerId    Int
  subtotal      Decimal  @db.Decimal(10, 2)
  taxRate       Decimal  @db.Decimal(5, 2) @default(0)
  taxAmount     Decimal  @db.Decimal(10, 2) @default(0)
  discountRate  Decimal  @db.Decimal(5, 2) @default(0)
  discountAmount Decimal @db.Decimal(10, 2) @default(0)
  totalAmount   Decimal  @db.Decimal(10, 2)
  status        String   @default("draft") // draft, sent, paid, overdue, cancelled
  paymentMethod String?  // cash, card, bank_transfer, check
  paymentStatus String   @default("unpaid") // unpaid, partial, paid
  paidAmount    Decimal  @db.Decimal(10, 2) @default(0)
  dueDate       DateTime?
  issueDate     DateTime @default(now())
  notes         String?
  terms         String?
  showImages    Boolean  @default(true) // إظهار الصور في الفاتورة
  currency      String   @default("USD")
  customer      Customer @relation(fields: [customerId], references: [id])
  items         InvoiceItem[]
  payments      Payment[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model InvoiceItem {
  id          Int      @id @default(autoincrement())
  quantity    Int
  unitPrice   Decimal  @db.Decimal(10, 2)
  discount    Decimal  @db.Decimal(5, 2) @default(0) // خصم على العنصر
  total       Decimal  @db.Decimal(10, 2) // المجموع بعد الخصم
  productId   Int
  invoiceId   Int
  product     Product  @relation(fields: [productId], references: [id])
  invoice     Invoice  @relation(fields: [invoiceId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج المدفوعات
model Payment {
  id          Int      @id @default(autoincrement())
  invoiceId   Int
  amount      Decimal  @db.Decimal(10, 2)
  method      String   // cash, card, bank_transfer, check
  reference   String?  // رقم المرجع أو الشيك
  notes       String?
  paymentDate DateTime @default(now())
  invoice     Invoice  @relation(fields: [invoiceId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج صور المنتجات
model ProductImage {
  id          Int      @id @default(autoincrement())
  filename    String   // اسم الملف
  originalName String  // الاسم الأصلي للملف
  path        String   // مسار الملف
  size        Int      // حجم الملف بالبايت
  mimeType    String   // نوع الملف
  isMain      Boolean  @default(false) // الصورة الرئيسية
  order       Int      @default(0) // ترتيب الصورة
  productId   Int
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج إعدادات التطبيق
model AppSettings {
  id          Int      @id @default(autoincrement())
  key         String   @unique
  value       String
  description String?
  category    String   @default("general")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج معلومات الشركة
model CompanyInfo {
  id          Int      @id @default(autoincrement())
  name        String
  address     String?
  phone       String?
  email       String?
  website     String?
  taxId       String?
  logo        String?  // مسار شعار الشركة
  currency    String   @default("USD")
  language    String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج حركات المخزون
model StockMovement {
  id          Int      @id @default(autoincrement())
  productId   Int
  type        String   // "IN", "OUT", "ADJUSTMENT"
  quantity    Int
  reason      String?  // سبب الحركة
  reference   String?  // مرجع (رقم فاتورة مثلاً)
  notes       String?
  product     Product  @relation(fields: [productId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
