{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoices/create/page.js"], "sourcesContent": ["// frontend/app/dashboard/invoices/create/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Image from 'next/image';\nimport { createInvoice, getCustomers, getProducts } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\n\nfunction CreateInvoicePage() {\n  const [invoiceData, setInvoiceData] = useState({\n    customerId: '',\n    items: [],\n    taxRate: 0,\n    discountRate: 0,\n    notes: '',\n    terms: '',\n    dueDate: '',\n    showImages: true\n  });\n  \n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [selectedProduct, setSelectedProduct] = useState('');\n  const [productQuantity, setProductQuantity] = useState(1);\n  const [productPrice, setProductPrice] = useState('');\n  const [productDiscount, setProductDiscount] = useState(0);\n  \n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [loadingData, setLoadingData] = useState(true);\n  \n  const router = useRouter();\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  const loadInitialData = async () => {\n    try {\n      const [customersData, productsData] = await Promise.all([\n        getCustomers(),\n        getProducts()\n      ]);\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (err) {\n      setError('Failed to load data. Please refresh the page.');\n      console.error(err);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const addProductToInvoice = () => {\n    if (!selectedProduct || productQuantity <= 0) {\n      setError('Please select a product and enter a valid quantity.');\n      return;\n    }\n\n    const product = products.find(p => p.id === parseInt(selectedProduct));\n    if (!product) {\n      setError('Selected product not found.');\n      return;\n    }\n\n    if (product.stock < productQuantity) {\n      setError(`Insufficient stock. Available: ${product.stock}`);\n      return;\n    }\n\n    // التحقق من عدم إضافة نفس المنتج مرتين\n    const existingItem = invoiceData.items.find(item => item.productId === product.id);\n    if (existingItem) {\n      setError('This product is already added to the invoice.');\n      return;\n    }\n\n    const unitPrice = productPrice || product.price;\n    const discount = productDiscount || 0;\n    const total = (parseFloat(unitPrice) * productQuantity) * (1 - discount / 100);\n\n    const newItem = {\n      productId: product.id,\n      product: product,\n      quantity: productQuantity,\n      unitPrice: parseFloat(unitPrice),\n      discount: discount,\n      total: total\n    };\n\n    setInvoiceData(prev => ({\n      ...prev,\n      items: [...prev.items, newItem]\n    }));\n\n    // إعادة تعيين النموذج\n    setSelectedProduct('');\n    setProductQuantity(1);\n    setProductPrice('');\n    setProductDiscount(0);\n    setError(null);\n  };\n\n  const removeProductFromInvoice = (index) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.filter((_, i) => i !== index)\n    }));\n  };\n\n  const updateItemQuantity = (index, quantity) => {\n    if (quantity <= 0) return;\n\n    const item = invoiceData.items[index];\n    const product = products.find(p => p.id === item.productId);\n    \n    if (product && product.stock < quantity) {\n      setError(`Insufficient stock for ${product.name}. Available: ${product.stock}`);\n      return;\n    }\n\n    const newTotal = (item.unitPrice * quantity) * (1 - item.discount / 100);\n\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.map((item, i) => \n        i === index \n          ? { ...item, quantity, total: newTotal }\n          : item\n      )\n    }));\n    setError(null);\n  };\n\n  const calculateTotals = () => {\n    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);\n    const discountAmount = subtotal * (invoiceData.discountRate / 100);\n    const subtotalAfterDiscount = subtotal - discountAmount;\n    const taxAmount = subtotalAfterDiscount * (invoiceData.taxRate / 100);\n    const total = subtotalAfterDiscount + taxAmount;\n\n    return {\n      subtotal,\n      discountAmount,\n      taxAmount,\n      total\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!invoiceData.customerId) {\n      setError('Please select a customer.');\n      return;\n    }\n\n    if (invoiceData.items.length === 0) {\n      setError('Please add at least one product to the invoice.');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const invoicePayload = {\n        customerId: parseInt(invoiceData.customerId),\n        items: invoiceData.items.map(item => ({\n          productId: item.productId,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          discount: item.discount\n        })),\n        taxRate: invoiceData.taxRate,\n        discountRate: invoiceData.discountRate,\n        notes: invoiceData.notes,\n        terms: invoiceData.terms,\n        dueDate: invoiceData.dueDate || null,\n        showImages: invoiceData.showImages\n      };\n\n      const result = await createInvoice(invoicePayload);\n      router.push(`/dashboard/invoices/${result.invoice.id}`);\n    } catch (err) {\n      setError(err.message || 'Failed to create invoice. Please try again.');\n      console.error('Invoice creation error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const totals = calculateTotals();\n\n  if (loadingData) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.back()}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                العودة\n              </button>\n              <div className=\"h-6 w-px bg-gray-300\"></div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">إنشاء فاتورة جديدة</h1>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* معلومات الفاتورة الأساسية */}\n            <div className=\"lg:col-span-2 space-y-6\">\n              {/* معلومات العميل */}\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4\">\n                  <h2 className=\"text-lg font-semibold text-white\">معلومات العميل</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div className=\"md:col-span-2\">\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        العميل *\n                      </label>\n                      <select\n                        value={invoiceData.customerId}\n                        onChange={(e) => handleInputChange('customerId', e.target.value)}\n                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n                        required\n                      >\n                        <option value=\"\">اختر العميل...</option>\n                        {customers.map(customer => (\n                          <option key={customer.id} value={customer.id}>\n                            {customer.name} - {customer.email}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        تاريخ الاستحقاق\n                      </label>\n                      <input\n                        type=\"date\"\n                        value={invoiceData.dueDate}\n                        onChange={(e) => handleInputChange('dueDate', e.target.value)}\n                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={invoiceData.showImages}\n                          onChange={(e) => handleInputChange('showImages', e.target.checked)}\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">إظهار صور المنتجات في الفاتورة</span>\n                      </label>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* إضافة المنتجات */}\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n                  <h2 className=\"text-lg font-semibold text-white\">إضافة المنتجات</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4\">\n                    <div className=\"md:col-span-2\">\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">المنتج</label>\n                      <select\n                        value={selectedProduct}\n                        onChange={(e) => {\n                          setSelectedProduct(e.target.value);\n                          const product = products.find(p => p.id === parseInt(e.target.value));\n                          if (product) {\n                            setProductPrice(product.price);\n                          }\n                        }}\n                        className=\"w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n                      >\n                        <option value=\"\">اختر المنتج...</option>\n                        {products.map(product => (\n                          <option key={product.id} value={product.id}>\n                            {product.name} - المخزون: {product.stock}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">الكمية</label>\n                      <input\n                        type=\"number\"\n                        min=\"1\"\n                        value={productQuantity}\n                        onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}\n                        className=\"w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">السعر</label>\n                      <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={productPrice}\n                        onChange={(e) => setProductPrice(e.target.value)}\n                        className=\"w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-semibold text-gray-700 mb-2\">خصم %</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        max=\"100\"\n                        step=\"0.01\"\n                        value={productDiscount}\n                        onChange={(e) => setProductDiscount(parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n                      />\n                    </div>\n                  </div>\n\n                  <button\n                    type=\"button\"\n                    onClick={addProductToInvoice}\n                    className=\"w-full md:w-auto px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200\"\n                  >\n                    <svg className=\"w-5 h-5 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                    إضافة المنتج\n                  </button>\n                </div>\n              </div>\n\n              {/* قائمة المنتجات المضافة */}\n              {invoiceData.items.length > 0 && (\n                <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                  <div className=\"bg-gradient-to-r from-purple-500 to-pink-600 px-6 py-4\">\n                    <h2 className=\"text-lg font-semibold text-white\">المنتجات المضافة</h2>\n                  </div>\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">المنتج</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">الكمية</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">السعر</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">خصم</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">المجموع</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">إجراءات</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {invoiceData.items.map((item, index) => (\n                          <tr key={index} className=\"hover:bg-gray-50\">\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <div className=\"flex items-center\">\n                                {item.product.images && item.product.images.length > 0 && (\n                                  <div className=\"flex-shrink-0 h-10 w-10 mr-3\">\n                                    <Image\n                                      src={`http://localhost:5000/${item.product.images[0].path}`}\n                                      alt={item.product.name}\n                                      width={40}\n                                      height={40}\n                                      className=\"h-10 w-10 rounded-lg object-cover\"\n                                    />\n                                  </div>\n                                )}\n                                <div>\n                                  <div className=\"text-sm font-medium text-gray-900\">{item.product.name}</div>\n                                  <div className=\"text-sm text-gray-500\">{item.product.category}</div>\n                                </div>\n                              </div>\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <input\n                                type=\"number\"\n                                min=\"1\"\n                                value={item.quantity}\n                                onChange={(e) => updateItemQuantity(index, parseInt(e.target.value) || 1)}\n                                className=\"w-20 px-2 py-1 border border-gray-300 rounded text-sm\"\n                              />\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              ${item.unitPrice.toFixed(2)}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {item.discount}%\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                              ${item.total.toFixed(2)}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                              <button\n                                type=\"button\"\n                                onClick={() => removeProductFromInvoice(index)}\n                                className=\"text-red-600 hover:text-red-900\"\n                              >\n                                حذف\n                              </button>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* الشريط الجانبي - الملخص والإعدادات */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"space-y-6\">\n                {/* ملخص الفاتورة */}\n                <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8\">\n                  <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n                    <h2 className=\"text-lg font-semibold text-white\">ملخص الفاتورة</h2>\n                  </div>\n                  <div className=\"p-6 space-y-4\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">المجموع الفرعي:</span>\n                      <span className=\"font-medium\">${totals.subtotal.toFixed(2)}</span>\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">خصم عام (%)</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        max=\"100\"\n                        step=\"0.01\"\n                        value={invoiceData.discountRate}\n                        onChange={(e) => handleInputChange('discountRate', parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                      />\n                    </div>\n                    \n                    {invoiceData.discountRate > 0 && (\n                      <div className=\"flex justify-between text-green-600\">\n                        <span>خصم:</span>\n                        <span>-${totals.discountAmount.toFixed(2)}</span>\n                      </div>\n                    )}\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">ضريبة (%)</label>\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        max=\"100\"\n                        step=\"0.01\"\n                        value={invoiceData.taxRate}\n                        onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                      />\n                    </div>\n                    \n                    {invoiceData.taxRate > 0 && (\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">ضريبة:</span>\n                        <span className=\"font-medium\">${totals.taxAmount.toFixed(2)}</span>\n                      </div>\n                    )}\n                    \n                    <hr />\n                    <div className=\"flex justify-between text-lg font-bold\">\n                      <span>المجموع الكلي:</span>\n                      <span className=\"text-indigo-600\">${totals.total.toFixed(2)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* ملاحظات وشروط */}\n                <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                  <div className=\"bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4\">\n                    <h2 className=\"text-lg font-semibold text-white\">ملاحظات وشروط</h2>\n                  </div>\n                  <div className=\"p-6 space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">ملاحظات</label>\n                      <textarea\n                        value={invoiceData.notes}\n                        onChange={(e) => handleInputChange('notes', e.target.value)}\n                        rows=\"3\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 resize-none\"\n                        placeholder=\"ملاحظات إضافية...\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">شروط الدفع</label>\n                      <textarea\n                        value={invoiceData.terms}\n                        onChange={(e) => handleInputChange('terms', e.target.value)}\n                        rows=\"3\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 resize-none\"\n                        placeholder=\"شروط وأحكام الدفع...\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* أزرار الحفظ */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:justify-end\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || invoiceData.items.length === 0}\n              className=\"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n            >\n              {loading ? (\n                <span className=\"flex items-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                  </svg>\n                  جاري إنشاء الفاتورة...\n                </span>\n              ) : (\n                <span className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  إنشاء الفاتورة\n                </span>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(CreateInvoicePage);\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;AAGjD;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,YAAY;QACZ,OAAO,EAAE;QACT,SAAS;QACT,cAAc;QACd,OAAO;QACP,OAAO;QACP,SAAS;QACT,YAAY;IACd;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,CAAC,eAAe,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,CAAA,GAAA,0GAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,0GAAA,CAAA,cAAW,AAAD;aACX;YACD,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAO;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,mBAAmB,mBAAmB,GAAG;YAC5C,SAAS;YACT;QACF;QAEA,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;QACrD,IAAI,CAAC,SAAS;YACZ,SAAS;YACT;QACF;QAEA,IAAI,QAAQ,KAAK,GAAG,iBAAiB;YACnC,SAAS,CAAC,+BAA+B,EAAE,QAAQ,KAAK,EAAE;YAC1D;QACF;QAEA,uCAAuC;QACvC,MAAM,eAAe,YAAY,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,QAAQ,EAAE;QACjF,IAAI,cAAc;YAChB,SAAS;YACT;QACF;QAEA,MAAM,YAAY,gBAAgB,QAAQ,KAAK;QAC/C,MAAM,WAAW,mBAAmB;QACpC,MAAM,QAAQ,AAAC,WAAW,aAAa,kBAAmB,CAAC,IAAI,WAAW,GAAG;QAE7E,MAAM,UAAU;YACd,WAAW,QAAQ,EAAE;YACrB,SAAS;YACT,UAAU;YACV,WAAW,WAAW;YACtB,UAAU;YACV,OAAO;QACT;QAEA,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO;uBAAI,KAAK,KAAK;oBAAE;iBAAQ;YACjC,CAAC;QAED,sBAAsB;QACtB,mBAAmB;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,SAAS;IACX;IAEA,MAAM,2BAA2B,CAAC;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC3C,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC,OAAO;QACjC,IAAI,YAAY,GAAG;QAEnB,MAAM,OAAO,YAAY,KAAK,CAAC,MAAM;QACrC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,SAAS;QAE1D,IAAI,WAAW,QAAQ,KAAK,GAAG,UAAU;YACvC,SAAS,CAAC,uBAAuB,EAAE,QAAQ,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,EAAE;YAC9E;QACF;QAEA,MAAM,WAAW,AAAC,KAAK,SAAS,GAAG,WAAY,CAAC,IAAI,KAAK,QAAQ,GAAG,GAAG;QAEvE,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,IAC3B,MAAM,QACF;wBAAE,GAAG,IAAI;wBAAE;wBAAU,OAAO;oBAAS,IACrC;YAER,CAAC;QACD,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW,YAAY,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QAC3E,MAAM,iBAAiB,WAAW,CAAC,YAAY,YAAY,GAAG,GAAG;QACjE,MAAM,wBAAwB,WAAW;QACzC,MAAM,YAAY,wBAAwB,CAAC,YAAY,OAAO,GAAG,GAAG;QACpE,MAAM,QAAQ,wBAAwB;QAEtC,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,YAAY,UAAU,EAAE;YAC3B,SAAS;YACT;QACF;QAEA,IAAI,YAAY,KAAK,CAAC,MAAM,KAAK,GAAG;YAClC,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,iBAAiB;gBACrB,YAAY,SAAS,YAAY,UAAU;gBAC3C,OAAO,YAAY,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACpC,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB,CAAC;gBACD,SAAS,YAAY,OAAO;gBAC5B,cAAc,YAAY,YAAY;gBACtC,OAAO,YAAY,KAAK;gBACxB,OAAO,YAAY,KAAK;gBACxB,SAAS,YAAY,OAAO,IAAI;gBAChC,YAAY,YAAY,UAAU;YACpC;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,0GAAA,CAAA,gBAAa,AAAD,EAAE;YACnC,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,OAAO,CAAC,EAAE,EAAE;QACxD,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;YACxB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;IAEf,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO,IAAI;oCAC1B,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAAiD;;;;;;sFAGlE,8OAAC;4EACC,OAAO,YAAY,UAAU;4EAC7B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4EAC/D,WAAU;4EACV,QAAQ;;8FAER,8OAAC;oFAAO,OAAM;8FAAG;;;;;;gFAChB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;wFAAyB,OAAO,SAAS,EAAE;;4FACzC,SAAS,IAAI;4FAAC;4FAAI,SAAS,KAAK;;uFADtB,SAAS,EAAE;;;;;;;;;;;;;;;;;8EAO9B,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAAiD;;;;;;sFAGlE,8OAAC;4EACC,MAAK;4EACL,OAAO,YAAY,OAAO;4EAC1B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4EAC5D,WAAU;;;;;;;;;;;;8EAId,8OAAC;8EACC,cAAA,8OAAC;wEAAM,WAAU;;0FACf,8OAAC;gFACC,MAAK;gFACL,SAAS,YAAY,UAAU;gFAC/B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,OAAO;gFACjE,WAAU;;;;;;0FAEZ,8OAAC;gFAAK,WAAU;0FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAM,WAAU;0FAAiD;;;;;;0FAClE,8OAAC;gFACC,OAAO;gFACP,UAAU,CAAC;oFACT,mBAAmB,EAAE,MAAM,CAAC,KAAK;oFACjC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK;oFACnE,IAAI,SAAS;wFACX,gBAAgB,QAAQ,KAAK;oFAC/B;gFACF;gFACA,WAAU;;kGAEV,8OAAC;wFAAO,OAAM;kGAAG;;;;;;oFAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;4FAAwB,OAAO,QAAQ,EAAE;;gGACvC,QAAQ,IAAI;gGAAC;gGAAa,QAAQ,KAAK;;2FAD7B,QAAQ,EAAE;;;;;;;;;;;;;;;;;kFAO7B,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAiD;;;;;;0FAClE,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,OAAO;gFACP,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gFAChE,WAAU;;;;;;;;;;;;kFAId,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAiD;;;;;;0FAClE,8OAAC;gFACC,MAAK;gFACL,MAAK;gFACL,OAAO;gFACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gFAC/C,WAAU;;;;;;;;;;;;kFAId,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAiD;;;;;;0FAClE,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,KAAI;gFACJ,MAAK;gFACL,OAAO;gFACP,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gFAClE,WAAU;;;;;;;;;;;;;;;;;;0EAKhB,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;wEAAsB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC7E,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;;;;;;;oEACjE;;;;;;;;;;;;;;;;;;;4CAOX,YAAY,KAAK,CAAC,MAAM,GAAG,mBAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEAAM,WAAU;8EACf,cAAA,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;0FAC/F,8OAAC;gFAAG,WAAU;0FAAiF;;;;;;;;;;;;;;;;;8EAGnG,8OAAC;oEAAM,WAAU;8EACd,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;4EAAe,WAAU;;8FACxB,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC;wFAAI,WAAU;;4FACZ,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,mBACnD,8OAAC;gGAAI,WAAU;0GACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oGACJ,KAAK,CAAC,sBAAsB,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE;oGAC3D,KAAK,KAAK,OAAO,CAAC,IAAI;oGACtB,OAAO;oGACP,QAAQ;oGACR,WAAU;;;;;;;;;;;0GAIhB,8OAAC;;kHACC,8OAAC;wGAAI,WAAU;kHAAqC,KAAK,OAAO,CAAC,IAAI;;;;;;kHACrE,8OAAC;wGAAI,WAAU;kHAAyB,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8FAInE,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC;wFACC,MAAK;wFACL,KAAI;wFACJ,OAAO,KAAK,QAAQ;wFACpB,UAAU,CAAC,IAAM,mBAAmB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wFACvE,WAAU;;;;;;;;;;;8FAGd,8OAAC;oFAAG,WAAU;;wFAAoD;wFAC9D,KAAK,SAAS,CAAC,OAAO,CAAC;;;;;;;8FAE3B,8OAAC;oFAAG,WAAU;;wFACX,KAAK,QAAQ;wFAAC;;;;;;;8FAEjB,8OAAC;oFAAG,WAAU;;wFAAgE;wFAC1E,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;8FAEvB,8OAAC;oFAAG,WAAU;8FACZ,cAAA,8OAAC;wFACC,MAAK;wFACL,SAAS,IAAM,yBAAyB;wFACxC,WAAU;kGACX;;;;;;;;;;;;2EA3CI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyDvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;;;;;;sEAEnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;;gFAAc;gFAAE,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAG1D,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAChE,8OAAC;4EACC,MAAK;4EACL,KAAI;4EACJ,KAAI;4EACJ,MAAK;4EACL,OAAO,YAAY,YAAY;4EAC/B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACjF,WAAU;;;;;;;;;;;;gEAIb,YAAY,YAAY,GAAG,mBAC1B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;;gFAAK;gFAAG,OAAO,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAI3C,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAChE,8OAAC;4EACC,MAAK;4EACL,KAAI;4EACJ,KAAI;4EACJ,MAAK;4EACL,OAAO,YAAY,OAAO;4EAC1B,UAAU,CAAC,IAAM,kBAAkB,WAAW,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC5E,WAAU;;;;;;;;;;;;gEAIb,YAAY,OAAO,GAAG,mBACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;;gFAAc;gFAAE,OAAO,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAI7D,8OAAC;;;;;8EACD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,WAAU;;gFAAkB;gFAAE,OAAO,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8DAM/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;;;;;;sEAEnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAChE,8OAAC;4EACC,OAAO,YAAY,KAAK;4EACxB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC1D,MAAK;4EACL,WAAU;4EACV,aAAY;;;;;;;;;;;;8EAIhB,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAChE,8OAAC;4EACC,OAAO,YAAY,KAAK;4EACxB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC1D,MAAK;4EACL,WAAU;4EACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,MAAK;wCACL,UAAU,WAAW,YAAY,KAAK,CAAC,MAAM,KAAK;wCAClD,WAAU;kDAET,wBACC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;oDAA6C,OAAM;oDAA6B,MAAK;oDAAO,SAAQ;;sEACjH,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAC/C;;;;;;iEAIR,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}