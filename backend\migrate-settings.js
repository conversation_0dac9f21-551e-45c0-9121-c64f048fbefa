// backend/migrate-settings.js
const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// فتح قاعدة البيانات
const db = new Database('database.db');

try {
  // قراءة ملف الهجرة
  const migrationSQL = fs.readFileSync(path.join(__dirname, 'migrations', '006_create_settings_table.sql'), 'utf8');
  
  // تنفيذ الهجرة
  db.exec(migrationSQL);
  
  console.log('✅ تم إنشاء جدول الإعدادات بنجاح!');
  
  // التحقق من البيانات
  const count = db.prepare('SELECT COUNT(*) as count FROM settings').get();
  console.log(`📊 تم إدراج ${count.count} إعداد افتراضي`);
  
} catch (error) {
  console.error('❌ خطأ في تطبيق الهجرة:', error.message);
} finally {
  db.close();
}
