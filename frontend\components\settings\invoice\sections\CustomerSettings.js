// frontend/components/settings/invoice/sections/CustomerSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function CustomerSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const customerFields = [
    { field: 'showCustomerName', label: 'اسم العميل', description: 'عرض اسم العميل' },
    { field: 'showCustomerAddress', label: 'عنوان العميل', description: 'عرض عنوان العميل' },
    { field: 'showCustomerPhone', label: 'هاتف العميل', description: 'عرض رقم هاتف العميل' },
    { field: 'showCustomerEmail', label: 'بريد العميل', description: 'عرض البريد الإلكتروني للعميل' }
  ];

  return (
    <SectionHeader
      title="معلومات العميل"
      section="customer"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      }
    >
      {customerFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
