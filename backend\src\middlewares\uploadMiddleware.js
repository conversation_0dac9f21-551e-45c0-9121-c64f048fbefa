// backend/src/middlewares/uploadMiddleware.js
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');

// إنشاء مجلد الصور إذا لم يكن موجوداً
const createUploadDir = async (dir) => {
  try {
    await fs.access(dir);
  } catch {
    await fs.mkdir(dir, { recursive: true });
  }
};

// إعداد التخزين
const storage = multer.memoryStorage();

// فلتر أنواع الملفات المسموحة
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم. الأنواع المسموحة: JPEG, PNG, GIF, WebP'), false);
  }
};

// إعداد multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB حد أقصى
  },
});

// معالجة وحفظ الصور
const processAndSaveImage = async (file, productId) => {
  const uploadsDir = path.join(__dirname, '../../uploads/products');
  const thumbnailsDir = path.join(__dirname, '../../uploads/products/thumbnails');
  
  // إنشاء المجلدات
  await createUploadDir(uploadsDir);
  await createUploadDir(thumbnailsDir);
  
  // إنشاء اسم ملف فريد
  const fileExtension = path.extname(file.originalname);
  const uniqueFilename = `${productId}_${crypto.randomUUID()}${fileExtension}`;
  
  const imagePath = path.join(uploadsDir, uniqueFilename);
  const thumbnailPath = path.join(thumbnailsDir, `thumb_${uniqueFilename}`);
  
  try {
    // معالجة الصورة الأصلية
    await sharp(file.buffer)
      .resize(800, 600, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 85 })
      .toFile(imagePath);
    
    // إنشاء صورة مصغرة
    await sharp(file.buffer)
      .resize(200, 150, { 
        fit: 'cover' 
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);
    
    return {
      filename: uniqueFilename,
      originalName: file.originalname,
      path: `uploads/products/${uniqueFilename}`,
      thumbnailPath: `uploads/products/thumbnails/thumb_${uniqueFilename}`,
      size: file.size,
      mimeType: file.mimetype,
    };
  } catch (error) {
    console.error('خطأ في معالجة الصورة:', error);
    throw new Error('فشل في معالجة الصورة');
  }
};

// حذف الصور
const deleteImage = async (imagePath) => {
  try {
    const fullPath = path.join(__dirname, '../../', imagePath);
    const thumbnailPath = path.join(
      path.dirname(fullPath), 
      'thumbnails', 
      `thumb_${path.basename(fullPath)}`
    );
    
    // حذف الصورة الأصلية
    try {
      await fs.unlink(fullPath);
    } catch (error) {
      console.log('الصورة الأصلية غير موجودة:', fullPath);
    }
    
    // حذف الصورة المصغرة
    try {
      await fs.unlink(thumbnailPath);
    } catch (error) {
      console.log('الصورة المصغرة غير موجودة:', thumbnailPath);
    }
  } catch (error) {
    console.error('خطأ في حذف الصورة:', error);
  }
};

module.exports = {
  upload,
  processAndSaveImage,
  deleteImage,
};
