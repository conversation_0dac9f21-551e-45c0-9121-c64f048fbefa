'use client';

import { useState } from 'react';
import Link from 'next/link';
import withAuth from '@/components/withAuth';
import DashboardLayout from '@/components/DashboardLayout';
import TemplatePreview from '@/components/invoice-templates/TemplatePreview';

function InvoiceTemplatesPage() {
  const [selectedTemplate, setSelectedTemplate] = useState('classic');
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSelectTemplate = (templateId) => {
    setSelectedTemplate(templateId);
    setShowSuccess(true);
    
    // Save to localStorage for now (later can be saved to user preferences)
    localStorage.setItem('selectedInvoiceTemplate', templateId);
    
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <nav className="flex" aria-label="Breadcrumb">
                  <ol className="flex items-center space-x-4">
                    <li>
                      <Link href="/dashboard" className="text-gray-400 hover:text-gray-500">
                        <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                      </Link>
                    </li>
                    <li>
                      <div className="flex items-center">
                        <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="ml-4 text-sm font-medium text-gray-900">قوالب الفواتير</span>
                      </div>
                    </li>
                  </ol>
                </nav>
                <h1 className="mt-2 text-3xl font-bold text-gray-900">قوالب الفواتير</h1>
                <p className="mt-1 text-sm text-gray-600">
                  اختر القالب المناسب لفواتيرك من مجموعة متنوعة من التصاميم الاحترافية
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                <Link
                  href="/dashboard/invoices/create"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  إنشاء فاتورة جديدة
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Success Message */}
          {showSuccess && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                <p className="text-green-800 font-medium">
                  تم حفظ اختيار القالب بنجاح! سيتم استخدام هذا القالب في الفواتير الجديدة.
                </p>
              </div>
            </div>
          )}

          {/* Templates Info */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">اختر قالب الفاتورة المناسب</h2>
                <p className="text-gray-600 mb-4">
                  نوفر لك مجموعة متنوعة من قوالب الفواتير الاحترافية التي تناسب مختلف أنواع الأعمال. 
                  يمكنك معاينة كل قالب بالتفصيل قبل اختياره.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700">تصاميم احترافية متنوعة</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700">معاينة كاملة قبل الاختيار</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700">سهولة التغيير في أي وقت</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Current Selection */}
          {selectedTemplate && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
              <div className="flex items-center gap-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-blue-800 font-medium">
                  القالب المحدد حالياً: <span className="font-bold">
                    {selectedTemplate === 'classic' && 'القالب الكلاسيكي'}
                    {selectedTemplate === 'modern' && 'القالب الحديث'}
                    {selectedTemplate === 'elegant' && 'القالب الأنيق'}
                    {selectedTemplate === 'minimal' && 'القالب البسيط'}
                  </span>
                </span>
              </div>
            </div>
          )}

          {/* Templates Grid */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">القوالب المتاحة</h3>
            
            <TemplatePreview
              onSelectTemplate={handleSelectTemplate}
              selectedTemplate={selectedTemplate}
            />
          </div>

          {/* Help Section */}
          <div className="mt-8 bg-gray-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">نصائح لاختيار القالب المناسب</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">للشركات التقليدية</h4>
                <p className="text-gray-600 text-sm">
                  ننصح باستخدام القالب الكلاسيكي أو الأنيق للحصول على مظهر رسمي ومحترف.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-800 mb-2">للشركات التقنية</h4>
                <p className="text-gray-600 text-sm">
                  القالب الحديث مناسب للشركات التقنية والناشئة بتصميمه العصري والجذاب.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-800 mb-2">للاستخدام العام</h4>
                <p className="text-gray-600 text-sm">
                  القالب البسيط مناسب لجميع أنواع الأعمال ويركز على وضوح المعلومات.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-800 mb-2">للشركات الفاخرة</h4>
                <p className="text-gray-600 text-sm">
                  القالب الأنيق يوفر لمسة راقية مع التفاصيل الذهبية للشركات الفاخرة.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(InvoiceTemplatesPage);
