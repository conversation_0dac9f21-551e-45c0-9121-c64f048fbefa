'use client';

import withAuth from '@/components/withAuth';
import DashboardLayout from '@/components/DashboardLayout';
import InvoiceSettingsDemo from '@/components/demo/InvoiceSettingsDemo';

function DemoPage() {
  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🧪 العرض التوضيحي</h1>
                <p className="mt-1 text-sm text-gray-600">
                  اختبر كيفية عمل نظام إعدادات الفواتير وتأثيرها على القوالب
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="py-8">
          <InvoiceSettingsDemo />
        </div>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(DemoPage);
