// frontend/components/settings/SettingsTestPanel.js
'use client';

import { useState } from 'react';
import settingsService from '@/lib/settingsService';

export default function SettingsTestPanel() {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const data = await settingsService.loadSettings();
      setSettings(data);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveTestSettings = async () => {
    setLoading(true);
    try {
      await settingsService.saveCompanySettings({
        companyName: 'شركة الاختبار المحدودة',
        companyAddress: 'الرياض، المملكة العربية السعودية',
        companyPhone: '+966 11 555 0123',
        companyEmail: '<EMAIL>',
        companyWebsite: 'www.testcompany.com',
        taxId: '300123456789003'
      });

      await settingsService.saveInvoiceDisplaySettings({
        showCompanyLogo: true,
        showCompanyName: true,
        showCompanyAddress: true,
        showCompanyPhone: true,
        showCompanyEmail: true,
        showTaxId: true,
        showInvoiceNumber: true,
        showInvoiceDate: true,
        showCustomerName: true,
        showProductDescription: true,
        showQuantity: true,
        showUnitPrice: true,
        showTotalPrice: true,
        showSubtotal: true,
        showTaxAmount: true,
        showTotalAmount: true,
        showNotes: true,
        showFooter: true
      });

      alert('تم حفظ الإعدادات التجريبية بنجاح!');
      loadSettings();
    } catch (error) {
      console.error('Error saving test settings:', error);
      alert('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">🧪 لوحة اختبار الإعدادات</h3>
      
      <div className="flex gap-4 mb-4">
        <button
          onClick={loadSettings}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? 'جاري التحميل...' : 'تحميل الإعدادات'}
        </button>
        
        <button
          onClick={saveTestSettings}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400"
        >
          {loading ? 'جاري الحفظ...' : 'حفظ إعدادات تجريبية'}
        </button>
      </div>

      {settings && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold mb-2">الإعدادات المحملة:</h4>
          <pre className="text-xs overflow-auto max-h-64 bg-white p-2 rounded border">
            {JSON.stringify(settings, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
