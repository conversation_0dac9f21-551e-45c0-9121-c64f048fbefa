// frontend/components/settings/invoice/sections/HeaderSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function HeaderSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const headerFields = [
    { field: 'showCompanyLogo', label: 'شعار الشركة', description: 'عرض شعار الشركة في الرأس' },
    { field: 'showCompanyName', label: 'اسم الشركة', description: 'عرض اسم الشركة' },
    { field: 'showCompanyAddress', label: 'عنوان الشركة', description: 'عرض عنوان الشركة' },
    { field: 'showCompanyPhone', label: 'هاتف الشركة', description: 'عرض رقم هاتف الشركة' },
    { field: 'showCompanyEmail', label: 'بريد الشركة', description: 'عرض البريد الإلكتروني' },
    { field: 'showCompanyWebsite', label: 'موقع الشركة', description: 'عرض موقع الشركة الإلكتروني' },
    { field: 'showTaxId', label: 'الرقم الضريبي', description: 'عرض الرقم الضريبي للشركة' }
  ];

  return (
    <SectionHeader
      title="رأس الفاتورة"
      section="header"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
        </svg>
      }
    >
      {headerFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
