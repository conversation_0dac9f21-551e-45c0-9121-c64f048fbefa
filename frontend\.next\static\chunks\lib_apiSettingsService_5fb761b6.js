(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/apiSettingsService.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/lib/apiSettingsService.js
// خدمة إدارة الإعدادات عبر API
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
class ApiSettingsService {
    constructor(){
        this.apiUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';
        this.fallbackService = null; // للعودة إلى localStorage في حالة فشل API
    }
    // تحميل الإعدادات من API
    async loadSettings() {
        try {
            const response = await fetch(`${this.apiUrl}/settings`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const settings = await response.json();
            // حفظ نسخة احتياطية في localStorage
            localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(settings));
            return settings;
        } catch (error) {
            console.error('Error loading settings from API:', error);
            // محاولة تحميل من localStorage كبديل
            try {
                const backup = localStorage.getItem('invoiceAppSettings_backup');
                if (backup) {
                    console.log('Using backup settings from localStorage');
                    return JSON.parse(backup);
                }
            } catch (localError) {
                console.error('Error loading backup settings:', localError);
            }
            // إرجاع إعدادات افتراضية
            return this.getDefaultSettings();
        }
    }
    // حفظ إعدادات الشركة
    async saveCompanySettings(companySettings) {
        try {
            // معالجة رفع الصورة إذا كانت موجودة
            if (companySettings.logoFile) {
                const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);
                companySettings.logoUrl = logoUrl;
                delete companySettings.logoFile;
            }
            const response = await fetch(`${this.apiUrl}/settings/company`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(companySettings)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            // تحديث النسخة الاحتياطية
            const currentSettings = await this.loadSettings();
            currentSettings.company = {
                ...currentSettings.company,
                ...companySettings
            };
            localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));
            return result;
        } catch (error) {
            console.error('Error saving company settings:', error);
            throw error;
        }
    }
    // حفظ إعدادات الفواتير العامة
    async saveInvoiceSettings(invoiceSettings) {
        try {
            const response = await fetch(`${this.apiUrl}/settings/invoice`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(invoiceSettings)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            // تحديث النسخة الاحتياطية
            const currentSettings = await this.loadSettings();
            currentSettings.invoice = {
                ...currentSettings.invoice,
                ...invoiceSettings
            };
            localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));
            return result;
        } catch (error) {
            console.error('Error saving invoice settings:', error);
            throw error;
        }
    }
    // حفظ إعدادات عرض الفاتورة
    async saveInvoiceDisplaySettings(displaySettings) {
        try {
            const response = await fetch(`${this.apiUrl}/settings/invoice/display`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(displaySettings)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            // تحديث النسخة الاحتياطية
            const currentSettings = await this.loadSettings();
            if (!currentSettings.invoice) currentSettings.invoice = {};
            currentSettings.invoice.display = displaySettings;
            localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));
            return result;
        } catch (error) {
            console.error('Error saving display settings:', error);
            throw error;
        }
    }
    // رفع شعار الشركة
    async uploadCompanyLogo(file) {
        return new Promise((resolve, reject)=>{
            const reader = new FileReader();
            reader.onload = (e)=>{
                const logoUrl = e.target.result;
                // حفظ في localStorage مؤقتاً
                localStorage.setItem('companyLogo', logoUrl);
                resolve(logoUrl);
            };
            reader.onerror = ()=>reject(new Error('فشل في قراءة الملف'));
            reader.readAsDataURL(file);
        });
    }
    // الحصول على شعار الشركة
    getCompanyLogo() {
        return localStorage.getItem('companyLogo');
    }
    // الحصول على إعدادات الشركة
    async getCompanySettings() {
        const settings = await this.loadSettings();
        return settings.company || {};
    }
    // الحصول على إعدادات الفواتير
    async getInvoiceSettings() {
        const settings = await this.loadSettings();
        return settings.invoice || {};
    }
    // الحصول على إعدادات عرض الفاتورة
    async getInvoiceDisplaySettings() {
        const settings = await this.loadSettings();
        return settings.invoice?.display || {};
    }
    // الإعدادات الافتراضية
    getDefaultSettings() {
        return {
            company: {
                companyName: '',
                companyAddress: '',
                companyPhone: '',
                companyEmail: '',
                companyWebsite: '',
                taxId: '',
                currency: 'SAR',
                logoUrl: null
            },
            invoice: {
                invoicePrefix: 'INV',
                invoiceNumberLength: 6,
                defaultTaxRate: 15,
                defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
                autoGenerateInvoiceNumber: true,
                showImagesInInvoice: true,
                allowPartialPayments: true,
                requireCustomerInfo: true,
                defaultDueDays: 30,
                invoiceFooter: '',
                invoiceNotes: '',
                display: {
                    // رأس الفاتورة
                    showCompanyLogo: true,
                    showCompanyName: true,
                    showCompanyAddress: true,
                    showCompanyPhone: true,
                    showCompanyEmail: true,
                    showCompanyWebsite: true,
                    showTaxId: true,
                    // معلومات الفاتورة
                    showInvoiceNumber: true,
                    showInvoiceDate: true,
                    showDueDate: true,
                    showPaymentTerms: true,
                    // معلومات العميل
                    showCustomerName: true,
                    showCustomerAddress: true,
                    showCustomerPhone: true,
                    showCustomerEmail: true,
                    // عناصر الجدول
                    showProductImages: true,
                    showProductCode: true,
                    showProductDescription: true,
                    showQuantity: true,
                    showUnitPrice: true,
                    showDiscount: false,
                    showTotalPrice: true,
                    showItemNumbers: true,
                    // المجاميع
                    showSubtotal: true,
                    showTaxAmount: true,
                    showDiscountAmount: false,
                    showTotalAmount: true,
                    // التذييل
                    showNotes: true,
                    showFooter: true,
                    showSignature: false,
                    showQRCode: false,
                    showBankDetails: false,
                    showPaymentInstructions: true
                }
            }
        };
    }
    // فحص حالة الاتصال بـ API
    async checkApiConnection() {
        try {
            const response = await fetch(`${this.apiUrl}/settings`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }
}
// إنشاء مثيل واحد للخدمة
const apiSettingsService = new ApiSettingsService();
const __TURBOPACK__default__export__ = apiSettingsService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=lib_apiSettingsService_5fb761b6.js.map