// frontend/lib/settingsService.js

// خدمة إدارة الإعدادات
class SettingsService {
  constructor() {
    this.storageKey = 'invoiceAppSettings';
    this.defaultSettings = this.getDefaultSettings();
  }

  // الحصول على الإعدادات الافتراضية
  getDefaultSettings() {
    return {
      company: {
        companyName: '',
        companyAddress: '',
        companyPhone: '',
        companyEmail: '',
        companyWebsite: '',
        taxId: '',
        currency: 'SAR',
        logoUrl: null
      },
      invoice: {
        invoicePrefix: 'INV',
        invoiceNumberLength: 6,
        defaultTaxRate: 15,
        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
        autoGenerateInvoiceNumber: true,
        showImagesInInvoice: true,
        allowPartialPayments: true,
        requireCustomerInfo: true,
        defaultDueDays: 30,
        invoiceFooter: '',
        invoiceNotes: '',
        // إعدادات العرض
        display: {
          // عناصر الرأس
          showCompanyLogo: true,
          showCompanyName: true,
          showCompanyAddress: true,
          showCompanyPhone: true,
          showCompanyEmail: true,
          showCompanyWebsite: true,
          showTaxId: true,

          // معلومات الفاتورة
          showInvoiceNumber: true,
          showInvoiceDate: true,
          showDueDate: true,
          showPaymentTerms: true,

          // معلومات العميل
          showCustomerName: true,
          showCustomerAddress: true,
          showCustomerPhone: true,
          showCustomerEmail: true,

          // عناصر الجدول
          showProductImages: true,
          showProductCode: true,
          showProductDescription: true,
          showQuantity: true,
          showUnitPrice: true,
          showDiscount: false,
          showTotalPrice: true,
          showItemNumbers: true,

          // المجاميع
          showSubtotal: true,
          showTaxAmount: true,
          showDiscountAmount: false,
          showTotalAmount: true,

          // التذييل
          showNotes: true,
          showFooter: true,
          showSignature: false,
          showQRCode: false,
          showBankDetails: false,
          showPaymentInstructions: true
        }
      }
    };
  }

  // تحميل الإعدادات
  async loadSettings() {
    try {
      // محاولة تحميل من localStorage أولاً
      const localSettings = localStorage.getItem(this.storageKey);
      if (localSettings) {
        const parsed = JSON.parse(localSettings);
        return this.mergeWithDefaults(parsed);
      }

      // في المستقبل: تحميل من API
      // const response = await fetch('/api/settings');
      // const settings = await response.json();
      // return this.mergeWithDefaults(settings);

      return this.defaultSettings;
    } catch (error) {
      console.error('Error loading settings:', error);
      return this.defaultSettings;
    }
  }

  // حفظ الإعدادات
  async saveSettings(settings) {
    try {
      const mergedSettings = this.mergeWithDefaults(settings);

      // حفظ في localStorage
      localStorage.setItem(this.storageKey, JSON.stringify(mergedSettings));

      // في المستقبل: حفظ في API
      // await fetch('/api/settings', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(mergedSettings)
      // });

      return mergedSettings;
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }

  // دمج الإعدادات مع الافتراضية
  mergeWithDefaults(settings) {
    return {
      company: { ...this.defaultSettings.company, ...settings.company },
      invoice: {
        ...this.defaultSettings.invoice,
        ...settings.invoice,
        display: {
          ...this.defaultSettings.invoice.display,
          ...settings.invoice?.display
        }
      }
    };
  }

  // الحصول على إعدادات الشركة
  async getCompanySettings() {
    const settings = await this.loadSettings();
    return settings.company;
  }

  // الحصول على إعدادات الفواتير
  async getInvoiceSettings() {
    const settings = await this.loadSettings();
    return settings.invoice;
  }

  // الحصول على إعدادات عرض الفواتير
  async getInvoiceDisplaySettings() {
    const settings = await this.loadSettings();
    return settings.invoice.display;
  }

  // حفظ إعدادات الشركة
  async saveCompanySettings(companySettings) {
    const currentSettings = await this.loadSettings();

    // معالجة رفع الصورة إذا كانت موجودة
    if (companySettings.logoFile) {
      try {
        const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);
        companySettings.logoUrl = logoUrl;
        delete companySettings.logoFile; // إزالة الملف من البيانات
      } catch (error) {
        console.error('Error uploading logo:', error);
        throw new Error('فشل في رفع شعار الشركة');
      }
    }

    return this.saveSettings({
      ...currentSettings,
      company: companySettings
    });
  }

  // رفع شعار الشركة
  async uploadCompanyLogo(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoUrl = e.target.result;
        // حفظ في localStorage مؤقتاً
        localStorage.setItem('companyLogo', logoUrl);
        resolve(logoUrl);
      };
      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
      reader.readAsDataURL(file);
    });
  }

  // الحصول على شعار الشركة
  getCompanyLogo() {
    return localStorage.getItem('companyLogo');
  }

  // حفظ إعدادات الفواتير
  async saveInvoiceSettings(invoiceSettings) {
    const currentSettings = await this.loadSettings();
    return this.saveSettings({
      ...currentSettings,
      invoice: {
        ...currentSettings.invoice,
        ...invoiceSettings
      }
    });
  }

  // حفظ إعدادات عرض الفواتير
  async saveInvoiceDisplaySettings(displaySettings) {
    const currentSettings = await this.loadSettings();
    return this.saveSettings({
      ...currentSettings,
      invoice: {
        ...currentSettings.invoice,
        display: displaySettings
      }
    });
  }
}

// إنشاء مثيل واحد للخدمة
const settingsService = new SettingsService();

export default settingsService;
