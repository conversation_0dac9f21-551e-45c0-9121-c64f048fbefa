// backend/src/routes/invoiceRoutes.js
const express = require('express');
const router = express.Router();
const { protect } = require('../middlewares/authMiddleware');
const {
  getInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  addPayment,
  getInvoiceStats
} = require('../controllers/invoiceController');

// إحصائيات الفواتير
router.get('/stats', protect, getInvoiceStats);

// الحصول على جميع الفواتير
router.get('/', protect, getInvoices);

// إنشاء فاتورة جديدة
router.post('/', protect, createInvoice);

// الحصول على فاتورة محددة
router.get('/:id', protect, getInvoiceById);

// تحديث فاتورة
router.put('/:id', protect, updateInvoice);

// حذف فاتورة
router.delete('/:id', protect, deleteInvoice);

// إضافة دفعة لفاتورة
router.post('/:id/payments', protect, addPayment);

module.exports = router;
