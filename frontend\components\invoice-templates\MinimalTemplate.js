// frontend/components/invoice-templates/MinimalTemplate.js
'use client';

import React from 'react';

export default function MinimalTemplate({ invoice, preview = false }) {
  const sampleData = {
    invoiceNumber: 'INV-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customerName: 'شركة البساطة للأعمال',
    customerDetails: 'الحلول البسيطة والفعالة',
    items: [
      { id: 1, description: 'خدمة استشارية', quantity: 2, price: 500.00, total: 1000.00 },
      { id: 2, description: 'تطوير موقع إلكتروني', quantity: 1, price: 3000.00, total: 3000.00 },
      { id: 3, description: 'صيانة شهرية', quantity: 6, price: 200.00, total: 1200.00 }
    ],
    subtotal: 5200.00,
    tax: 780.00,
    total: 5980.00,
    notes: 'شكراً لاختياركم خدماتنا',
    companyName: 'شركة البساطة للأعمال',
    companyDetails: 'الحلول البسيطة والفعالة'
  };

  const data = preview ? sampleData : invoice;

  return (
    <div className="bg-white p-8" style={{ minHeight: '297mm', width: '210mm' }}>
      {/* Minimal Header */}
      <div className="border-b-4 border-gray-800 pb-6 mb-8">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{data.companyName}</h1>
            <p className="text-gray-600">{data.companyDetails}</p>
          </div>

          <div className="text-right">
            <h2 className="text-4xl font-bold text-gray-800 mb-2">فاتورة</h2>
            <p className="text-gray-600 text-lg">#{data.invoiceNumber}</p>
          </div>
        </div>
      </div>

      {/* Customer and Invoice Info */}
      <div className="grid grid-cols-2 gap-12 mb-8">
        <div>
          <h3 className="text-lg font-bold text-gray-800 mb-3">إلى:</h3>
          <div className="space-y-1">
            <p className="text-gray-800 font-semibold">{data.customerName}</p>
            <p className="text-gray-600">{data.customerDetails}</p>
          </div>
        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">تاريخ الفاتورة:</span>
              <span className="font-semibold">{data.date}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">رقم الفاتورة:</span>
              <span className="font-semibold">{data.invoiceNumber}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Simple Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b-2 border-gray-800">
              <th className="py-3 text-right font-bold text-gray-800">المبلغ</th>
              <th className="py-3 text-right font-bold text-gray-800">السعر</th>
              <th className="py-3 text-right font-bold text-gray-800">الكمية</th>
              <th className="py-3 text-right font-bold text-gray-800">الوصف</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className="border-b border-gray-200">
                <td className="py-4 text-right font-semibold">{(Number(item.total) || 0).toFixed(2)} ر.س</td>
                <td className="py-4 text-right">{(Number(item.price) || 0).toFixed(2)} ر.س</td>
                <td className="py-4 text-right">{item.quantity || 0}</td>
                <td className="py-4 text-right">{item.description || ''}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Simple Totals */}
      <div className="flex justify-between items-start mb-12">
        <div className="w-1/2">
          <h4 className="text-lg font-bold text-gray-800 mb-3">ملاحظات:</h4>
          <p className="text-gray-700">{data.notes}</p>

          <div className="mt-6">
            <h5 className="font-bold text-gray-800 mb-2">شروط الدفع:</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• الدفع خلال 30 يوم</li>
              <li>• تطبق غرامة تأخير 2% شهرياً</li>
            </ul>
          </div>
        </div>

        <div className="w-80">
          <div className="border-2 border-gray-800 p-6">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-700">المجموع الفرعي:</span>
                <span className="font-semibold">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">الضريبة (15%):</span>
                <span className="font-semibold">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>
              </div>
              <div className="border-t-2 border-gray-800 pt-3">
                <div className="flex justify-between text-xl font-bold">
                  <span>الإجمالي:</span>
                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Simple Footer */}
      <div className="border-t border-gray-300 pt-6">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            <p>الهاتف: +966 50 123 4567 | البريد: <EMAIL> | الموقع: www.simple.com</p>
          </div>

          <div className="text-center">
            <div className="w-24 h-12 border border-gray-400 flex items-center justify-center text-gray-500 text-sm">
              الشعار
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
