{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.10.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "sharp": "^0.34.2"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.10.1"}}