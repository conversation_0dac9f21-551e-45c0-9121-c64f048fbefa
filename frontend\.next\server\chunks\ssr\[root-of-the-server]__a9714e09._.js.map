{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/StatsCard.js"], "sourcesContent": ["// frontend/components/dashboard/StatsCard.js\n'use client';\n\nexport default function StatsCard({ \n  title, \n  value, \n  icon, \n  color = 'blue', \n  trend = null,\n  loading = false \n}) {\n  const colorClasses = {\n    blue: 'bg-blue-100 text-blue-600',\n    green: 'bg-green-100 text-green-600',\n    yellow: 'bg-yellow-100 text-yellow-600',\n    red: 'bg-red-100 text-red-600',\n    purple: 'bg-purple-100 text-purple-600',\n    indigo: 'bg-indigo-100 text-indigo-600'\n  };\n\n  const trendColors = {\n    up: 'text-green-600',\n    down: 'text-red-600',\n    neutral: 'text-gray-600'\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"p-6 animate-pulse\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n            <div className=\"ml-4 flex-1\">\n              <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-6 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300\">\n      <div className=\"p-6\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>\n              {icon}\n            </div>\n          </div>\n          <div className=\"ml-4 flex-1\">\n            <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n            <div className=\"flex items-center\">\n              <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n              {trend && (\n                <div className={`ml-2 flex items-center text-sm ${trendColors[trend.direction]}`}>\n                  {trend.direction === 'up' && (\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 17l9.2-9.2M17 17V7H7\" />\n                    </svg>\n                  )}\n                  {trend.direction === 'down' && (\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 7l-9.2 9.2M7 7v10h10\" />\n                    </svg>\n                  )}\n                  <span>{trend.value}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAC7C;;AAEe,SAAS,UAAU,EAChC,KAAK,EACL,KAAK,EACL,IAAI,EACJ,QAAQ,MAAM,EACd,QAAQ,IAAI,EACZ,UAAU,KAAK,EAChB;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,MAAM;QACN,SAAS;IACX;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,sDAAsD,EAAE,YAAY,CAAC,MAAM,EAAE;sCAC3F;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAChD,uBACC,8OAAC;wCAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW,CAAC,MAAM,SAAS,CAAC,EAAE;;4CAC7E,MAAM,SAAS,KAAK,sBACnB,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CAGxE,MAAM,SAAS,KAAK,wBACnB,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAGzE,8OAAC;0DAAM,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/ChartCard.js"], "sourcesContent": ["// frontend/components/dashboard/ChartCard.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function ChartCard({ \n  title, \n  data = [], \n  type = 'bar',\n  color = 'blue',\n  loading = false \n}) {\n  const [timeRange, setTimeRange] = useState('7d');\n\n  const colorClasses = {\n    blue: 'from-blue-500 to-cyan-600',\n    green: 'from-green-500 to-emerald-600',\n    purple: 'from-purple-500 to-pink-600',\n    orange: 'from-orange-500 to-red-600'\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4\">\n          <div className=\"h-6 bg-gray-300 rounded w-1/3\"></div>\n        </div>\n        <div className=\"p-6 animate-pulse\">\n          <div className=\"space-y-3\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-gray-200 rounded\"></div>\n                <div className=\"flex-1 h-4 bg-gray-200 rounded\"></div>\n                <div className=\"w-16 h-4 bg-gray-200 rounded\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const maxValue = Math.max(...data.map(item => item.value));\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className={`bg-gradient-to-r ${colorClasses[color]} px-6 py-4`}>\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-white\">{title}</h3>\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value)}\n            className=\"bg-white bg-opacity-20 text-white text-sm rounded-lg px-3 py-1 border border-white border-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\"\n          >\n            <option value=\"7d\" className=\"text-gray-900\">آخر 7 أيام</option>\n            <option value=\"30d\" className=\"text-gray-900\">آخر 30 يوم</option>\n            <option value=\"90d\" className=\"text-gray-900\">آخر 3 أشهر</option>\n            <option value=\"1y\" className=\"text-gray-900\">آخر سنة</option>\n          </select>\n        </div>\n      </div>\n      \n      <div className=\"p-6\">\n        {data.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <p className=\"text-gray-500\">لا توجد بيانات للعرض</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {data.map((item, index) => (\n              <div key={index} className=\"flex items-center space-x-3\">\n                <div className=\"flex-shrink-0 w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm font-medium text-gray-700\">{item.label}</span>\n                    <span className=\"text-sm text-gray-500\">{item.value}</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`bg-gradient-to-r ${colorClasses[color]} h-2 rounded-full transition-all duration-500 ease-out`}\n                      style={{ width: `${(item.value / maxValue) * 100}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAG7C;AAFA;;;AAIe,SAAS,UAAU,EAChC,KAAK,EACL,OAAO,EAAE,EACT,OAAO,KAAK,EACZ,QAAQ,MAAM,EACd,UAAU,KAAK,EAChB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAA<PERSON>,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAHP;;;;;;;;;;;;;;;;;;;;;IAUtB;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;0BACjE,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;oCAAK,WAAU;8CAAgB;;;;;;8CAC7C,8OAAC;oCAAO,OAAM;oCAAM,WAAU;8CAAgB;;;;;;8CAC9C,8OAAC;oCAAO,OAAM;oCAAM,WAAU;8CAAgB;;;;;;8CAC9C,8OAAC;oCAAO,OAAM;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAKnD,8OAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAqC,KAAK,KAAK;;;;;;8DAC/D,8OAAC;oDAAK,WAAU;8DAAyB,KAAK,KAAK;;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,sDAAsD,CAAC;gDAC1G,OAAO;oDAAE,OAAO,GAAG,AAAC,KAAK,KAAK,GAAG,WAAY,IAAI,CAAC,CAAC;gDAAC;;;;;;;;;;;;;;;;;;2BAVlD;;;;;;;;;;;;;;;;;;;;;AAqBxB", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/reports/page.js"], "sourcesContent": ["// frontend/app/dashboard/reports/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getInvoices, getProducts, getCustomers, getInvoiceStats } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport StatsCard from '@/components/dashboard/StatsCard';\nimport ChartCard from '@/components/dashboard/ChartCard';\n\nfunction ReportsPage() {\n  const [reportData, setReportData] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // آخر 30 يوم\n    endDate: new Date().toISOString().split('T')[0]\n  });\n  const [reportType, setReportType] = useState('sales');\n\n  useEffect(() => {\n    loadReportData();\n  }, [dateRange, reportType]);\n\n  const loadReportData = async () => {\n    try {\n      setLoading(true);\n      \n      const [invoices, products, customers, stats] = await Promise.all([\n        getInvoices({ limit: 1000 }), // جلب جميع الفواتير\n        getProducts(),\n        getCustomers(),\n        getInvoiceStats()\n      ]);\n\n      // فلترة البيانات حسب التاريخ\n      const filteredInvoices = invoices.invoices?.filter(invoice => {\n        const invoiceDate = new Date(invoice.createdAt);\n        const start = new Date(dateRange.startDate);\n        const end = new Date(dateRange.endDate);\n        return invoiceDate >= start && invoiceDate <= end;\n      }) || [];\n\n      // حساب التقارير\n      const salesReport = calculateSalesReport(filteredInvoices);\n      const productReport = calculateProductReport(filteredInvoices, products);\n      const customerReport = calculateCustomerReport(filteredInvoices, customers);\n\n      setReportData({\n        sales: salesReport,\n        products: productReport,\n        customers: customerReport,\n        overview: stats\n      });\n\n    } catch (err) {\n      setError('Failed to load report data');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateSalesReport = (invoices) => {\n    const totalSales = invoices.reduce((sum, inv) => sum + parseFloat(inv.totalAmount), 0);\n    const totalInvoices = invoices.length;\n    const paidInvoices = invoices.filter(inv => inv.paymentStatus === 'paid').length;\n    const averageInvoiceValue = totalInvoices > 0 ? totalSales / totalInvoices : 0;\n\n    // مبيعات يومية\n    const dailySales = {};\n    invoices.forEach(invoice => {\n      const date = new Date(invoice.createdAt).toISOString().split('T')[0];\n      dailySales[date] = (dailySales[date] || 0) + parseFloat(invoice.totalAmount);\n    });\n\n    const chartData = Object.entries(dailySales)\n      .sort(([a], [b]) => new Date(a) - new Date(b))\n      .slice(-7) // آخر 7 أيام\n      .map(([date, amount]) => ({\n        label: new Date(date).toLocaleDateString('ar-SA', { weekday: 'short' }),\n        value: amount\n      }));\n\n    return {\n      totalSales,\n      totalInvoices,\n      paidInvoices,\n      averageInvoiceValue,\n      chartData\n    };\n  };\n\n  const calculateProductReport = (invoices, products) => {\n    const productSales = {};\n    \n    invoices.forEach(invoice => {\n      invoice.items?.forEach(item => {\n        const productId = item.productId;\n        if (!productSales[productId]) {\n          productSales[productId] = {\n            product: item.product,\n            quantity: 0,\n            revenue: 0\n          };\n        }\n        productSales[productId].quantity += item.quantity;\n        productSales[productId].revenue += parseFloat(item.total);\n      });\n    });\n\n    const topProducts = Object.values(productSales)\n      .sort((a, b) => b.revenue - a.revenue)\n      .slice(0, 5)\n      .map(item => ({\n        label: item.product?.name || 'Unknown Product',\n        value: item.revenue\n      }));\n\n    return {\n      topProducts,\n      totalProductsSold: Object.values(productSales).reduce((sum, item) => sum + item.quantity, 0),\n      uniqueProductsSold: Object.keys(productSales).length\n    };\n  };\n\n  const calculateCustomerReport = (invoices, customers) => {\n    const customerSales = {};\n    \n    invoices.forEach(invoice => {\n      const customerId = invoice.customerId;\n      if (!customerSales[customerId]) {\n        customerSales[customerId] = {\n          customer: invoice.customer,\n          invoiceCount: 0,\n          totalSpent: 0\n        };\n      }\n      customerSales[customerId].invoiceCount += 1;\n      customerSales[customerId].totalSpent += parseFloat(invoice.totalAmount);\n    });\n\n    const topCustomers = Object.values(customerSales)\n      .sort((a, b) => b.totalSpent - a.totalSpent)\n      .slice(0, 5)\n      .map(item => ({\n        label: item.customer?.name || 'Unknown Customer',\n        value: item.totalSpent\n      }));\n\n    return {\n      topCustomers,\n      activeCustomers: Object.keys(customerSales).length,\n      averageCustomerValue: Object.values(customerSales).length > 0 \n        ? Object.values(customerSales).reduce((sum, item) => sum + item.totalSpent, 0) / Object.values(customerSales).length \n        : 0\n    };\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const exportReport = () => {\n    // تصدير التقرير كـ CSV أو PDF\n    const csvContent = generateCSVReport();\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `report_${reportType}_${dateRange.startDate}_${dateRange.endDate}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  const generateCSVReport = () => {\n    let csvContent = '';\n    \n    if (reportType === 'sales') {\n      csvContent = 'Date,Sales Amount\\n';\n      reportData.sales?.chartData?.forEach(item => {\n        csvContent += `${item.label},${item.value}\\n`;\n      });\n    }\n    \n    return csvContent;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">التقارير والإحصائيات</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={exportReport}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                تصدير التقرير\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* فلاتر التقرير */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">نوع التقرير</label>\n              <select\n                value={reportType}\n                onChange={(e) => setReportType(e.target.value)}\n                className=\"w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n              >\n                <option value=\"sales\">تقرير المبيعات</option>\n                <option value=\"products\">تقرير المنتجات</option>\n                <option value=\"customers\">تقرير العملاء</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">من تاريخ</label>\n              <input\n                type=\"date\"\n                value={dateRange.startDate}\n                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}\n                className=\"w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">إلى تاريخ</label>\n              <input\n                type=\"date\"\n                value={dateRange.endDate}\n                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}\n                className=\"w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200\"\n              />\n            </div>\n\n            <div className=\"flex items-end\">\n              <button\n                onClick={loadReportData}\n                className=\"w-full px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200\"\n              >\n                تحديث التقرير\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* تقرير المبيعات */}\n        {reportType === 'sales' && (\n          <div className=\"space-y-8\">\n            {/* إحصائيات المبيعات */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <StatsCard\n                title=\"إجمالي المبيعات\"\n                value={formatCurrency(reportData.sales?.totalSales || 0)}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                }\n                color=\"green\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"عدد الفواتير\"\n                value={reportData.sales?.totalInvoices || 0}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                }\n                color=\"blue\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"الفواتير المدفوعة\"\n                value={reportData.sales?.paidInvoices || 0}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                }\n                color=\"green\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"متوسط قيمة الفاتورة\"\n                value={formatCurrency(reportData.sales?.averageInvoiceValue || 0)}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                }\n                color=\"purple\"\n                loading={loading}\n              />\n            </div>\n\n            {/* رسم بياني للمبيعات */}\n            <ChartCard\n              title=\"المبيعات اليومية\"\n              data={reportData.sales?.chartData || []}\n              color=\"green\"\n              loading={loading}\n            />\n          </div>\n        )}\n\n        {/* تقرير المنتجات */}\n        {reportType === 'products' && (\n          <div className=\"space-y-8\">\n            {/* إحصائيات المنتجات */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <StatsCard\n                title=\"إجمالي المنتجات المباعة\"\n                value={reportData.products?.totalProductsSold || 0}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                  </svg>\n                }\n                color=\"purple\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"أنواع المنتجات المباعة\"\n                value={reportData.products?.uniqueProductsSold || 0}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                  </svg>\n                }\n                color=\"indigo\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"أفضل منتج مبيعاً\"\n                value={reportData.products?.topProducts?.[0]?.label || 'لا يوجد'}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                  </svg>\n                }\n                color=\"yellow\"\n                loading={loading}\n              />\n            </div>\n\n            {/* أفضل المنتجات */}\n            <ChartCard\n              title=\"أفضل المنتجات مبيعاً\"\n              data={reportData.products?.topProducts || []}\n              color=\"purple\"\n              loading={loading}\n            />\n          </div>\n        )}\n\n        {/* تقرير العملاء */}\n        {reportType === 'customers' && (\n          <div className=\"space-y-8\">\n            {/* إحصائيات العملاء */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <StatsCard\n                title=\"العملاء النشطون\"\n                value={reportData.customers?.activeCustomers || 0}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                }\n                color=\"indigo\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"متوسط قيمة العميل\"\n                value={formatCurrency(reportData.customers?.averageCustomerValue || 0)}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                }\n                color=\"green\"\n                loading={loading}\n              />\n\n              <StatsCard\n                title=\"أفضل عميل\"\n                value={reportData.customers?.topCustomers?.[0]?.label || 'لا يوجد'}\n                icon={\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                  </svg>\n                }\n                color=\"yellow\"\n                loading={loading}\n              />\n            </div>\n\n            {/* أفضل العملاء */}\n            <ChartCard\n              title=\"أفضل العملاء\"\n              data={reportData.customers?.topCustomers || []}\n              color=\"indigo\"\n              loading={loading}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(ReportsPage);\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtF,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACjD;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YAEX,MAAM,CAAC,UAAU,UAAU,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/D,CAAA,GAAA,0GAAA,CAAA,cAAW,AAAD,EAAE;oBAAE,OAAO;gBAAK;gBAC1B,CAAA,GAAA,0GAAA,CAAA,cAAW,AAAD;gBACV,CAAA,GAAA,0GAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,0GAAA,CAAA,kBAAe,AAAD;aACf;YAED,6BAA6B;YAC7B,MAAM,mBAAmB,SAAS,QAAQ,EAAE,OAAO,CAAA;gBACjD,MAAM,cAAc,IAAI,KAAK,QAAQ,SAAS;gBAC9C,MAAM,QAAQ,IAAI,KAAK,UAAU,SAAS;gBAC1C,MAAM,MAAM,IAAI,KAAK,UAAU,OAAO;gBACtC,OAAO,eAAe,SAAS,eAAe;YAChD,MAAM,EAAE;YAER,gBAAgB;YAChB,MAAM,cAAc,qBAAqB;YACzC,MAAM,gBAAgB,uBAAuB,kBAAkB;YAC/D,MAAM,iBAAiB,wBAAwB,kBAAkB;YAEjE,cAAc;gBACZ,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,UAAU;YACZ;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,WAAW,IAAI,WAAW,GAAG;QACpF,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK,QAAQ,MAAM;QAChF,MAAM,sBAAsB,gBAAgB,IAAI,aAAa,gBAAgB;QAE7E,eAAe;QACf,MAAM,aAAa,CAAC;QACpB,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,OAAO,IAAI,KAAK,QAAQ,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpE,UAAU,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,WAAW,QAAQ,WAAW;QAC7E;QAEA,MAAM,YAAY,OAAO,OAAO,CAAC,YAC9B,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAC1C,KAAK,CAAC,CAAC,GAAG,aAAa;SACvB,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC;gBACxB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;oBAAE,SAAS;gBAAQ;gBACrE,OAAO;YACT,CAAC;QAEH,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,yBAAyB,CAAC,UAAU;QACxC,MAAM,eAAe,CAAC;QAEtB,SAAS,OAAO,CAAC,CAAA;YACf,QAAQ,KAAK,EAAE,QAAQ,CAAA;gBACrB,MAAM,YAAY,KAAK,SAAS;gBAChC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;oBAC5B,YAAY,CAAC,UAAU,GAAG;wBACxB,SAAS,KAAK,OAAO;wBACrB,UAAU;wBACV,SAAS;oBACX;gBACF;gBACA,YAAY,CAAC,UAAU,CAAC,QAAQ,IAAI,KAAK,QAAQ;gBACjD,YAAY,CAAC,UAAU,CAAC,OAAO,IAAI,WAAW,KAAK,KAAK;YAC1D;QACF;QAEA,MAAM,cAAc,OAAO,MAAM,CAAC,cAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO,EACpC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,OAAO,KAAK,OAAO,EAAE,QAAQ;gBAC7B,OAAO,KAAK,OAAO;YACrB,CAAC;QAEH,OAAO;YACL;YACA,mBAAmB,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YAC1F,oBAAoB,OAAO,IAAI,CAAC,cAAc,MAAM;QACtD;IACF;IAEA,MAAM,0BAA0B,CAAC,UAAU;QACzC,MAAM,gBAAgB,CAAC;QAEvB,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,aAAa,QAAQ,UAAU;YACrC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;gBAC9B,aAAa,CAAC,WAAW,GAAG;oBAC1B,UAAU,QAAQ,QAAQ;oBAC1B,cAAc;oBACd,YAAY;gBACd;YACF;YACA,aAAa,CAAC,WAAW,CAAC,YAAY,IAAI;YAC1C,aAAa,CAAC,WAAW,CAAC,UAAU,IAAI,WAAW,QAAQ,WAAW;QACxE;QAEA,MAAM,eAAe,OAAO,MAAM,CAAC,eAChC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,OAAO,KAAK,QAAQ,EAAE,QAAQ;gBAC9B,OAAO,KAAK,UAAU;YACxB,CAAC;QAEH,OAAO;YACL;YACA,iBAAiB,OAAO,IAAI,CAAC,eAAe,MAAM;YAClD,sBAAsB,OAAO,MAAM,CAAC,eAAe,MAAM,GAAG,IACxD,OAAO,MAAM,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,OAAO,MAAM,CAAC,eAAe,MAAM,GAClH;QACN;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe;QACnB,8BAA8B;QAC9B,MAAM,aAAa;QACnB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,IAAI,CAAC;QACpG,KAAK,KAAK,CAAC,UAAU,GAAG;QACxB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,MAAM,oBAAoB;QACxB,IAAI,aAAa;QAEjB,IAAI,eAAe,SAAS;YAC1B,aAAa;YACb,WAAW,KAAK,EAAE,WAAW,QAAQ,CAAA;gBACnC,cAAc,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;YAC/C;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAI9B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,UAAU,SAAS;4CAC1B,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC7E,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,UAAU,OAAO;4CACxB,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC3E,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;oBAON,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;oBAOxD,eAAe,yBACd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,eAAe,WAAW,KAAK,EAAE,cAAc;wCACtD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,KAAK,EAAE,iBAAiB;wCAC1C,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,KAAK,EAAE,gBAAgB;wCACzC,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,eAAe,WAAW,KAAK,EAAE,uBAAuB;wCAC/D,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;;;;;;;0CAKb,8OAAC,oIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,MAAM,WAAW,KAAK,EAAE,aAAa,EAAE;gCACvC,OAAM;gCACN,SAAS;;;;;;;;;;;;oBAMd,eAAe,4BACd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,QAAQ,EAAE,qBAAqB;wCACjD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,QAAQ,EAAE,sBAAsB;wCAClD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,QAAQ,EAAE,aAAa,CAAC,EAAE,EAAE,SAAS;wCACvD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;;;;;;;0CAKb,8OAAC,oIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,MAAM,WAAW,QAAQ,EAAE,eAAe,EAAE;gCAC5C,OAAM;gCACN,SAAS;;;;;;;;;;;;oBAMd,eAAe,6BACd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,SAAS,EAAE,mBAAmB;wCAChD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,eAAe,WAAW,SAAS,EAAE,wBAAwB;wCACpE,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;kDAGX,8OAAC,oIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,OAAO,WAAW,SAAS,EAAE,cAAc,CAAC,EAAE,EAAE,SAAS;wCACzD,oBACE,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAGzE,OAAM;wCACN,SAAS;;;;;;;;;;;;0CAKb,8OAAC,oIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,MAAM,WAAW,SAAS,EAAE,gBAAgB,EAAE;gCAC9C,OAAM;gCACN,SAAS;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}