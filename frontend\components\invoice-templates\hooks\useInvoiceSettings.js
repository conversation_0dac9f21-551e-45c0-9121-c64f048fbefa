// frontend/components/invoice-templates/hooks/useInvoiceSettings.js
'use client';

import { useState, useEffect } from 'react';
import settingsService from '@/lib/settingsService';

export function useInvoiceSettings() {
  const [companySettings, setCompanySettings] = useState(null);
  const [invoiceSettings, setInvoiceSettings] = useState(null);
  const [displaySettings, setDisplaySettings] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const [company, invoice, display] = await Promise.all([
        settingsService.getCompanySettings(),
        settingsService.getInvoiceSettings(),
        settingsService.getInvoiceDisplaySettings()
      ]);

      setCompanySettings(company);
      setInvoiceSettings(invoice);
      setDisplaySettings(display);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    companySettings,
    invoiceSettings,
    displaySettings,
    loading,
    refreshSettings: loadSettings
  };
}
