// frontend/components/settings/invoice/sections/InvoiceInfoSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function InvoiceInfoSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const invoiceFields = [
    { field: 'showInvoiceNumber', label: 'رقم الفاتورة', description: 'عرض رقم الفاتورة' },
    { field: 'showInvoiceDate', label: 'تاريخ الفاتورة', description: 'عرض تاريخ إصدار الفاتورة' },
    { field: 'showDueDate', label: 'تاريخ الاستحقاق', description: 'عرض تاريخ استحقاق الدفع' },
    { field: 'showPaymentTerms', label: 'شروط الدفع', description: 'عرض شروط وأحكام الدفع' }
  ];

  return (
    <SectionHeader
      title="معلومات الفاتورة"
      section="invoice"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      }
    >
      {invoiceFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
