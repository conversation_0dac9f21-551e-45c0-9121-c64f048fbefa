{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/products/page.js"], "sourcesContent": ["// frontend/app/dashboard/products/page.js\n'use client';\n\nimport { useEffect, useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { getProducts, deleteProduct } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\n\nfunction ProductsPage() {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  const loadProducts = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getProducts();\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products. Please try again later.');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadProducts();\n  }, []);\n\n  const handleDelete = async (id) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(id);\n        setProducts(products.filter(product => product.id !== id));\n      } catch (err) {\n        setError('Failed to delete product. Please try again.');\n        console.error(err);\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-xl\">Loading products...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto p-8\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800\">Product Management</h1>\n        <Link href=\"/dashboard/products/add\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300\">\n          Add New Product\n        </Link>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {error}\n        </div>\n      )}\n\n      {products.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-600 text-lg\">No products found.</p>\n          <Link href=\"/dashboard/products/add\" className=\"text-blue-600 hover:text-blue-800 font-medium\">\n            Add your first product\n          </Link>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {products.map((product) => (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              {product.imageUrl && (\n                <div className=\"h-48 bg-gray-200 flex items-center justify-center\">\n                  <Image\n                    src={product.imageUrl}\n                    alt={product.name}\n                    width={300}\n                    height={200}\n                    className=\"object-cover w-full h-full\"\n                  />\n                </div>\n              )}\n              <div className=\"p-4\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">{product.name}</h3>\n                <p className=\"text-gray-600 text-sm mb-2\">{product.description}</p>\n                <div className=\"flex justify-between items-center mb-2\">\n                  <span className=\"text-lg font-bold text-green-600\">${product.price}</span>\n                  <span className=\"text-sm text-gray-500\">Stock: {product.stock}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-xs text-gray-400\">{product.category}</span>\n                  <div className=\"space-x-2\">\n                    <Link\n                      href={`/dashboard/products/edit/${product.id}`}\n                      className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                    >\n                      Edit\n                    </Link>\n                    <button\n                      onClick={() => handleDelete(product.id)}\n                      className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default withAuth(ProductsPage);\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AAG1C;AACA;AACA;;;;;;;;;;;;;AAJA;;;;;;AAQA,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,kDAAkD;YACnE,IAAI;gBACF,MAAM,cAAc;gBACpB,YAAY,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;YACxD,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAA0B,WAAU;kCAAgH;;;;;;;;;;;;YAKhK,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,SAAS,MAAM,KAAK,kBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAA0B,WAAU;kCAAgD;;;;;;;;;;;qCAKjG,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAAqB,WAAU;;4BAC7B,QAAQ,QAAQ,kBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,QAAQ;oCACrB,KAAK,QAAQ,IAAI;oCACjB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C,QAAQ,IAAI;;;;;;kDACtE,6LAAC;wCAAE,WAAU;kDAA8B,QAAQ,WAAW;;;;;;kDAC9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAmC;oDAAE,QAAQ,KAAK;;;;;;;0DAClE,6LAAC;gDAAK,WAAU;;oDAAwB;oDAAQ,QAAQ,KAAK;;;;;;;;;;;;;kDAE/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAyB,QAAQ,QAAQ;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE,EAAE;wDAC9C,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;wDACtC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;uBA/BC,QAAQ,EAAE;;;;;;;;;;;;;;;;AA2ChC;GA/GS;KAAA;6CAiHM,SAAS", "debugId": null}}]}