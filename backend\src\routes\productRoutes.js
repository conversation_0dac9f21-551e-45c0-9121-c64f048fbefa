// backend/src/routes/productRoutes.js
const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { protect } = require('../middlewares/authMiddleware');

// GET /api/products - Get all products
router.get('/', protect, productController.getProducts);

// POST /api/products - Create a new product
router.post('/', protect, productController.createProduct);

// GET /api/products/:id - Get a single product by ID
router.get('/:id', protect, productController.getProductById);

// PUT /api/products/:id - Update a product by ID
router.put('/:id', protect, productController.updateProduct);

// DELETE /api/products/:id - Delete a product by ID
router.delete('/:id', protect, productController.deleteProduct);

module.exports = router;
