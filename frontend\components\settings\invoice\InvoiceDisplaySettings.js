// frontend/components/settings/invoice/InvoiceDisplaySettings.js
'use client';

import { useInvoiceDisplaySettings } from './hooks/useInvoiceDisplaySettings';
import HeaderSettings from './sections/HeaderSettings';
import InvoiceInfoSettings from './sections/InvoiceInfoSettings';
import CustomerSettings from './sections/CustomerSettings';
import ItemsSettings from './sections/ItemsSettings';
import TotalsSettings from './sections/TotalsSettings';
import FooterSettings from './sections/FooterSettings';
import SaveButtons from './components/SaveButtons';

export default function InvoiceDisplaySettings({ settings, onSave, loading }) {
  const {
    formData,
    handleInputChange,
    handleSelectAll,
    handleDeselectAll,
    handleReset
  } = useInvoiceDisplaySettings(settings);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <HeaderSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <InvoiceInfoSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <CustomerSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <ItemsSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <TotalsSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <FooterSettings
        formData={formData}
        onInputChange={handleInputChange}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      <SaveButtons
        loading={loading}
        onReset={handleReset}
      />
    </form>
  );
}
