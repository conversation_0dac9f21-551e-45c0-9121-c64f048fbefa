// frontend/components/settings/invoice/InvoiceDisplaySettings.js
'use client';

import { useState } from 'react';

export default function InvoiceDisplaySettings({ settings, onSave, loading }) {
  const [formData, setFormData] = useState({
    // عناصر الرأس
    showCompanyLogo: settings?.showCompanyLogo ?? true,
    showCompanyName: settings?.showCompanyName ?? true,
    showCompanyAddress: settings?.showCompanyAddress ?? true,
    showCompanyPhone: settings?.showCompanyPhone ?? true,
    showCompanyEmail: settings?.showCompanyEmail ?? true,
    showCompanyWebsite: settings?.showCompanyWebsite ?? true,
    showTaxId: settings?.showTaxId ?? true,
    
    // معلومات الفاتورة
    showInvoiceNumber: settings?.showInvoiceNumber ?? true,
    showInvoiceDate: settings?.showInvoiceDate ?? true,
    showDueDate: settings?.showDueDate ?? true,
    showPaymentTerms: settings?.showPaymentTerms ?? true,
    
    // معلومات العميل
    showCustomerName: settings?.showCustomerName ?? true,
    showCustomerAddress: settings?.showCustomerAddress ?? true,
    showCustomerPhone: settings?.showCustomerPhone ?? true,
    showCustomerEmail: settings?.showCustomerEmail ?? true,
    
    // عناصر الجدول
    showProductImages: settings?.showProductImages ?? true,
    showProductCode: settings?.showProductCode ?? true,
    showProductDescription: settings?.showProductDescription ?? true,
    showQuantity: settings?.showQuantity ?? true,
    showUnitPrice: settings?.showUnitPrice ?? true,
    showDiscount: settings?.showDiscount ?? false,
    showTotalPrice: settings?.showTotalPrice ?? true,
    
    // المجاميع
    showSubtotal: settings?.showSubtotal ?? true,
    showTaxAmount: settings?.showTaxAmount ?? true,
    showDiscountAmount: settings?.showDiscountAmount ?? false,
    showTotalAmount: settings?.showTotalAmount ?? true,
    
    // التذييل
    showNotes: settings?.showNotes ?? true,
    showFooter: settings?.showFooter ?? true,
    showSignature: settings?.showSignature ?? false,
    showQRCode: settings?.showQRCode ?? false,
    
    // إعدادات إضافية
    showItemNumbers: settings?.showItemNumbers ?? true,
    showBankDetails: settings?.showBankDetails ?? false,
    showPaymentInstructions: settings?.showPaymentInstructions ?? true
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleSelectAll = (section) => {
    const sectionFields = {
      header: ['showCompanyLogo', 'showCompanyName', 'showCompanyAddress', 'showCompanyPhone', 'showCompanyEmail', 'showCompanyWebsite', 'showTaxId'],
      invoice: ['showInvoiceNumber', 'showInvoiceDate', 'showDueDate', 'showPaymentTerms'],
      customer: ['showCustomerName', 'showCustomerAddress', 'showCustomerPhone', 'showCustomerEmail'],
      items: ['showProductImages', 'showProductCode', 'showProductDescription', 'showQuantity', 'showUnitPrice', 'showDiscount', 'showTotalPrice'],
      totals: ['showSubtotal', 'showTaxAmount', 'showDiscountAmount', 'showTotalAmount'],
      footer: ['showNotes', 'showFooter', 'showSignature', 'showQRCode']
    };

    const fields = sectionFields[section] || [];
    const newData = { ...formData };
    fields.forEach(field => {
      newData[field] = true;
    });
    setFormData(newData);
  };

  const handleDeselectAll = (section) => {
    const sectionFields = {
      header: ['showCompanyLogo', 'showCompanyName', 'showCompanyAddress', 'showCompanyPhone', 'showCompanyEmail', 'showCompanyWebsite', 'showTaxId'],
      invoice: ['showInvoiceNumber', 'showInvoiceDate', 'showDueDate', 'showPaymentTerms'],
      customer: ['showCustomerName', 'showCustomerAddress', 'showCustomerPhone', 'showCustomerEmail'],
      items: ['showProductImages', 'showProductCode', 'showProductDescription', 'showQuantity', 'showUnitPrice', 'showDiscount', 'showTotalPrice'],
      totals: ['showSubtotal', 'showTaxAmount', 'showDiscountAmount', 'showTotalAmount'],
      footer: ['showNotes', 'showFooter', 'showSignature', 'showQRCode']
    };

    const fields = sectionFields[section] || [];
    const newData = { ...formData };
    fields.forEach(field => {
      newData[field] = false;
    });
    setFormData(newData);
  };

  const SectionHeader = ({ title, icon, section, children }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {icon}
            <h3 className="text-lg font-semibold text-white mr-3">{title}</h3>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => handleSelectAll(section)}
              className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors"
            >
              تحديد الكل
            </button>
            <button
              type="button"
              onClick={() => handleDeselectAll(section)}
              className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors"
            >
              إلغاء الكل
            </button>
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {children}
        </div>
      </div>
    </div>
  );

  const CheckboxField = ({ field, label, description }) => (
    <div className="flex items-start space-x-3">
      <div className="flex items-center h-5">
        <input
          type="checkbox"
          checked={formData[field]}
          onChange={(e) => handleInputChange(field, e.target.checked)}
          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
        />
      </div>
      <div className="text-sm">
        <label className="font-medium text-gray-900 cursor-pointer">
          {label}
        </label>
        {description && (
          <p className="text-gray-500 text-xs mt-1">{description}</p>
        )}
      </div>
    </div>
  );

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* رأس الفاتورة */}
      <SectionHeader
        title="رأس الفاتورة"
        section="header"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
          </svg>
        }
      >
        <CheckboxField field="showCompanyLogo" label="شعار الشركة" description="عرض شعار الشركة في الرأس" />
        <CheckboxField field="showCompanyName" label="اسم الشركة" description="عرض اسم الشركة" />
        <CheckboxField field="showCompanyAddress" label="عنوان الشركة" description="عرض عنوان الشركة" />
        <CheckboxField field="showCompanyPhone" label="هاتف الشركة" description="عرض رقم هاتف الشركة" />
        <CheckboxField field="showCompanyEmail" label="بريد الشركة" description="عرض البريد الإلكتروني" />
        <CheckboxField field="showCompanyWebsite" label="موقع الشركة" description="عرض موقع الشركة الإلكتروني" />
        <CheckboxField field="showTaxId" label="الرقم الضريبي" description="عرض الرقم الضريبي للشركة" />
      </SectionHeader>

      {/* معلومات الفاتورة */}
      <SectionHeader
        title="معلومات الفاتورة"
        section="invoice"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        }
      >
        <CheckboxField field="showInvoiceNumber" label="رقم الفاتورة" description="عرض رقم الفاتورة" />
        <CheckboxField field="showInvoiceDate" label="تاريخ الفاتورة" description="عرض تاريخ إصدار الفاتورة" />
        <CheckboxField field="showDueDate" label="تاريخ الاستحقاق" description="عرض تاريخ استحقاق الدفع" />
        <CheckboxField field="showPaymentTerms" label="شروط الدفع" description="عرض شروط وأحكام الدفع" />
      </SectionHeader>

      {/* معلومات العميل */}
      <SectionHeader
        title="معلومات العميل"
        section="customer"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        }
      >
        <CheckboxField field="showCustomerName" label="اسم العميل" description="عرض اسم العميل" />
        <CheckboxField field="showCustomerAddress" label="عنوان العميل" description="عرض عنوان العميل" />
        <CheckboxField field="showCustomerPhone" label="هاتف العميل" description="عرض رقم هاتف العميل" />
        <CheckboxField field="showCustomerEmail" label="بريد العميل" description="عرض البريد الإلكتروني للعميل" />
      </SectionHeader>

      {/* عناصر الفاتورة */}
      <SectionHeader
        title="عناصر الفاتورة"
        section="items"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        }
      >
        <CheckboxField field="showItemNumbers" label="ترقيم العناصر" description="عرض أرقام تسلسلية للعناصر" />
        <CheckboxField field="showProductImages" label="صور المنتجات" description="عرض صور المنتجات" />
        <CheckboxField field="showProductCode" label="كود المنتج" description="عرض كود أو رقم المنتج" />
        <CheckboxField field="showProductDescription" label="وصف المنتج" description="عرض وصف المنتج" />
        <CheckboxField field="showQuantity" label="الكمية" description="عرض كمية المنتج" />
        <CheckboxField field="showUnitPrice" label="سعر الوحدة" description="عرض سعر الوحدة الواحدة" />
        <CheckboxField field="showDiscount" label="الخصم" description="عرض قيمة الخصم" />
        <CheckboxField field="showTotalPrice" label="الإجمالي" description="عرض إجمالي سعر العنصر" />
      </SectionHeader>

      {/* المجاميع */}
      <SectionHeader
        title="المجاميع والحسابات"
        section="totals"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        }
      >
        <CheckboxField field="showSubtotal" label="المجموع الفرعي" description="عرض المجموع قبل الضريبة" />
        <CheckboxField field="showTaxAmount" label="قيمة الضريبة" description="عرض مبلغ الضريبة" />
        <CheckboxField field="showDiscountAmount" label="قيمة الخصم" description="عرض إجمالي الخصم" />
        <CheckboxField field="showTotalAmount" label="المبلغ الإجمالي" description="عرض المبلغ النهائي" />
      </SectionHeader>

      {/* التذييل */}
      <SectionHeader
        title="تذييل الفاتورة"
        section="footer"
        icon={
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
          </svg>
        }
      >
        <CheckboxField field="showNotes" label="الملاحظات" description="عرض ملاحظات الفاتورة" />
        <CheckboxField field="showFooter" label="تذييل مخصص" description="عرض نص التذييل المخصص" />
        <CheckboxField field="showSignature" label="مكان التوقيع" description="عرض مساحة للتوقيع" />
        <CheckboxField field="showQRCode" label="رمز QR" description="عرض رمز QR للفاتورة" />
        <CheckboxField field="showBankDetails" label="تفاصيل البنك" description="عرض معلومات الحساب البنكي" />
        <CheckboxField field="showPaymentInstructions" label="تعليمات الدفع" description="عرض تعليمات الدفع" />
      </SectionHeader>

      {/* أزرار الحفظ */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          إعادة تعيين
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
        </button>
      </div>
    </form>
  );
}
