{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAJA;;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,8OAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,8OAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,8OAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,8OAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ClassicTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة الجبر للمقاولات',\n    customerDetails: 'المقاولات العامة د.م.م',\n    items: [\n      { id: 1, description: 'الصنف الأول', quantity: 2, price: 10.00, total: 20.00 },\n      { id: 2, description: 'الصنف الثاني', quantity: 4, price: 10.00, total: 40.00 },\n      { id: 3, description: 'الصنف الثالث', quantity: 6, price: 10.00, total: 60.00 },\n      { id: 4, description: 'الصنف الرابع', quantity: 8, price: 10.00, total: 80.00 }\n    ],\n    subtotal: 200.00,\n    tax: 0.00,\n    total: 200.00,\n    notes: 'شكراً لكم...',\n    companyName: 'شركة الجبر للمقاولات',\n    companyDetails: 'المقاولات العامة د.م.م'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header with Teal Design */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              <p className=\"text-teal-100\">{data.companyDetails}</p>\n            </div>\n\n            {/* Logo Area */}\n            <div className=\"bg-white p-4 rounded-lg shadow-md\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                <div className=\"text-white font-bold text-xl\">شعار</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">رقم الفاتورة:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">اسم العميل:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">التاريخ:</span>\n          </div>\n\n          <div>{data.invoiceNumber}</div>\n          <div>{data.customerName}</div>\n          <div>{data.date}</div>\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"bg-yellow-100\">\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">البيان</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Totals */}\n      <div className=\"flex justify-between items-end mb-8\">\n        <div className=\"text-right\">\n          <p className=\"text-lg font-semibold mb-4\">{data.notes}</p>\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">السعر</span>\n              <span>{(Number(data.subtotal) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">الضريبة</span>\n              <span>{(Number(data.tax) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"border-t border-gray-300 pt-2\">\n              <div className=\"flex justify-between font-bold text-lg\">\n                <span>الإجمالي</span>\n                <span>{(Number(data.total) || 0).toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"flex justify-between items-end\">\n        <div>\n          <h4 className=\"font-semibold mb-2\">ملاحظات</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• البضاعة المباعة لا ترد ولا تستبدل</li>\n            <li>• التأكد من استلام جميع بنود الفاتورة</li>\n          </ul>\n        </div>\n\n        <div className=\"text-center\">\n          <h4 className=\"font-semibold mb-4\">توقيع البائع</h4>\n          <div className=\"w-32 border-b-2 border-gray-400 mb-2\"></div>\n        </div>\n      </div>\n\n      {/* Contact Info */}\n      <div className=\"mt-8 pt-4 border-t border-gray-300\">\n        <div className=\"flex justify-between text-sm text-gray-600\">\n          <div>\n            <p>+123-456-7890</p>\n            <p>www.reallygreatsite.com</p>\n            <p><EMAIL></p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"w-32 h-8 bg-gray-800 flex items-center justify-center\">\n              <div className=\"text-white text-xs\">|||||||||||||||||||||||</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAe,UAAU;gBAAG,OAAO;gBAAO,OAAO;YAAM;YAC7E;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAO,OAAO;YAAM;YAC9E;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAO,OAAO;YAAM;YAC9E;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAO,OAAO;YAAM;SAC/E;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAiB,KAAK,cAAc;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAGlC,8OAAC;0CAAK,KAAK,aAAa;;;;;;0CACxB,8OAAC;0CAAK,KAAK,YAAY;;;;;;0CACvB,8OAAC;0CAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAGxE,8OAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;0DAC3E,8OAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCALxD,KAAK,EAAE;;;;;gCAUjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCALpC,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAa7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA8B,KAAK,KAAK;;;;;;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;sDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;sDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;sCAEL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ModernTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ModernTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ModernTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Modern Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 text-white\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold mb-2\">{data.companyName}</h1>\n            <p className=\"text-blue-100 text-lg\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4\">\n              <h2 className=\"text-2xl font-bold mb-2\">فاتورة</h2>\n              <p className=\"text-blue-100\">#{data.invoiceNumber}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-8\">\n        {/* Customer Info Card */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8\">\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات العميل</h3>\n              <div className=\"space-y-2\">\n                <p className=\"text-gray-700 font-medium\">{data.customerName}</p>\n                <p className=\"text-gray-600\">{data.customerDetails}</p>\n              </div>\n            </div>\n\n            <div className=\"text-right\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">تفاصيل الفاتورة</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">التاريخ:</span>\n                  <span className=\"font-medium\">{data.date}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                  <span className=\"font-medium\">{data.invoiceNumber}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Items Table */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">تفاصيل الخدمات</h3>\n\n          <div className=\"overflow-hidden rounded-xl border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n                <tr>\n                  <th className=\"p-4 text-right font-semibold\">الإجمالي</th>\n                  <th className=\"p-4 text-right font-semibold\">السعر</th>\n                  <th className=\"p-4 text-right font-semibold\">الكمية</th>\n                  <th className=\"p-4 text-right font-semibold\">الوصف</th>\n                  <th className=\"p-4 text-right font-semibold\">#</th>\n                </tr>\n              </thead>\n              <tbody>\n                {data.items.map((item, index) => (\n                  <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>\n                    <td className=\"p-4 text-right font-semibold text-blue-600\">\n                      {(Number(item.total) || 0).toFixed(2)} ر.س\n                    </td>\n                    <td className=\"p-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm\">\n                        {item.quantity || 0}\n                      </span>\n                    </td>\n                    <td className=\"p-4 text-right font-medium\">{item.description || ''}</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm font-medium\">\n                        {index + 1}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Modern Totals */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div className=\"w-1/2\">\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6\">\n              <h4 className=\"text-lg font-semibold text-gray-800 mb-3\">ملاحظات</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h5 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h5>\n                <ul className=\"text-sm text-gray-600 space-y-1\">\n                  <li>• الدفع خلال 30 يوم من تاريخ الفاتورة</li>\n                  <li>• جميع الأسعار شاملة ضريبة القيمة المضافة</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"w-80\">\n            <div className=\"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white\">\n              <h4 className=\"text-lg font-semibold mb-4\">ملخص الفاتورة</h4>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"border-t border-white border-opacity-30 pt-3\">\n                  <div className=\"flex justify-between text-xl font-bold\">\n                    <span>الإجمالي النهائي:</span>\n                    <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Footer */}\n        <div className=\"bg-gray-50 rounded-xl p-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">معلومات التواصل</h4>\n              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                <span>📞 +966 50 123 4567</span>\n                <span>📧 <EMAIL></span>\n                <span>🌐 www.company.com</span>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-32 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold\">\n                شعار الشركة\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;AAFA;;;AAIe,SAAS,eAAe,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IACjE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAEpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2B,KAAK,WAAW;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAyB,KAAK,cAAc;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;;4CAAgB;4CAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA6B,KAAK,YAAY;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,KAAK,IAAI;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;sDACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;oDAAiB,WAAW,QAAQ,MAAM,IAAI,eAAe;;sEAC5D,8OAAC;4DAAG,WAAU;;gEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,8OAAC;4DAAG,WAAU;;gEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACrE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;sEAGtB,8OAAC;4DAAG,WAAU;sEAA8B,KAAK,WAAW,IAAI;;;;;;sEAChE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;;;;;;;mDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwB1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAE5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvJ", "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ElegantTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ElegantTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ElegantTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'مؤسسة الأناقة التجارية',\n    customerDetails: 'للتجارة والمقاولات',\n    items: [\n      { id: 1, description: 'أثاث مكتبي فاخر', quantity: 5, price: 800.00, total: 4000.00 },\n      { id: 2, description: 'ديكورات داخلية', quantity: 1, price: 2500.00, total: 2500.00 },\n      { id: 3, description: 'إكسسوارات مكتبية', quantity: 10, price: 150.00, total: 1500.00 }\n    ],\n    subtotal: 8000.00,\n    tax: 1200.00,\n    total: 9200.00,\n    notes: 'نتطلع لخدمتكم مرة أخرى',\n    companyName: 'مؤسسة الأناقة التجارية',\n    companyDetails: 'للتجارة والمقاولات'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Elegant Header */}\n      <div className=\"relative\">\n        <div className=\"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600\"></div>\n\n        <div className=\"p-8 pt-12\">\n          <div className=\"flex justify-between items-start mb-8\">\n            <div>\n              <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n              <p className=\"text-gray-600 text-lg italic\">{data.companyDetails}</p>\n\n              <div className=\"mt-6 w-24 h-1 bg-gradient-to-r from-amber-400 to-yellow-500\"></div>\n            </div>\n\n            <div className=\"text-right\">\n              <div className=\"border-2 border-amber-400 rounded-lg p-4 bg-amber-50\">\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-1\">فاتورة</h2>\n                <p className=\"text-amber-600 font-semibold\">#{data.invoiceNumber}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Customer Section */}\n          <div className=\"border border-gray-200 rounded-lg p-6 mb-8 bg-gray-50\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  فواتير إلى\n                </h3>\n                <div className=\"space-y-2\">\n                  <p className=\"text-gray-800 font-semibold text-lg\">{data.customerName}</p>\n                  <p className=\"text-gray-600\">{data.customerDetails}</p>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  تفاصيل الفاتورة\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                    <span className=\"font-semibold\">{data.date}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">رقم المرجع:</span>\n                    <span className=\"font-semibold\">{data.invoiceNumber}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Items Table */}\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-serif font-semibold text-gray-800 mb-4 border-b-2 border-amber-400 pb-2\">\n              بنود الفاتورة\n            </h3>\n\n            <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gradient-to-r from-gray-100 to-amber-50\">\n                  <tr>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">المبلغ</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">سعر الوحدة</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الكمية</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الوصف</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">م</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {data.items.map((item, index) => (\n                    <tr key={item.id} className=\"border-b border-gray-100 hover:bg-amber-25\">\n                      <td className=\"p-4 text-right font-semibold text-amber-600\">\n                        {(Number(item.total) || 0).toFixed(2)} ر.س\n                      </td>\n                      <td className=\"p-4 text-right text-gray-700\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium\">\n                          {item.quantity || 0}\n                        </span>\n                      </td>\n                      <td className=\"p-4 text-right font-medium text-gray-800\">{item.description || ''}</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                          {index + 1}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Elegant Summary */}\n          <div className=\"flex justify-between items-start mb-8\">\n            <div className=\"w-1/2 pr-8\">\n              <div className=\"border-l-4 border-amber-400 pl-6\">\n                <h4 className=\"text-lg font-serif font-semibold text-gray-800 mb-3\">ملاحظات خاصة</h4>\n                <p className=\"text-gray-700 italic mb-4\">{data.notes}</p>\n\n                <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n                  <h5 className=\"font-semibold text-gray-800 mb-2\">شروط وأحكام:</h5>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• الدفع خلال 15 يوم من تاريخ الفاتورة</li>\n                    <li>• ضمان جودة لمدة سنة كاملة</li>\n                    <li>• خدمة ما بعد البيع متاحة</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"w-80\">\n              <div className=\"border-2 border-amber-300 rounded-lg bg-gradient-to-br from-amber-50 to-yellow-50\">\n                <div className=\"bg-gradient-to-r from-amber-400 to-yellow-500 text-white p-4 rounded-t-lg\">\n                  <h4 className=\"text-lg font-serif font-semibold\">الملخص المالي</h4>\n                </div>\n\n                <div className=\"p-6 space-y-4\">\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>المجموع الفرعي:</span>\n                    <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>ضريبة القيمة المضافة:</span>\n                    <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"border-t-2 border-amber-300 pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold text-gray-800\">\n                      <span className=\"font-serif\">الإجمالي النهائي:</span>\n                      <span className=\"text-amber-600\">{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Footer */}\n          <div className=\"border-t-2 border-amber-300 pt-6\">\n            <div className=\"flex justify-between items-end\">\n              <div>\n                <h4 className=\"font-serif font-semibold text-gray-800 mb-3\">معلومات التواصل</h4>\n                <div className=\"space-y-1 text-sm text-gray-600\">\n                  <p>📱 الهاتف: +966 50 123 4567</p>\n                  <p>📧 البريد: <EMAIL></p>\n                  <p>🌐 الموقع: www.elegant-business.com</p>\n                  <p>📍 العنوان: الرياض، المملكة العربية السعودية</p>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <h5 className=\"font-serif font-semibold text-gray-800 mb-4\">توقيع مخول</h5>\n                <div className=\"w-32 h-16 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-amber-500 text-sm\">التوقيع</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAmB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAoB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;SACvF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD,KAAK,WAAW;;;;;;sDAClF,8OAAC;4CAAE,WAAU;sDAAgC,KAAK,cAAc;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;;oDAA+B;oDAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuC,KAAK,YAAY;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kDAItD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAIrG,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,WAAU;0DACf,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAGnG,8OAAC;0DACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wDAAiB,WAAU;;0EAC1B,8OAAC;gEAAG,WAAU;;oEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EAExC,8OAAC;gEAAG,WAAU;;oEAAgC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACnF,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;0EAGtB,8OAAC;gEAAG,WAAU;0EAA4C,KAAK,WAAW,IAAI;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAU;8EACb,QAAQ;;;;;;;;;;;;uDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAa;;;;;;8EAC7B,8OAAC;oEAAK,WAAU;;wEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D", "debugId": null}}, {"offset": {"line": 3002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/MinimalTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/MinimalTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function MinimalTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة البساطة للأعمال',\n    customerDetails: 'الحلول البسيطة والفعالة',\n    items: [\n      { id: 1, description: 'خدمة استشارية', quantity: 2, price: 500.00, total: 1000.00 },\n      { id: 2, description: 'تطوير موقع إلكتروني', quantity: 1, price: 3000.00, total: 3000.00 },\n      { id: 3, description: 'صيانة شهرية', quantity: 6, price: 200.00, total: 1200.00 }\n    ],\n    subtotal: 5200.00,\n    tax: 780.00,\n    total: 5980.00,\n    notes: 'شكراً لاختياركم خدماتنا',\n    companyName: 'شركة البساطة للأعمال',\n    companyDetails: 'الحلول البسيطة والفعالة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white p-8\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Minimal Header */}\n      <div className=\"border-b-4 border-gray-800 pb-6 mb-8\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n            <p className=\"text-gray-600\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <h2 className=\"text-4xl font-bold text-gray-800 mb-2\">فاتورة</h2>\n            <p className=\"text-gray-600 text-lg\">#{data.invoiceNumber}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Customer and Invoice Info */}\n      <div className=\"grid grid-cols-2 gap-12 mb-8\">\n        <div>\n          <h3 className=\"text-lg font-bold text-gray-800 mb-3\">إلى:</h3>\n          <div className=\"space-y-1\">\n            <p className=\"text-gray-800 font-semibold\">{data.customerName}</p>\n            <p className=\"text-gray-600\">{data.customerDetails}</p>\n          </div>\n        </div>\n\n        <div className=\"text-right\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">تاريخ الفاتورة:</span>\n              <span className=\"font-semibold\">{data.date}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">رقم الفاتورة:</span>\n              <span className=\"font-semibold\">{data.invoiceNumber}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"border-b-2 border-gray-800\">\n              <th className=\"py-3 text-right font-bold text-gray-800\">المبلغ</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">السعر</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الكمية</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الوصف</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id} className=\"border-b border-gray-200\">\n                <td className=\"py-4 text-right font-semibold\">{(Number(item.total) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{item.quantity || 0}</td>\n                <td className=\"py-4 text-right\">{item.description || ''}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Simple Totals */}\n      <div className=\"flex justify-between items-start mb-12\">\n        <div className=\"w-1/2\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3\">ملاحظات:</h4>\n          <p className=\"text-gray-700\">{data.notes}</p>\n\n          <div className=\"mt-6\">\n            <h5 className=\"font-bold text-gray-800 mb-2\">شروط الدفع:</h5>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• الدفع خلال 30 يوم</li>\n              <li>• تطبق غرامة تأخير 2% شهرياً</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"w-80\">\n          <div className=\"border-2 border-gray-800 p-6\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">المجموع الفرعي:</span>\n                <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">الضريبة (15%):</span>\n                <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"border-t-2 border-gray-800 pt-3\">\n                <div className=\"flex justify-between text-xl font-bold\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Footer */}\n      <div className=\"border-t border-gray-300 pt-6\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-600\">\n            <p>الهاتف: +966 50 123 4567 | البريد: <EMAIL> | الموقع: www.simple.com</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-24 h-12 border border-gray-400 flex items-center justify-center text-gray-500 text-sm\">\n              الشعار\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAiB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YAClF;gBAAE,IAAI;gBAAG,aAAa;gBAAuB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACzF;gBAAE,IAAI;gBAAG,aAAa;gBAAe,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;SACjF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAe,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAExE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC,KAAK,WAAW;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAAiB,KAAK,cAAc;;;;;;;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;;wCAAwB;wCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA+B,KAAK,YAAY;;;;;;kDAC7D,8OAAC;wCAAE,WAAU;kDAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,IAAI;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;;;;;;;;;;;;sCAG5D,8OAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAG,WAAU;;gDAAiC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACpF,8OAAC;4CAAG,WAAU;;gDAAmB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACtE,8OAAC;4CAAG,WAAU;sDAAmB,KAAK,QAAQ,IAAI;;;;;;sDAClD,8OAAC;4CAAG,WAAU;sDAAmB,KAAK,WAAW,IAAI;;;;;;;mCAJ9C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAYxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;sCAGL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA0F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrH", "debugId": null}}, {"offset": {"line": 3599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/TemplatePreview.js"], "sourcesContent": ["// frontend/components/invoice-templates/TemplatePreview.js\n'use client';\n\nimport React, { useState } from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\n\nconst templates = {\n  classic: {\n    id: 'classic',\n    name: 'القالب الكلاسيكي',\n    description: 'تصميم تقليدي أنيق مع ألوان الأخضر والذهبي',\n    component: ClassicTemplate,\n    preview: '/images/templates/classic-preview.jpg',\n    features: ['تصميم تقليدي', 'ألوان هادئة', 'مناسب للشركات التقليدية']\n  },\n  modern: {\n    id: 'modern',\n    name: 'القالب الحديث',\n    description: 'تصميم عصري مع تدرجات لونية جذابة',\n    component: ModernTemplate,\n    preview: '/images/templates/modern-preview.jpg',\n    features: ['تصميم عصري', 'تدرجات لونية', 'مناسب للشركات التقنية']\n  },\n  elegant: {\n    id: 'elegant',\n    name: 'القالب الأنيق',\n    description: 'تصميم راقي مع لمسات ذهبية فاخرة',\n    component: ElegantTemplate,\n    preview: '/images/templates/elegant-preview.jpg',\n    features: ['تصميم راقي', 'لمسات ذهبية', 'مناسب للشركات الفاخرة']\n  },\n  minimal: {\n    id: 'minimal',\n    name: 'القالب البسيط',\n    description: 'تصميم بسيط ونظيف للاستخدام العام',\n    component: MinimalTemplate,\n    preview: '/images/templates/minimal-preview.jpg',\n    features: ['تصميم بسيط', 'سهل القراءة', 'مناسب لجميع الأعمال']\n  }\n};\n\nexport default function TemplatePreview({ onSelectTemplate, selectedTemplate }) {\n  const [previewTemplate, setPreviewTemplate] = useState(null);\n  const [scale, setScale] = useState(0.3);\n\n  const handlePreview = (templateId) => {\n    setPreviewTemplate(templateId);\n  };\n\n  const closePreview = () => {\n    setPreviewTemplate(null);\n  };\n\n  const handleSelect = (templateId) => {\n    onSelectTemplate(templateId);\n    closePreview();\n  };\n\n  return (\n    <div>\n      {/* Templates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {Object.values(templates).map((template) => (\n          <div\n            key={template.id}\n            className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:scale-105 ${\n              selectedTemplate === template.id\n                ? 'border-blue-500 ring-4 ring-blue-100'\n                : 'border-gray-200 hover:border-blue-300'\n            }`}\n          >\n            {/* Template Preview Image */}\n            <div className=\"relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden\">\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"transform scale-[0.15] origin-top-left\">\n                  <template.component preview={true} />\n                </div>\n              </div>\n              \n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-105\"\n                >\n                  معاينة كاملة\n                </button>\n              </div>\n            </div>\n\n            {/* Template Info */}\n            <div className=\"p-4\">\n              <h3 className=\"text-lg font-bold text-gray-800 mb-2\">{template.name}</h3>\n              <p className=\"text-gray-600 text-sm mb-3\">{template.description}</p>\n              \n              {/* Features */}\n              <div className=\"mb-4\">\n                <div className=\"flex flex-wrap gap-1\">\n                  {template.features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-2\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors\"\n                >\n                  معاينة\n                </button>\n                <button\n                  onClick={() => handleSelect(template.id)}\n                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${\n                    selectedTemplate === template.id\n                      ? 'bg-green-500 text-white'\n                      : 'bg-blue-500 text-white hover:bg-blue-600'\n                  }`}\n                >\n                  {selectedTemplate === template.id ? 'محدد' : 'اختيار'}\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Full Preview Modal */}\n      {previewTemplate && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white rounded-xl max-w-6xl max-h-[90vh] overflow-hidden flex flex-col\">\n            {/* Modal Header */}\n            <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\n              <div>\n                <h3 className=\"text-xl font-bold text-gray-800\">\n                  معاينة {templates[previewTemplate].name}\n                </h3>\n                <p className=\"text-gray-600 text-sm\">\n                  {templates[previewTemplate].description}\n                </p>\n              </div>\n              \n              <div className=\"flex items-center gap-4\">\n                {/* Zoom Controls */}\n                <div className=\"flex items-center gap-2\">\n                  <button\n                    onClick={() => setScale(Math.max(0.1, scale - 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 12H4\" />\n                    </svg>\n                  </button>\n                  <span className=\"text-sm font-medium min-w-[60px] text-center\">\n                    {Math.round(scale * 100)}%\n                  </span>\n                  <button\n                    onClick={() => setScale(Math.min(1, scale + 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                  </button>\n                </div>\n                \n                <button\n                  onClick={closePreview}\n                  className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Preview Content */}\n            <div className=\"flex-1 overflow-auto p-4 bg-gray-100\">\n              <div className=\"flex justify-center\">\n                <div \n                  className=\"bg-white shadow-lg\"\n                  style={{ \n                    transform: `scale(${scale})`,\n                    transformOrigin: 'top center'\n                  }}\n                >\n                  {React.createElement(templates[previewTemplate].component, { preview: true })}\n                </div>\n              </div>\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex gap-2\">\n                  {templates[previewTemplate].features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={closePreview}\n                    className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    إغلاق\n                  </button>\n                  <button\n                    onClick={() => handleSelect(previewTemplate)}\n                    className=\"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium\"\n                  >\n                    اختيار هذا القالب\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport { templates };\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,YAAY;IAChB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAgB;YAAe;SAA0B;IACtE;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,oJAAA,CAAA,UAAc;QACzB,SAAS;QACT,UAAU;YAAC;YAAc;YAAgB;SAAwB;IACnE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAwB;IAClE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAsB;IAChE;AACF;AAEe,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB,CAAC;QACrB,mBAAmB;IACrB;IAEA,MAAM,eAAe;QACnB,mBAAmB;IACrB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB;IACF;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,yBAC7B,8OAAC;wBAEC,WAAW,CAAC,mGAAmG,EAC7G,qBAAqB,SAAS,EAAE,GAC5B,yCACA,yCACJ;;0CAGF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,SAAS,SAAS;gDAAC,SAAS;;;;;;;;;;;;;;;;kDAKjC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,cAAc,SAAS,EAAE;4CACxC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC,SAAS,IAAI;;;;;;kDACnE,8OAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;kDAUb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,cAAc,SAAS,EAAE;gDACxC,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,aAAa,SAAS,EAAE;gDACvC,WAAW,CAAC,kEAAkE,EAC5E,qBAAqB,SAAS,EAAE,GAC5B,4BACA,4CACJ;0DAED,qBAAqB,SAAS,EAAE,GAAG,SAAS;;;;;;;;;;;;;;;;;;;uBA7D9C,SAAS,EAAE;;;;;;;;;;YAsErB,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAkC;gDACtC,SAAS,CAAC,gBAAgB,CAAC,IAAI;;;;;;;sDAEzC,8OAAC;4CAAE,WAAU;sDACV,SAAS,CAAC,gBAAgB,CAAC,WAAW;;;;;;;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,KAAK,QAAQ;oDAC9C,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,QAAQ;wDAAK;;;;;;;8DAE3B,8OAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,GAAG,QAAQ;oDAC5C,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAK3E,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;wCAC5B,iBAAiB;oCACnB;8CAEC,cAAA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE;wCAAE,SAAS;oCAAK;;;;;;;;;;;;;;;;sCAMjF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;kDAQX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 4083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoice-templates/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport TemplatePreview from '@/components/invoice-templates/TemplatePreview';\n\nfunction InvoiceTemplatesPage() {\n  const [selectedTemplate, setSelectedTemplate] = useState('classic');\n  const [showSuccess, setShowSuccess] = useState(false);\n\n  const handleSelectTemplate = (templateId) => {\n    setSelectedTemplate(templateId);\n    setShowSuccess(true);\n    \n    // Save to localStorage for now (later can be saved to user preferences)\n    localStorage.setItem('selectedInvoiceTemplate', templateId);\n    \n    setTimeout(() => {\n      setShowSuccess(false);\n    }, 3000);\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <nav className=\"flex\" aria-label=\"Breadcrumb\">\n                  <ol className=\"flex items-center space-x-4\">\n                    <li>\n                      <Link href=\"/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                        <svg className=\"flex-shrink-0 h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n                        </svg>\n                      </Link>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <span className=\"ml-4 text-sm font-medium text-gray-900\">قوالب الفواتير</span>\n                      </div>\n                    </li>\n                  </ol>\n                </nav>\n                <h1 className=\"mt-2 text-3xl font-bold text-gray-900\">قوالب الفواتير</h1>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  اختر القالب المناسب لفواتيرك من مجموعة متنوعة من التصاميم الاحترافية\n                </p>\n              </div>\n              \n              <div className=\"flex items-center gap-4\">\n                <Link\n                  href=\"/dashboard/invoices/create\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  إنشاء فاتورة جديدة\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Success Message */}\n          {showSuccess && (\n            <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <p className=\"text-green-800 font-medium\">\n                  تم حفظ اختيار القالب بنجاح! سيتم استخدام هذا القالب في الفواتير الجديدة.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Templates Info */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n            <div className=\"flex items-start gap-4\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n              </div>\n              \n              <div className=\"flex-1\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">اختر قالب الفاتورة المناسب</h2>\n                <p className=\"text-gray-600 mb-4\">\n                  نوفر لك مجموعة متنوعة من قوالب الفواتير الاحترافية التي تناسب مختلف أنواع الأعمال. \n                  يمكنك معاينة كل قالب بالتفصيل قبل اختياره.\n                </p>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                  <div className=\"flex items-center gap-2\">\n                    <svg className=\"w-4 h-4 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    <span className=\"text-gray-700\">تصاميم احترافية متنوعة</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <svg className=\"w-4 h-4 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    <span className=\"text-gray-700\">معاينة كاملة قبل الاختيار</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <svg className=\"w-4 h-4 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    <span className=\"text-gray-700\">سهولة التغيير في أي وقت</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Current Selection */}\n          {selectedTemplate && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8\">\n              <div className=\"flex items-center gap-3\">\n                <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-blue-800 font-medium\">\n                  القالب المحدد حالياً: <span className=\"font-bold\">\n                    {selectedTemplate === 'classic' && 'القالب الكلاسيكي'}\n                    {selectedTemplate === 'modern' && 'القالب الحديث'}\n                    {selectedTemplate === 'elegant' && 'القالب الأنيق'}\n                    {selectedTemplate === 'minimal' && 'القالب البسيط'}\n                  </span>\n                </span>\n              </div>\n            </div>\n          )}\n\n          {/* Templates Grid */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">القوالب المتاحة</h3>\n            \n            <TemplatePreview\n              onSelectTemplate={handleSelectTemplate}\n              selectedTemplate={selectedTemplate}\n            />\n          </div>\n\n          {/* Help Section */}\n          <div className=\"mt-8 bg-gray-50 rounded-xl p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">نصائح لاختيار القالب المناسب</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h4 className=\"font-medium text-gray-800 mb-2\">للشركات التقليدية</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  ننصح باستخدام القالب الكلاسيكي أو الأنيق للحصول على مظهر رسمي ومحترف.\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-800 mb-2\">للشركات التقنية</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  القالب الحديث مناسب للشركات التقنية والناشئة بتصميمه العصري والجذاب.\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-800 mb-2\">للاستخدام العام</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  القالب البسيط مناسب لجميع أنواع الأعمال ويركز على وضوح المعلومات.\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-800 mb-2\">للشركات الفاخرة</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  القالب الأنيق يوفر لمسة راقية مع التفاصيل الذهبية للشركات الفاخرة.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(InvoiceTemplatesPage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,eAAe;QAEf,wEAAwE;QACxE,aAAa,OAAO,CAAC,2BAA2B;QAEhD,WAAW;YACT,eAAe;QACjB,GAAG;IACL;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;4CAAO,cAAW;sDAC/B,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;sEAChC,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;kEAId,8OAAC;kEACC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAAsC,MAAK;oEAAe,SAAQ;8EAC/E,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;8EAE3J,8OAAC;oEAAK,WAAU;8EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKjE,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhB,8OAAC;oBAAI,WAAU;;wBAEZ,6BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA8B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACrF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAQhD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;kDAK3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAKlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQzC,kCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;;4CAA4B;0DACpB,8OAAC;gDAAK,WAAU;;oDACnC,qBAAqB,aAAa;oDAClC,qBAAqB,YAAY;oDACjC,qBAAqB,aAAa;oDAClC,qBAAqB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC,qJAAA,CAAA,UAAe;oCACd,kBAAkB;oCAClB,kBAAkB;;;;;;;;;;;;sCAKtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}