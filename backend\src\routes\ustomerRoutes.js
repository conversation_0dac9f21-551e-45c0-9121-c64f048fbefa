// backend/src/routes/customerRoutes.js
const express = require('express');
const router = express.Router();
const customerController = require('../controllers/customerController');
const { protect } = require('../middlewares/authMiddleware');

// GET /api/customers - جلب جميع العملاء
router.get('/', protect, customerController.getCustomers);

// POST /api/customers - إنشاء عميل جديد
router.post('/', protect, customerController.createCustomer);

// GET /api/customers/:id - جلب عميل واحد بواسطة الـ ID
router.get('/:id', protect, customerController.getCustomerById);

// PUT /api/customers/:id - تحديث عميل بواسطة الـ ID
router.put('/:id', protect, customerController.updateCustomer);

// DELETE /api/customers/:id - حذف عميل بواسطة الـ ID
router.delete('/:id', protect, customerController.deleteCustomer);

module.exports = router;
