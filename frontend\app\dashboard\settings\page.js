// frontend/app/dashboard/settings/page.js
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import withAuth from '@/components/withAuth';
import CompanySettings from '@/components/settings/CompanySettings';
import InvoiceSettings from '@/components/settings/InvoiceSettings';
import BackupSettings from '@/components/settings/BackupSettings';
import NotificationSettings from '@/components/settings/NotificationSettings';
import SecuritySettings from '@/components/settings/SecuritySettings';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('company');
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const router = useRouter();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // تحميل الإعدادات من خدمة API
      const apiSettingsService = (await import('@/lib/apiSettingsService')).default;
      const loadedSettings = await apiSettingsService.loadSettings();
      setSettings(loadedSettings);
    } catch (loadError) {
      console.error('Error loading settings:', loadError);
      // في حالة الخطأ، استخدم الإعدادات الافتراضية
      const mockSettings = {
        company: {
          companyName: '',
          companyAddress: 'الرياض، المملكة العربية السعودية',
          companyPhone: '+966 11 123 4567',
          companyEmail: '<EMAIL>',
          companyWebsite: 'https://www.techcompany.com',
          taxId: '*********',
          currency: 'SAR',
          logoUrl: null
        },
        invoice: {
          invoicePrefix: 'INV',
          invoiceNumberLength: 6,
          defaultTaxRate: 15,
          defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
          autoGenerateInvoiceNumber: true,
          showImagesInInvoice: true,
          allowPartialPayments: true,
          requireCustomerInfo: true,
          defaultDueDays: 30,
          invoiceFooter: 'شكراً لتعاملكم معنا',
          invoiceNotes: 'يرجى الدفع في الموعد المحدد'
        },
        notifications: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          invoiceCreated: true,
          invoicePaid: true,
          invoiceOverdue: true,
          paymentReceived: true,
          lowStockAlert: true,
          outOfStockAlert: true,
          stockThreshold: 5,
          systemUpdates: true,
          securityAlerts: true,
          backupStatus: true,
          emailServer: 'smtp.gmail.com',
          emailPort: 587,
          emailUsername: '',
          emailPassword: '',
          emailSecurity: 'tls'
        }
      };
      setSettings(mockSettings);
    }
  };

  const handleSave = async (tabType, data) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const apiSettingsService = (await import('@/lib/apiSettingsService')).default;

      // حفظ الإعدادات حسب النوع
      if (tabType === 'company') {
        await apiSettingsService.saveCompanySettings(data);
      } else if (tabType === 'invoice') {
        await apiSettingsService.saveInvoiceSettings(data);
      } else {
        // للأنواع الأخرى، حفظ عام (استخدام localStorage كبديل)
        const settingsService = (await import('@/lib/settingsService')).default;
        const currentSettings = await settingsService.loadSettings();
        await settingsService.saveSettings({
          ...currentSettings,
          [tabType]: { ...currentSettings[tabType], ...data }
        });
      }

      // تحديث الحالة المحلية
      setSettings(prev => ({
        ...prev,
        [tabType]: { ...prev[tabType], ...data }
      }));

      setSuccess('تم حفظ الإعدادات بنجاح');

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleBackup = async (options) => {
    setLoading(true);
    try {
      // محاكاة إنشاء نسخة احتياطية
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إنشاء ملف JSON للتحميل
      const backupData = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        data: {
          settings,
          // يمكن إضافة بيانات أخرى هنا
        }
      };

      const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      setSuccess('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('فشل في إنشاء النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async (backupData) => {
    setLoading(true);
    try {
      // محاكاة استعادة النسخة الاحتياطية
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (backupData instanceof File) {
        const text = await backupData.text();
        const data = JSON.parse(text);
        setSettings(data.data.settings || {});
      }

      setSuccess('تم استعادة النسخة الاحتياطية بنجاح');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('فشل في استعادة النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const handleSecuritySave = async (securityData) => {
    setLoading(true);
    try {
      // محاكاة حفظ إعدادات الأمان
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (securityData.type === 'password') {
        setSuccess('تم تغيير كلمة المرور بنجاح');
      } else {
        setSuccess('تم حفظ إعدادات الأمان بنجاح');
      }

      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('فشل في حفظ إعدادات الأمان');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    {
      id: 'company',
      name: 'معلومات الشركة',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
        </svg>
      )
    },
    {
      id: 'invoice',
      name: 'إعدادات الفواتير',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'notifications',
      name: 'الإشعارات',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6z" />
        </svg>
      )
    },
    {
      id: 'security',
      name: 'الأمان',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      )
    },
    {
      id: 'backup',
      name: 'النسخ الاحتياطي',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                العودة
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* رسائل النجاح والخطأ */}
        {success && (
          <div className="mb-6 bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700 font-medium">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* التبويبات الجانبية */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                <h2 className="text-lg font-semibold text-white">أقسام الإعدادات</h2>
              </div>
              <nav className="p-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 mb-1 ${
                      activeTab === tab.id
                        ? 'bg-indigo-50 text-indigo-700 border-r-4 border-indigo-500'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <span className={`mr-3 ${activeTab === tab.id ? 'text-indigo-500' : 'text-gray-400'}`}>
                      {tab.icon}
                    </span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-3">
            {activeTab === 'company' && (
              <CompanySettings
                settings={settings.company}
                onSave={(data) => handleSave('company', data)}
                loading={loading}
              />
            )}

            {activeTab === 'invoice' && (
              <InvoiceSettings
                settings={settings.invoice}
                onSave={(data) => handleSave('invoice', data)}
                onRefresh={loadSettings}
                loading={loading}
              />
            )}

            {activeTab === 'notifications' && (
              <NotificationSettings
                settings={settings.notifications}
                onSave={(data) => handleSave('notifications', data)}
                loading={loading}
              />
            )}

            {activeTab === 'security' && (
              <SecuritySettings
                onSave={handleSecuritySave}
                loading={loading}
              />
            )}

            {activeTab === 'backup' && (
              <BackupSettings
                onBackup={handleBackup}
                onRestore={handleRestore}
                loading={loading}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(SettingsPage);
