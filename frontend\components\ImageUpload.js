// frontend/components/ImageUpload.js
'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { uploadProductImage, deleteProductImage, setMainImage } from '@/lib/api';

export default function ImageUpload({ productId, images = [], onImagesChange }) {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileSelect = async (files) => {
    if (!files || files.length === 0) return;

    setUploading(true);
    try {
      const uploadPromises = Array.from(files).map(file => 
        uploadProductImage(productId, file)
      );
      
      const results = await Promise.all(uploadPromises);
      
      // تحديث قائمة الصور
      if (onImagesChange) {
        const newImages = results.map(result => result.image);
        onImagesChange([...images, ...newImages]);
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('فشل في رفع الصور: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e) => {
    handleFileSelect(e.target.files);
  };

  const handleDeleteImage = async (imageId) => {
    if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) return;

    try {
      await deleteProductImage(imageId);
      
      // تحديث قائمة الصور
      if (onImagesChange) {
        const updatedImages = images.filter(img => img.id !== imageId);
        onImagesChange(updatedImages);
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      alert('فشل في حذف الصورة: ' + error.message);
    }
  };

  const handleSetMainImage = async (imageId) => {
    try {
      await setMainImage(imageId);
      
      // تحديث قائمة الصور
      if (onImagesChange) {
        const updatedImages = images.map(img => ({
          ...img,
          isMain: img.id === imageId
        }));
        onImagesChange(updatedImages);
      }
    } catch (error) {
      console.error('Error setting main image:', error);
      alert('فشل في تحديد الصورة الرئيسية: ' + error.message);
    }
  };

  return (
    <div className="space-y-4">
      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
        
        {uploading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">جاري رفع الصور...</span>
          </div>
        ) : (
          <div>
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <p className="mt-2 text-sm text-gray-600">
              <span className="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
                اضغط لرفع الصور
              </span>
              {' '}أو اسحب الصور هنا
            </p>
            <p className="text-xs text-gray-500">PNG, JPG, GIF حتى 5MB</p>
          </div>
        )}
      </div>

      {/* عرض الصور المرفوعة */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square relative overflow-hidden rounded-lg border">
                <Image
                  src={`http://localhost:5000/${image.path}`}
                  alt={image.originalName}
                  fill
                  className="object-cover"
                />
                
                {/* شارة الصورة الرئيسية */}
                {image.isMain && (
                  <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                    رئيسية
                  </div>
                )}
                
                {/* أزرار التحكم */}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                  {!image.isMain && (
                    <button
                      onClick={() => handleSetMainImage(image.id)}
                      className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                    >
                      جعل رئيسية
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteImage(image.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                  >
                    حذف
                  </button>
                </div>
              </div>
              
              <p className="mt-1 text-xs text-gray-500 truncate">
                {image.originalName}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
