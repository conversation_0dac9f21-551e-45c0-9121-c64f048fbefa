// backend/src/controllers/invoiceController.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// @desc    Get all invoices
// @route   GET /api/invoices
// @access  Private
exports.getInvoices = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    const where = {};
    
    if (status && status !== 'all') {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search } },
        { customer: { name: { contains: search } } },
        { customer: { email: { contains: search } } }
      ];
    }

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        include: {
          customer: true,
          items: {
            include: {
              product: {
                include: {
                  images: {
                    where: { isMain: true },
                    take: 1
                  }
                }
              }
            }
          },
          payments: true
        },
        orderBy: { createdAt: 'desc' },
        skip: parseInt(skip),
        take: parseInt(limit)
      }),
      prisma.invoice.count({ where })
    ]);

    res.json({
      invoices,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ message: 'Failed to fetch invoices', error: error.message });
  }
};

// @desc    Get single invoice
// @route   GET /api/invoices/:id
// @access  Private
exports.getInvoiceById = async (req, res) => {
  const { id } = req.params;
  
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        },
        payments: {
          orderBy: { paymentDate: 'desc' }
        }
      }
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json(invoice);
  } catch (error) {
    console.error('Error fetching invoice:', error);
    res.status(500).json({ message: 'Failed to fetch invoice', error: error.message });
  }
};

// @desc    Create new invoice
// @route   POST /api/invoices
// @access  Private
exports.createInvoice = async (req, res) => {
  const {
    customerId,
    items,
    taxRate = 0,
    discountRate = 0,
    notes,
    terms,
    dueDate,
    showImages = true
  } = req.body;

  try {
    // التحقق من وجود العميل
    const customer = await prisma.customer.findUnique({
      where: { id: parseInt(customerId) }
    });

    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    // التحقق من وجود المنتجات وحساب المجاميع
    let subtotal = 0;
    const processedItems = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: parseInt(item.productId) }
      });

      if (!product) {
        return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
      }

      if (product.stock < item.quantity) {
        return res.status(400).json({ 
          message: `Insufficient stock for product ${product.name}. Available: ${product.stock}, Requested: ${item.quantity}` 
        });
      }

      const unitPrice = parseFloat(item.unitPrice || product.price);
      const quantity = parseInt(item.quantity);
      const discount = parseFloat(item.discount || 0);
      const itemTotal = (unitPrice * quantity) * (1 - discount / 100);

      processedItems.push({
        productId: product.id,
        quantity,
        unitPrice,
        discount,
        total: itemTotal
      });

      subtotal += itemTotal;
    }

    // حساب الضرائب والخصومات
    const discountAmount = subtotal * (parseFloat(discountRate) / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const taxAmount = subtotalAfterDiscount * (parseFloat(taxRate) / 100);
    const totalAmount = subtotalAfterDiscount + taxAmount;

    // إنشاء رقم فاتورة فريد
    const invoiceCount = await prisma.invoice.count();
    const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`;

    // إنشاء الفاتورة
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        customerId: parseInt(customerId),
        subtotal,
        taxRate: parseFloat(taxRate),
        taxAmount,
        discountRate: parseFloat(discountRate),
        discountAmount,
        totalAmount,
        notes,
        terms,
        dueDate: dueDate ? new Date(dueDate) : null,
        showImages,
        items: {
          create: processedItems
        }
      },
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        }
      }
    });

    // تحديث المخزون
    for (const item of processedItems) {
      await prisma.product.update({
        where: { id: item.productId },
        data: {
          stock: {
            decrement: item.quantity
          }
        }
      });

      // إضافة حركة مخزون
      await prisma.stockMovement.create({
        data: {
          productId: item.productId,
          type: 'OUT',
          quantity: -item.quantity,
          reason: 'Sale',
          reference: invoiceNumber,
          notes: `Sold via invoice ${invoiceNumber}`
        }
      });
    }

    res.status(201).json({
      message: 'Invoice created successfully',
      invoice
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({ message: 'Failed to create invoice', error: error.message });
  }
};

// @desc    Update invoice
// @route   PUT /api/invoices/:id
// @access  Private
exports.updateInvoice = async (req, res) => {
  const { id } = req.params;
  const {
    customerId,
    items,
    taxRate,
    discountRate,
    notes,
    terms,
    dueDate,
    showImages,
    status
  } = req.body;

  try {
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: { items: true }
    });

    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // لا يمكن تعديل الفواتير المدفوعة
    if (existingInvoice.status === 'paid' && status !== 'paid') {
      return res.status(400).json({ message: 'Cannot modify paid invoices' });
    }

    // إذا تم تغيير العناصر، نحتاج لإعادة حساب كل شيء
    let updateData = {
      notes,
      terms,
      dueDate: dueDate ? new Date(dueDate) : null,
      showImages,
      status
    };

    if (items && items.length > 0) {
      // إعادة المخزون للعناصر القديمة
      for (const oldItem of existingInvoice.items) {
        await prisma.product.update({
          where: { id: oldItem.productId },
          data: {
            stock: {
              increment: oldItem.quantity
            }
          }
        });
      }

      // حذف العناصر القديمة
      await prisma.invoiceItem.deleteMany({
        where: { invoiceId: parseInt(id) }
      });

      // معالجة العناصر الجديدة
      let subtotal = 0;
      const processedItems = [];

      for (const item of items) {
        const product = await prisma.product.findUnique({
          where: { id: parseInt(item.productId) }
        });

        if (!product) {
          return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
        }

        const unitPrice = parseFloat(item.unitPrice || product.price);
        const quantity = parseInt(item.quantity);
        const discount = parseFloat(item.discount || 0);
        const itemTotal = (unitPrice * quantity) * (1 - discount / 100);

        processedItems.push({
          productId: product.id,
          quantity,
          unitPrice,
          discount,
          total: itemTotal
        });

        subtotal += itemTotal;

        // تحديث المخزون
        await prisma.product.update({
          where: { id: product.id },
          data: {
            stock: {
              decrement: quantity
            }
          }
        });
      }

      // حساب المجاميع
      const discountAmount = subtotal * (parseFloat(discountRate || 0) / 100);
      const subtotalAfterDiscount = subtotal - discountAmount;
      const taxAmount = subtotalAfterDiscount * (parseFloat(taxRate || 0) / 100);
      const totalAmount = subtotalAfterDiscount + taxAmount;

      updateData = {
        ...updateData,
        customerId: parseInt(customerId),
        subtotal,
        taxRate: parseFloat(taxRate || 0),
        taxAmount,
        discountRate: parseFloat(discountRate || 0),
        discountAmount,
        totalAmount,
        items: {
          create: processedItems
        }
      };
    }

    const updatedInvoice = await prisma.invoice.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        customer: true,
        items: {
          include: {
            product: {
              include: {
                images: {
                  orderBy: [
                    { isMain: 'desc' },
                    { order: 'asc' }
                  ]
                }
              }
            }
          }
        },
        payments: true
      }
    });

    res.json({
      message: 'Invoice updated successfully',
      invoice: updatedInvoice
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({ message: 'Failed to update invoice', error: error.message });
  }
};

// @desc    Delete invoice
// @route   DELETE /api/invoices/:id
// @access  Private
exports.deleteInvoice = async (req, res) => {
  const { id } = req.params;

  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: { items: true, payments: true }
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // لا يمكن حذف الفواتير المدفوعة
    if (invoice.status === 'paid' || invoice.payments.length > 0) {
      return res.status(400).json({ message: 'Cannot delete paid invoices or invoices with payments' });
    }

    // إعادة المخزون
    for (const item of invoice.items) {
      await prisma.product.update({
        where: { id: item.productId },
        data: {
          stock: {
            increment: item.quantity
          }
        }
      });

      // إضافة حركة مخزون
      await prisma.stockMovement.create({
        data: {
          productId: item.productId,
          type: 'IN',
          quantity: item.quantity,
          reason: 'Invoice Cancelled',
          reference: invoice.invoiceNumber,
          notes: `Stock returned from cancelled invoice ${invoice.invoiceNumber}`
        }
      });
    }

    // حذف الفاتورة (سيتم حذف العناصر تلقائياً بسبب cascade)
    await prisma.invoice.delete({
      where: { id: parseInt(id) }
    });

    res.json({ message: 'Invoice deleted successfully' });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    res.status(500).json({ message: 'Failed to delete invoice', error: error.message });
  }
};

// @desc    Add payment to invoice
// @route   POST /api/invoices/:id/payments
// @access  Private
exports.addPayment = async (req, res) => {
  const { id } = req.params;
  const { amount, method, reference, notes } = req.body;

  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: { payments: true }
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    const totalPaid = invoice.payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const newTotalPaid = totalPaid + parseFloat(amount);

    if (newTotalPaid > parseFloat(invoice.totalAmount)) {
      return res.status(400).json({ 
        message: 'Payment amount exceeds remaining balance',
        remaining: parseFloat(invoice.totalAmount) - totalPaid
      });
    }

    // إضافة الدفعة
    const payment = await prisma.payment.create({
      data: {
        invoiceId: parseInt(id),
        amount: parseFloat(amount),
        method,
        reference,
        notes
      }
    });

    // تحديث حالة الدفع في الفاتورة
    let paymentStatus = 'partial';
    if (newTotalPaid >= parseFloat(invoice.totalAmount)) {
      paymentStatus = 'paid';
    }

    await prisma.invoice.update({
      where: { id: parseInt(id) },
      data: {
        paidAmount: newTotalPaid,
        paymentStatus,
        status: paymentStatus === 'paid' ? 'paid' : invoice.status
      }
    });

    res.status(201).json({
      message: 'Payment added successfully',
      payment
    });
  } catch (error) {
    console.error('Error adding payment:', error);
    res.status(500).json({ message: 'Failed to add payment', error: error.message });
  }
};

// @desc    Get invoice statistics
// @route   GET /api/invoices/stats
// @access  Private
exports.getInvoiceStats = async (req, res) => {
  try {
    const [
      totalInvoices,
      paidInvoices,
      unpaidInvoices,
      overdueInvoices,
      totalRevenue,
      pendingRevenue
    ] = await Promise.all([
      prisma.invoice.count(),
      prisma.invoice.count({ where: { paymentStatus: 'paid' } }),
      prisma.invoice.count({ where: { paymentStatus: 'unpaid' } }),
      prisma.invoice.count({ 
        where: { 
          paymentStatus: { not: 'paid' },
          dueDate: { lt: new Date() }
        } 
      }),
      prisma.invoice.aggregate({
        where: { paymentStatus: 'paid' },
        _sum: { totalAmount: true }
      }),
      prisma.invoice.aggregate({
        where: { paymentStatus: { not: 'paid' } },
        _sum: { totalAmount: true }
      })
    ]);

    res.json({
      totalInvoices,
      paidInvoices,
      unpaidInvoices,
      overdueInvoices,
      totalRevenue: totalRevenue._sum.totalAmount || 0,
      pendingRevenue: pendingRevenue._sum.totalAmount || 0
    });
  } catch (error) {
    console.error('Error fetching invoice stats:', error);
    res.status(500).json({ message: 'Failed to fetch invoice statistics', error: error.message });
  }
};
