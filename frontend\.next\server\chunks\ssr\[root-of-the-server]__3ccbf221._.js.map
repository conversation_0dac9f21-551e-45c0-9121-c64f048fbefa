{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function Home() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    // التحقق من حالة تسجيل الدخول\n    const checkAuth = () => {\n      const user = localStorage.getItem('user');\n      if (user) {\n        setIsAuthenticated(true);\n        // إعادة توجيه المستخدم المسجل إلى لوحة التحكم\n        router.push('/dashboard');\n      } else {\n        setIsAuthenticated(false);\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [router]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900\">نظام إدارة الفواتير</h1>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {!isAuthenticated && (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                  >\n                    تسجيل الدخول\n                  </Link>\n                  <Link\n                    href=\"/register\"\n                    className=\"bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    إنشاء حساب\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <div className=\"relative overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              نظام إدارة الفواتير\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600\">\n                الاحترافي\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              نظام شامل لإدارة الفواتير والمنتجات والعملاء مع دعم الصور والتقارير المتقدمة\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              {!isAuthenticated ? (\n                <>\n                  <Link\n                    href=\"/register\"\n                    className=\"inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                    ابدأ الآن مجاناً\n                  </Link>\n                  <Link\n                    href=\"/login\"\n                    className=\"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-lg font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200\"\n                  >\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                    </svg>\n                    تسجيل الدخول\n                  </Link>\n                </>\n              ) : (\n                <Link\n                  href=\"/dashboard\"\n                  className=\"inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl\"\n                >\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                  الذهاب إلى لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">الميزات الرئيسية</h2>\n            <p className=\"text-lg text-gray-600\">كل ما تحتاجه لإدارة أعمالك بكفاءة</p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Feature 1 */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">فواتير احترافية</h3>\n              <p className=\"text-gray-600\">إنشاء فواتير احترافية مع دعم الصور والتخصيص الكامل</p>\n            </div>\n\n            {/* Feature 2 */}\n            <div className=\"bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">إدارة المنتجات</h3>\n              <p className=\"text-gray-600\">إدارة شاملة للمنتجات مع الصور والمخزون والتصنيفات</p>\n            </div>\n\n            {/* Feature 3 */}\n            <div className=\"bg-gradient-to-br from-purple-50 to-pink-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">إدارة العملاء</h3>\n              <p className=\"text-gray-600\">قاعدة بيانات شاملة للعملاء مع تتبع المدفوعات</p>\n            </div>\n\n            {/* Feature 4 */}\n            <div className=\"bg-gradient-to-br from-orange-50 to-red-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">تقارير متقدمة</h3>\n              <p className=\"text-gray-600\">تقارير تفصيلية ورسوم بيانية لتحليل الأداء</p>\n            </div>\n\n            {/* Feature 5 */}\n            <div className=\"bg-gradient-to-br from-teal-50 to-cyan-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">إدارة المدفوعات</h3>\n              <p className=\"text-gray-600\">تتبع المدفوعات والمستحقات مع تنبيهات ذكية</p>\n            </div>\n\n            {/* Feature 6 */}\n            <div className=\"bg-gradient-to-br from-gray-50 to-slate-100 rounded-xl p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-gray-500 to-slate-600 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">إعدادات متقدمة</h3>\n              <p className=\"text-gray-600\">تخصيص كامل للنظام مع نسخ احتياطي وأمان عالي</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      {!isAuthenticated && (\n        <div className=\"bg-gradient-to-r from-indigo-600 to-purple-600 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-3xl font-bold text-white mb-4\">\n              جاهز للبدء؟\n            </h2>\n            <p className=\"text-xl text-indigo-100 mb-8\">\n              انضم إلى آلاف المستخدمين الذين يثقون في نظامنا\n            </p>\n            <Link\n              href=\"/register\"\n              className=\"inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-xl text-indigo-600 bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl\"\n            >\n              إنشاء حساب مجاني\n              <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n              </svg>\n            </Link>\n          </div>\n        </div>\n      )}\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold\">نظام إدارة الفواتير</h3>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              نظام شامل ومتطور لإدارة الفواتير والأعمال\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              © 2024 جميع الحقوق محفوظة\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,MAAM,YAAY;YAChB,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,MAAM;gBACR,mBAAmB;gBACnB,8CAA8C;gBAC9C,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,mBAAmB;YACrB;YACA,WAAW;QACb;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;0CACZ,CAAC,iCACA;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAqF;;;;;;;;;;;;0CAIvG,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;0CACZ,CAAC,gCACA;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;iEAKV,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;kEAC5E,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;kEACrE,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOpC,CAAC,iCACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA+B;;;;;;sCAG5C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/E,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAAoB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}