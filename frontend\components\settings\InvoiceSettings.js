// frontend/components/settings/InvoiceSettings.js
'use client';

import { useState } from 'react';
import InvoiceDisplaySettings from './invoice/InvoiceDisplaySettings';

export default function InvoiceSettings({ settings, onSave, loading }) {
  const [activeTab, setActiveTab] = useState('general');

  const [formData, setFormData] = useState({
    invoicePrefix: settings?.invoicePrefix || 'INV',
    invoiceNumberLength: settings?.invoiceNumberLength || 6,
    defaultTaxRate: settings?.defaultTaxRate || 0,
    defaultPaymentTerms: settings?.defaultPaymentTerms || '',
    autoGenerateInvoiceNumber: settings?.autoGenerateInvoiceNumber ?? true,
    showImagesInInvoice: settings?.showImagesInInvoice ?? true,
    allowPartialPayments: settings?.allowPartialPayments ?? true,
    requireCustomerInfo: settings?.requireCustomerInfo ?? true,
    defaultDueDays: settings?.defaultDueDays || 30,
    invoiceFooter: settings?.invoiceFooter || '',
    invoiceNotes: settings?.invoiceNotes || ''
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const tabs = [
    { id: 'general', name: 'الإعدادات العامة', icon: '⚙️' },
    { id: 'display', name: 'إعدادات العرض', icon: '👁️' }
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
        <div className="flex items-center">
          <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h2 className="text-lg font-semibold text-white">إعدادات الفواتير</h2>
        </div>
      </div>

      {/* التبويبات */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'general' && (
          <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* ترقيم الفواتير */}
          <div className="md:col-span-2">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
              ترقيم الفواتير
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">بادئة الفاتورة</label>
                <input
                  type="text"
                  value={formData.invoicePrefix}
                  onChange={(e) => handleInputChange('invoicePrefix', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                  placeholder="INV"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">طول الرقم</label>
                <input
                  type="number"
                  min="4"
                  max="10"
                  value={formData.invoiceNumberLength}
                  onChange={(e) => handleInputChange('invoiceNumberLength', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                />
              </div>
              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.autoGenerateInvoiceNumber}
                    onChange={(e) => handleInputChange('autoGenerateInvoiceNumber', e.target.checked)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">ترقيم تلقائي</span>
                </label>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              مثال: {formData.invoicePrefix}-{String(1).padStart(formData.invoiceNumberLength, '0')}
            </p>
          </div>

          {/* الضرائب والدفع */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">معدل الضريبة الافتراضي (%)</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="100"
              value={formData.defaultTaxRate}
              onChange={(e) => handleInputChange('defaultTaxRate', parseFloat(e.target.value) || 0)}
              className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
              placeholder="15.00"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">مدة الاستحقاق الافتراضية (أيام)</label>
            <input
              type="number"
              min="0"
              value={formData.defaultDueDays}
              onChange={(e) => handleInputChange('defaultDueDays', parseInt(e.target.value) || 0)}
              className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
              placeholder="30"
            />
          </div>

          {/* شروط الدفع الافتراضية */}
          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">شروط الدفع الافتراضية</label>
            <textarea
              value={formData.defaultPaymentTerms}
              onChange={(e) => handleInputChange('defaultPaymentTerms', e.target.value)}
              rows="3"
              className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none"
              placeholder="الدفع خلال 30 يوم من تاريخ الفاتورة..."
            />
          </div>

          {/* الخيارات المتقدمة */}
          <div className="md:col-span-2">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              خيارات متقدمة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.showImagesInInvoice}
                  onChange={(e) => handleInputChange('showImagesInInvoice', e.target.checked)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">إظهار صور المنتجات في الفواتير</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.allowPartialPayments}
                  onChange={(e) => handleInputChange('allowPartialPayments', e.target.checked)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">السماح بالدفعات الجزئية</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.requireCustomerInfo}
                  onChange={(e) => handleInputChange('requireCustomerInfo', e.target.checked)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">إجبارية معلومات العميل</span>
              </label>
            </div>
          </div>

          {/* ملاحظات الفاتورة */}
          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">الملاحظات الافتراضية</label>
            <textarea
              value={formData.invoiceNotes}
              onChange={(e) => handleInputChange('invoiceNotes', e.target.value)}
              rows="3"
              className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none"
              placeholder="شكراً لتعاملكم معنا..."
            />
          </div>

          {/* تذييل الفاتورة */}
          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">تذييل الفاتورة</label>
            <textarea
              value={formData.invoiceFooter}
              onChange={(e) => handleInputChange('invoiceFooter', e.target.value)}
              rows="2"
              className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none"
              placeholder="معلومات إضافية تظهر في أسفل الفاتورة..."
            />
          </div>
        </div>

        <div className="flex justify-end mt-8">
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            {loading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z"></path>
                </svg>
                جاري الحفظ...
              </span>
            ) : (
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                حفظ الإعدادات
              </span>
            )}
          </button>
        </div>
          </form>
        )}

        {activeTab === 'display' && (
          <InvoiceDisplaySettings
            settings={settings?.invoice?.display || {}}
            onSave={async (data) => {
              try {
                const settingsService = (await import('@/lib/settingsService')).default;
                await settingsService.saveInvoiceDisplaySettings(data);

                // تحديث الحالة المحلية
                setSettings(prev => ({
                  ...prev,
                  invoice: {
                    ...prev.invoice,
                    display: data
                  }
                }));

                // إشعار بالنجاح
                alert('✅ تم حفظ إعدادات العرض بنجاح!');
              } catch (error) {
                console.error('Error saving display settings:', error);
                alert('❌ حدث خطأ أثناء حفظ الإعدادات');
              }
            }}
            loading={loading}
          />
        )}
      </div>
    </div>
  );
}
