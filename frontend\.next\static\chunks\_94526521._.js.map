{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || \n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n                \n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n            \n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n        \n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n          \n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || \n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n                \n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n          \n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,6LAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,6LAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GApPwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/StatsCard.js"], "sourcesContent": ["// frontend/components/dashboard/StatsCard.js\n'use client';\n\nexport default function StatsCard({ \n  title, \n  value, \n  icon, \n  color = 'blue', \n  trend = null,\n  loading = false \n}) {\n  const colorClasses = {\n    blue: 'bg-blue-100 text-blue-600',\n    green: 'bg-green-100 text-green-600',\n    yellow: 'bg-yellow-100 text-yellow-600',\n    red: 'bg-red-100 text-red-600',\n    purple: 'bg-purple-100 text-purple-600',\n    indigo: 'bg-indigo-100 text-indigo-600'\n  };\n\n  const trendColors = {\n    up: 'text-green-600',\n    down: 'text-red-600',\n    neutral: 'text-gray-600'\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"p-6 animate-pulse\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n            <div className=\"ml-4 flex-1\">\n              <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-6 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300\">\n      <div className=\"p-6\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>\n              {icon}\n            </div>\n          </div>\n          <div className=\"ml-4 flex-1\">\n            <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n            <div className=\"flex items-center\">\n              <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n              {trend && (\n                <div className={`ml-2 flex items-center text-sm ${trendColors[trend.direction]}`}>\n                  {trend.direction === 'up' && (\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 17l9.2-9.2M17 17V7H7\" />\n                    </svg>\n                  )}\n                  {trend.direction === 'down' && (\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 7l-9.2 9.2M7 7v10h10\" />\n                    </svg>\n                  )}\n                  <span>{trend.value}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAC7C;;AAEe,SAAS,UAAU,EAChC,KAAK,EACL,KAAK,EACL,IAAI,EACJ,QAAQ,MAAM,EACd,QAAQ,IAAI,EACZ,UAAU,KAAK,EAChB;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,MAAM;QACN,SAAS;IACX;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,CAAC,sDAAsD,EAAE,YAAY,CAAC,MAAM,EAAE;sCAC3F;;;;;;;;;;;kCAGL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAChD,uBACC,6LAAC;wCAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW,CAAC,MAAM,SAAS,CAAC,EAAE;;4CAC7E,MAAM,SAAS,KAAK,sBACnB,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CAGxE,MAAM,SAAS,KAAK,wBACnB,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAGzE,6LAAC;0DAAM,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;KAzEwB", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/ChartCard.js"], "sourcesContent": ["// frontend/components/dashboard/ChartCard.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function ChartCard({ \n  title, \n  data = [], \n  type = 'bar',\n  color = 'blue',\n  loading = false \n}) {\n  const [timeRange, setTimeRange] = useState('7d');\n\n  const colorClasses = {\n    blue: 'from-blue-500 to-cyan-600',\n    green: 'from-green-500 to-emerald-600',\n    purple: 'from-purple-500 to-pink-600',\n    orange: 'from-orange-500 to-red-600'\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4\">\n          <div className=\"h-6 bg-gray-300 rounded w-1/3\"></div>\n        </div>\n        <div className=\"p-6 animate-pulse\">\n          <div className=\"space-y-3\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-gray-200 rounded\"></div>\n                <div className=\"flex-1 h-4 bg-gray-200 rounded\"></div>\n                <div className=\"w-16 h-4 bg-gray-200 rounded\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const maxValue = Math.max(...data.map(item => item.value));\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className={`bg-gradient-to-r ${colorClasses[color]} px-6 py-4`}>\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-white\">{title}</h3>\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value)}\n            className=\"bg-white bg-opacity-20 text-white text-sm rounded-lg px-3 py-1 border border-white border-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\"\n          >\n            <option value=\"7d\" className=\"text-gray-900\">آخر 7 أيام</option>\n            <option value=\"30d\" className=\"text-gray-900\">آخر 30 يوم</option>\n            <option value=\"90d\" className=\"text-gray-900\">آخر 3 أشهر</option>\n            <option value=\"1y\" className=\"text-gray-900\">آخر سنة</option>\n          </select>\n        </div>\n      </div>\n      \n      <div className=\"p-6\">\n        {data.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <p className=\"text-gray-500\">لا توجد بيانات للعرض</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {data.map((item, index) => (\n              <div key={index} className=\"flex items-center space-x-3\">\n                <div className=\"flex-shrink-0 w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm font-medium text-gray-700\">{item.label}</span>\n                    <span className=\"text-sm text-gray-500\">{item.value}</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`bg-gradient-to-r ${colorClasses[color]} h-2 rounded-full transition-all duration-500 ease-out`}\n                      style={{ width: `${(item.value / maxValue) * 100}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAG7C;;;AAFA;;AAIe,SAAS,UAAU,EAChC,KAAK,EACL,OAAO,EAAE,EACT,OAAO,KAAK,EACZ,QAAQ,MAAM,EACd,UAAU,KAAK,EAChB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;+<PERSON><PERSON><PERSON>,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAHP;;;;;;;;;;;;;;;;;;;;;IAUtB;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;0BACjE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;oCAAK,WAAU;8CAAgB;;;;;;8CAC7C,6LAAC;oCAAO,OAAM;oCAAM,WAAU;8CAAgB;;;;;;8CAC9C,6LAAC;oCAAO,OAAM;oCAAM,WAAU;8CAAgB;;;;;;8CAC9C,6LAAC;oCAAO,OAAM;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAKnD,6LAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,KAAK,kBACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAqC,KAAK,KAAK;;;;;;8DAC/D,6LAAC;oDAAK,WAAU;8DAAyB,KAAK,KAAK;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,sDAAsD,CAAC;gDAC1G,OAAO;oDAAE,OAAO,GAAG,AAAC,KAAK,KAAK,GAAG,WAAY,IAAI,CAAC,CAAC;gDAAC;;;;;;;;;;;;;;;;;;2BAVlD;;;;;;;;;;;;;;;;;;;;;AAqBxB;GAzFwB;KAAA", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/RecentActivity.js"], "sourcesContent": ["// frontend/components/dashboard/RecentActivity.js\n'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nexport default function RecentActivity({ activities = [], loading = false }) {\n  const getActivityIcon = (type) => {\n    const icons = {\n      invoice_created: (\n        <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      ),\n      payment_received: (\n        <svg className=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n        </svg>\n      ),\n      product_added: (\n        <svg className=\"w-5 h-5 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      ),\n      customer_added: (\n        <svg className=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      ),\n      stock_low: (\n        <svg className=\"w-5 h-5 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n        </svg>\n      )\n    };\n    return icons[type] || icons.invoice_created;\n  };\n\n  const getActivityColor = (type) => {\n    const colors = {\n      invoice_created: 'bg-blue-100',\n      payment_received: 'bg-green-100',\n      product_added: 'bg-purple-100',\n      customer_added: 'bg-orange-100',\n      stock_low: 'bg-red-100'\n    };\n    return colors[type] || 'bg-gray-100';\n  };\n\n  const formatTimeAgo = (date) => {\n    const now = new Date();\n    const activityDate = new Date(date);\n    const diffInMinutes = Math.floor((now - activityDate) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'الآن';\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `منذ ${diffInDays} يوم`;\n    \n    return activityDate.toLocaleDateString('ar-SA');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4\">\n          <div className=\"h-6 bg-gray-300 rounded w-1/3\"></div>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3 animate-pulse\">\n                <div className=\"w-10 h-10 bg-gray-200 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-white\">الأنشطة الأخيرة</h3>\n          <Link \n            href=\"/dashboard/activity\" \n            className=\"text-white text-sm hover:text-gray-200 transition-colors\"\n          >\n            عرض الكل\n          </Link>\n        </div>\n      </div>\n      \n      <div className=\"p-6\">\n        {activities.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <p className=\"text-gray-500\">لا توجد أنشطة حديثة</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {activities.map((activity, index) => (\n              <div key={index} className=\"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\">\n                <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`}>\n                  {getActivityIcon(activity.type)}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">\n                      {activity.title}\n                    </p>\n                    <span className=\"text-xs text-gray-500 flex-shrink-0\">\n                      {formatTimeAgo(activity.createdAt)}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 mt-1\">{activity.description}</p>\n                  {activity.link && (\n                    <Link \n                      href={activity.link}\n                      className=\"text-xs text-indigo-600 hover:text-indigo-800 mt-1 inline-block\"\n                    >\n                      عرض التفاصيل →\n                    </Link>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;AAGlD;AACA;AAHA;;;;AAKe,SAAS,eAAe,EAAE,aAAa,EAAE,EAAE,UAAU,KAAK,EAAE;IACzE,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ;YACZ,+BACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,gCACE,6LAAC;gBAAI,WAAU;gBAAyB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAChF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,6BACE,6LAAC;gBAAI,WAAU;gBAA0B,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,8BACE,6LAAC;gBAAI,WAAU;gBAA0B,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,yBACE,6LAAC;gBAAI,WAAU;gBAAuB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC9E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA,OAAO,KAAK,CAAC,KAAK,IAAI,MAAM,eAAe;IAC7C;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,iBAAiB;YACjB,kBAAkB;YAClB,eAAe;YACf,gBAAgB;YAChB,WAAW;QACb;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK;QAC9B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,YAAY,IAAI,CAAC,OAAO,EAAE;QAElE,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC;QAE3D,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC;QAEtD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,aAAa,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC;QAElD,OAAO,aAAa,kBAAkB,CAAC;IACzC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAW,CAAC,sEAAsE,EAAE,iBAAiB,SAAS,IAAI,GAAG;8CACvH,gBAAgB,SAAS,IAAI;;;;;;8CAEhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,SAAS,KAAK;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DACb,cAAc,SAAS,SAAS;;;;;;;;;;;;sDAGrC,6LAAC;4CAAE,WAAU;sDAA8B,SAAS,WAAW;;;;;;wCAC9D,SAAS,IAAI,kBACZ,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,SAAS,IAAI;4CACnB,WAAU;sDACX;;;;;;;;;;;;;2BAlBG;;;;;;;;;;;;;;;;;;;;;AA8BxB;KA1IwB", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/QuickActions.js"], "sourcesContent": ["// frontend/components/dashboard/QuickActions.js\n'use client';\n\nimport Link from 'next/link';\n\nexport default function QuickActions() {\n  const actions = [\n    {\n      title: 'إنشاء فاتورة جديدة',\n      description: 'إنشاء فاتورة جديدة للعملاء',\n      href: '/dashboard/invoices/create',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      ),\n      color: 'from-blue-500 to-cyan-600',\n      bgColor: 'bg-blue-50 hover:bg-blue-100'\n    },\n    {\n      title: 'إضافة منتج جديد',\n      description: 'إضافة منتج جديد للمخزون',\n      href: '/dashboard/products/add',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      ),\n      color: 'from-green-500 to-emerald-600',\n      bgColor: 'bg-green-50 hover:bg-green-100'\n    },\n    {\n      title: 'إضافة عميل جديد',\n      description: 'إضافة عميل جديد للنظام',\n      href: '/dashboard/customers/add',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      ),\n      color: 'from-purple-500 to-pink-600',\n      bgColor: 'bg-purple-50 hover:bg-purple-100'\n    },\n    {\n      title: 'عرض التقارير',\n      description: 'مراجعة التقارير والإحصائيات',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      ),\n      color: 'from-orange-500 to-red-600',\n      bgColor: 'bg-orange-50 hover:bg-orange-100'\n    },\n    {\n      title: 'إدارة المخزون',\n      description: 'مراجعة وإدارة المخزون',\n      href: '/dashboard/inventory',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n        </svg>\n      ),\n      color: 'from-teal-500 to-cyan-600',\n      bgColor: 'bg-teal-50 hover:bg-teal-100'\n    },\n    {\n      title: 'الإعدادات',\n      description: 'إعدادات النظام والتخصيص',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n      color: 'from-gray-500 to-gray-600',\n      bgColor: 'bg-gray-50 hover:bg-gray-100'\n    }\n  ];\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n        <h3 className=\"text-lg font-semibold text-white\">الإجراءات السريعة</h3>\n      </div>\n      \n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {actions.map((action, index) => (\n            <Link\n              key={index}\n              href={action.href}\n              className={`block p-4 rounded-lg border-2 border-transparent ${action.bgColor} transition-all duration-200 hover:border-gray-200 hover:shadow-md group`}\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className={`flex-shrink-0 w-10 h-10 rounded-lg bg-gradient-to-r ${action.color} flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-200`}>\n                  {action.icon}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-sm font-semibold text-gray-900 group-hover:text-gray-700 transition-colors\">\n                    {action.title}\n                  </h4>\n                  <p className=\"text-xs text-gray-600 mt-1 group-hover:text-gray-500 transition-colors\">\n                    {action.description}\n                  </p>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAGhD;AAFA;;;AAIe,SAAS;IACtB,MAAM,UAAU;QACd;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAmC;;;;;;;;;;;0BAGnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,OAAO,IAAI;4BACjB,WAAW,CAAC,iDAAiD,EAAE,OAAO,OAAO,CAAC,wEAAwE,CAAC;sCAEvJ,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,oDAAoD,EAAE,OAAO,KAAK,CAAC,oGAAoG,CAAC;kDACtL,OAAO,IAAI;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,OAAO,KAAK;;;;;;0DAEf,6LAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;2BAbpB;;;;;;;;;;;;;;;;;;;;;AAuBnB;KA9GwB", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/dashboard/LowStockAlert.js"], "sourcesContent": ["// frontend/components/dashboard/LowStockAlert.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { getProducts } from '@/lib/api';\n\nexport default function LowStockAlert() {\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    loadLowStockProducts();\n  }, []);\n\n  const loadLowStockProducts = async () => {\n    try {\n      const products = await getProducts();\n      // فلترة المنتجات التي مخزونها أقل من الحد الأدنى\n      const lowStock = products.filter(product => \n        product.stock <= (product.minStock || 5)\n      );\n      setLowStockProducts(lowStock.slice(0, 5)); // أول 5 منتجات فقط\n    } catch (err) {\n      setError('Failed to load low stock products');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStockStatus = (product) => {\n    if (product.stock === 0) {\n      return { text: 'نفد المخزون', color: 'text-red-600 bg-red-100' };\n    } else if (product.stock <= (product.minStock || 5)) {\n      return { text: 'مخزون منخفض', color: 'text-yellow-600 bg-yellow-100' };\n    }\n    return { text: 'متوفر', color: 'text-green-600 bg-green-100' };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4\">\n          <div className=\"h-6 bg-gray-300 rounded w-1/3\"></div>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3 animate-pulse\">\n                <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className=\"bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-5 h-5 text-white mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-white\">تنبيهات المخزون</h3>\n          </div>\n          {lowStockProducts.length > 0 && (\n            <span className=\"bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full\">\n              {lowStockProducts.length}\n            </span>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"p-6\">\n        {error ? (\n          <div className=\"text-center py-4\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        ) : lowStockProducts.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <svg className=\"mx-auto h-12 w-12 text-green-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <p className=\"text-green-600 font-medium\">جميع المنتجات متوفرة!</p>\n            <p className=\"text-gray-500 text-sm mt-1\">لا توجد منتجات بمخزون منخفض</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {lowStockProducts.map((product, index) => {\n              const status = getStockStatus(product);\n              return (\n                <div key={index} className=\"flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\">\n                  <div className=\"flex-shrink-0\">\n                    {product.images && product.images.length > 0 ? (\n                      <Image\n                        src={`http://localhost:5000/${product.images[0].path}`}\n                        alt={product.name}\n                        width={48}\n                        height={48}\n                        className=\"w-12 h-12 rounded-lg object-cover border border-gray-200\"\n                      />\n                    ) : (\n                      <div className=\"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center\">\n                        <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                        </svg>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n                        {product.name}\n                      </h4>\n                      <span className={`text-xs px-2 py-1 rounded-full ${status.color}`}>\n                        {status.text}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between mt-1\">\n                      <p className=\"text-sm text-gray-600\">\n                        المخزون: <span className=\"font-medium\">{product.stock}</span>\n                        {product.minStock && (\n                          <span className=\"text-gray-400\"> / الحد الأدنى: {product.minStock}</span>\n                        )}\n                      </p>\n                      <Link\n                        href={`/dashboard/products/edit/${product.id}`}\n                        className=\"text-xs text-blue-600 hover:text-blue-800 font-medium\"\n                      >\n                        تحديث المخزون\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n            \n            {lowStockProducts.length > 0 && (\n              <div className=\"pt-4 border-t border-gray-200\">\n                <Link\n                  href=\"/dashboard/products?filter=low_stock\"\n                  className=\"block w-full text-center py-2 px-4 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors text-sm font-medium\"\n                >\n                  عرض جميع المنتجات منخفضة المخزون\n                </Link>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;AAGjD;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD;YACjC,iDAAiD;YACjD,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,UAC/B,QAAQ,KAAK,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC;YAEzC,oBAAoB,SAAS,KAAK,CAAC,GAAG,KAAK,mBAAmB;QAChE,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,KAAK,KAAK,GAAG;YACvB,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAA0B;QACjE,OAAO,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,GAAG;YACnD,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAgC;QACvE;QACA,OAAO;YAAE,MAAM;YAAS,OAAO;QAA8B;IAC/D;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;wBAElD,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;4BAAK,WAAU;sCACb,iBAAiB,MAAM;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;0BACZ,sBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;2BAErC,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAwC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/F,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,6LAAC;oBAAI,WAAU;;wBACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS;4BAC9B,MAAM,SAAS,eAAe;4BAC9B,qBACE,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,kBACzC,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,CAAC,sBAAsB,EAAE,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE;4CACtD,KAAK,QAAQ,IAAI;4CACjB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;iEAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;kDAK7E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;kEAEf,6LAAC;wDAAK,WAAW,CAAC,+BAA+B,EAAE,OAAO,KAAK,EAAE;kEAC9D,OAAO,IAAI;;;;;;;;;;;;0DAGhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAwB;0EAC1B,6LAAC;gEAAK,WAAU;0EAAe,QAAQ,KAAK;;;;;;4DACpD,QAAQ,QAAQ,kBACf,6LAAC;gEAAK,WAAU;;oEAAgB;oEAAiB,QAAQ,QAAQ;;;;;;;;;;;;;kEAGrE,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE,EAAE;wDAC9C,WAAU;kEACX;;;;;;;;;;;;;;;;;;;+BArCG;;;;;wBA4Cd;wBAEC,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA1JwB;KAAA", "debugId": null}}, {"offset": {"line": 2910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/page.js"], "sourcesContent": ["// frontend/app/dashboard/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getInvoiceStats, getProducts, getCustomers } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport StatsCard from '@/components/dashboard/StatsCard';\nimport ChartCard from '@/components/dashboard/ChartCard';\nimport RecentActivity from '@/components/dashboard/RecentActivity';\nimport QuickActions from '@/components/dashboard/QuickActions';\nimport LowStockAlert from '@/components/dashboard/LowStockAlert';\n\nfunction DashboardPage() {\n  const [stats, setStats] = useState({});\n  const [chartData, setChartData] = useState([]);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // تحميل الإحصائيات\n      const [invoiceStats, products, customers] = await Promise.all([\n        getInvoiceStats(),\n        getProducts(),\n        getCustomers()\n      ]);\n\n      // حساب إحصائيات إضافية\n      const totalProducts = products.length;\n      const lowStockProducts = products.filter(p => p.stock <= (p.minStock || 5)).length;\n      const totalCustomers = customers.length;\n\n      setStats({\n        ...invoiceStats,\n        totalProducts,\n        lowStockProducts,\n        totalCustomers\n      });\n\n      // بيانات الرسم البياني (مبيعات آخر 7 أيام)\n      const salesData = [\n        { label: 'الأحد', value: 1200 },\n        { label: 'الاثنين', value: 1900 },\n        { label: 'الثلاثاء', value: 800 },\n        { label: 'الأربعاء', value: 1500 },\n        { label: 'الخميس', value: 2100 },\n        { label: 'الجمعة', value: 1800 },\n        { label: 'السبت', value: 2400 }\n      ];\n      setChartData(salesData);\n\n      // الأنشطة الأخيرة (بيانات تجريبية)\n      const recentActivities = [\n        {\n          type: 'invoice_created',\n          title: 'فاتورة جديدة',\n          description: 'تم إنشاء فاتورة INV-000123 للعميل أحمد محمد',\n          createdAt: new Date(Date.now() - 5 * 60 * 1000), // منذ 5 دقائق\n          link: '/dashboard/invoices/123'\n        },\n        {\n          type: 'payment_received',\n          title: 'دفعة مستلمة',\n          description: 'تم استلام دفعة بقيمة $500 للفاتورة INV-000122',\n          createdAt: new Date(Date.now() - 15 * 60 * 1000), // منذ 15 دقيقة\n          link: '/dashboard/invoices/122'\n        },\n        {\n          type: 'product_added',\n          title: 'منتج جديد',\n          description: 'تم إضافة منتج \"لابتوب Dell XPS 13\" للمخزون',\n          createdAt: new Date(Date.now() - 30 * 60 * 1000), // منذ 30 دقيقة\n          link: '/dashboard/products'\n        },\n        {\n          type: 'stock_low',\n          title: 'تنبيه مخزون',\n          description: 'مخزون منتج \"ماوس لاسلكي\" أصبح منخفضاً (5 قطع متبقية)',\n          createdAt: new Date(Date.now() - 45 * 60 * 1000), // منذ 45 دقيقة\n          link: '/dashboard/products'\n        },\n        {\n          type: 'customer_added',\n          title: 'عميل جديد',\n          description: 'تم إضافة عميل جديد: شركة التقنية المتقدمة',\n          createdAt: new Date(Date.now() - 60 * 60 * 1000), // منذ ساعة\n          link: '/dashboard/customers'\n        }\n      ];\n      setActivities(recentActivities);\n\n    } catch (err) {\n      setError('Failed to load dashboard data');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">لوحة التحكم</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"hidden sm:flex items-center space-x-2 text-sm text-gray-500\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>\n                </div>\n                <button\n                  onClick={loadDashboardData}\n                  className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                  </svg>\n                  تحديث\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* الإحصائيات الرئيسية */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <StatsCard\n            title=\"إجمالي الفواتير\"\n            value={stats.totalInvoices || 0}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            }\n            color=\"blue\"\n            loading={loading}\n            trend={{ direction: 'up', value: '+12%' }}\n          />\n\n          <StatsCard\n            title=\"الإيرادات المحصلة\"\n            value={formatCurrency(stats.totalRevenue || 0)}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            }\n            color=\"green\"\n            loading={loading}\n            trend={{ direction: 'up', value: '+8%' }}\n          />\n\n          <StatsCard\n            title=\"إجمالي المنتجات\"\n            value={stats.totalProducts || 0}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n              </svg>\n            }\n            color=\"purple\"\n            loading={loading}\n          />\n\n          <StatsCard\n            title=\"إجمالي العملاء\"\n            value={stats.totalCustomers || 0}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            }\n            color=\"indigo\"\n            loading={loading}\n            trend={{ direction: 'up', value: '+5%' }}\n          />\n        </div>\n\n        {/* الإحصائيات الثانوية */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <StatsCard\n            title=\"في انتظار الدفع\"\n            value={formatCurrency(stats.pendingRevenue || 0)}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            }\n            color=\"yellow\"\n            loading={loading}\n          />\n\n          <StatsCard\n            title=\"فواتير متأخرة\"\n            value={stats.overdueInvoices || 0}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            }\n            color=\"red\"\n            loading={loading}\n          />\n\n          <StatsCard\n            title=\"منتجات منخفضة المخزون\"\n            value={stats.lowStockProducts || 0}\n            icon={\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            }\n            color=\"red\"\n            loading={loading}\n          />\n        </div>\n\n        {/* الإجراءات السريعة */}\n        <div className=\"mb-8\">\n          <QuickActions />\n        </div>\n\n        {/* المحتوى الرئيسي */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* العمود الأيسر - الرسوم البيانية والأنشطة */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* الرسم البياني للمبيعات */}\n            <ChartCard\n              title=\"المبيعات الأسبوعية\"\n              data={chartData}\n              color=\"blue\"\n              loading={loading}\n            />\n\n            {/* الأنشطة الأخيرة */}\n            <RecentActivity\n              activities={activities}\n              loading={loading}\n            />\n          </div>\n\n          {/* العمود الأيمن - التنبيهات */}\n          <div className=\"lg:col-span-1\">\n            <LowStockAlert />\n          </div>\n        </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(DashboardPage);\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AAGjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YAEX,mBAAmB;YACnB,MAAM,CAAC,cAAc,UAAU,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5D,CAAA,GAAA,6GAAA,CAAA,kBAAe,AAAD;gBACd,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD;gBACV,CAAA,GAAA,6GAAA,CAAA,eAAY,AAAD;aACZ;YAED,uBAAuB;YACvB,MAAM,gBAAgB,SAAS,MAAM;YACrC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,MAAM;YAClF,MAAM,iBAAiB,UAAU,MAAM;YAEvC,SAAS;gBACP,GAAG,YAAY;gBACf;gBACA;gBACA;YACF;YAEA,2CAA2C;YAC3C,MAAM,YAAY;gBAChB;oBAAE,OAAO;oBAAS,OAAO;gBAAK;gBAC9B;oBAAE,OAAO;oBAAW,OAAO;gBAAK;gBAChC;oBAAE,OAAO;oBAAY,OAAO;gBAAI;gBAChC;oBAAE,OAAO;oBAAY,OAAO;gBAAK;gBACjC;oBAAE,OAAO;oBAAU,OAAO;gBAAK;gBAC/B;oBAAE,OAAO;oBAAU,OAAO;gBAAK;gBAC/B;oBAAE,OAAO;oBAAS,OAAO;gBAAK;aAC/B;YACD,aAAa;YAEb,mCAAmC;YACnC,MAAM,mBAAmB;gBACvB;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK;oBAC1C,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;gBACR;aACD;YACD,cAAc;QAEhB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;;wDAAK;wDAAY,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;sDAElD,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhB,6LAAC;oBAAI,WAAU;;wBACd,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;sCAOzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,MAAM,aAAa,IAAI;oCAC9B,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;oCACT,OAAO;wCAAE,WAAW;wCAAM,OAAO;oCAAO;;;;;;8CAG1C,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,eAAe,MAAM,YAAY,IAAI;oCAC5C,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;oCACT,OAAO;wCAAE,WAAW;wCAAM,OAAO;oCAAM;;;;;;8CAGzC,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,MAAM,aAAa,IAAI;oCAC9B,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;;;;;;8CAGX,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,MAAM,cAAc,IAAI;oCAC/B,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;oCACT,OAAO;wCAAE,WAAW;wCAAM,OAAO;oCAAM;;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,eAAe,MAAM,cAAc,IAAI;oCAC9C,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;;;;;;8CAGX,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,MAAM,eAAe,IAAI;oCAChC,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;;;;;;8CAGX,6LAAC,uIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,MAAM,gBAAgB,IAAI;oCACjC,oBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCAGzE,OAAM;oCACN,SAAS;;;;;;;;;;;;sCAKb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0IAAA,CAAA,UAAY;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,uIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAM;4CACN,OAAM;4CACN,SAAS;;;;;;sDAIX,6LAAC,4IAAA,CAAA,UAAc;4CACb,YAAY;4CACZ,SAAS;;;;;;;;;;;;8CAKb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GAlRS;KAAA;6CAoRM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}