{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/products/add/page.js"], "sourcesContent": ["// frontend/app/dashboard/products/add/page.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { createProduct, uploadProductImage } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport Image from 'next/image';\n\nfunction AddProductPage() {\n  const [productData, setProductData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    minStock: '',\n    category: '',\n    supplier: '',\n    barcode: '',\n  });\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const router = useRouter();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setProductData((prevData) => ({\n      ...prevData,\n      [name]: value,\n    }));\n  };\n\n  const handleImageSelect = (files) => {\n    if (files && files.length > 0) {\n      const imageFiles = Array.from(files).filter(file => \n        file.type.startsWith('image/')\n      );\n      setSelectedImages(prev => [...prev, ...imageFiles]);\n    }\n  };\n\n  const removeImage = (index) => {\n    setSelectedImages(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(null);\n\n    try {\n      // إنشاء المنتج أولاً\n      const newProduct = await createProduct(productData);\n      \n      // رفع الصور إذا كانت موجودة\n      if (selectedImages.length > 0) {\n        const uploadPromises = selectedImages.map(file => \n          uploadProductImage(newProduct.id, file)\n        );\n        await Promise.all(uploadPromises);\n      }\n      \n      // الانتقال إلى صفحة المنتجات\n      router.push('/dashboard/products');\n    } catch (err) {\n      setError(err.message || 'فشل في إنشاء المنتج. يرجى المحاولة مرة أخرى.');\n      console.error('Product creation error:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.back()}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                العودة\n              </button>\n              <div className=\"h-6 w-px bg-gray-300\"></div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">إضافة منتج جديد</h1>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"hidden sm:flex items-center space-x-2 text-sm text-gray-500\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span>املأ جميع الحقول المطلوبة</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* النموذج الرئيسي */}\n          <div className=\"lg:col-span-2\">\n            <form id=\"product-form\" onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* معلومات المنتج الأساسية */}\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <h2 className=\"ml-3 text-lg font-semibold text-white\">المعلومات الأساسية</h2>\n                  </div>\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div className=\"md:col-span-2\">\n                      <label htmlFor=\"name\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        اسم المنتج *\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"text\"\n                          id=\"name\"\n                          name=\"name\"\n                          value={productData.name}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"أدخل اسم المنتج...\"\n                          required\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"barcode\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        الباركود\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"text\"\n                          id=\"barcode\"\n                          name=\"barcode\"\n                          value={productData.barcode}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"123456789...\"\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"category\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        الفئة\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"text\"\n                          id=\"category\"\n                          name=\"category\"\n                          value={productData.category}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"إلكترونيات، ملابس...\"\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"md:col-span-2\">\n                      <label htmlFor=\"description\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        الوصف\n                      </label>\n                      <textarea\n                        id=\"description\"\n                        name=\"description\"\n                        value={productData.description}\n                        onChange={handleChange}\n                        rows=\"4\"\n                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none\"\n                        placeholder=\"وصف تفصيلي للمنتج...\"\n                      />\n                    </div>\n\n                    <div className=\"md:col-span-2\">\n                      <label htmlFor=\"supplier\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        المورد\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"text\"\n                          id=\"supplier\"\n                          name=\"supplier\"\n                          value={productData.supplier}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"اسم الشركة المورّدة...\"\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* معلومات السعر والمخزون */}\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                      </svg>\n                    </div>\n                    <h2 className=\"ml-3 text-lg font-semibold text-white\">السعر والمخزون</h2>\n                  </div>\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div>\n                      <label htmlFor=\"price\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        السعر *\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"number\"\n                          step=\"0.01\"\n                          id=\"price\"\n                          name=\"price\"\n                          value={productData.price}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"0.00\"\n                          required\n                        />\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                          <span className=\"text-gray-500 text-sm font-medium\">$</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"stock\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        الكمية المتوفرة *\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"number\"\n                          id=\"stock\"\n                          name=\"stock\"\n                          value={productData.stock}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"0\"\n                          required\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"minStock\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                        الحد الأدنى للمخزون\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"number\"\n                          id=\"minStock\"\n                          name=\"minStock\"\n                          value={productData.minStock}\n                          onChange={handleChange}\n                          className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200 text-gray-900 placeholder-gray-400\"\n                          placeholder=\"0\"\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n\n          {/* الشريط الجانبي - رفع الصور */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8\">\n              <div className=\"bg-gradient-to-r from-rose-500 to-pink-600 px-6 py-4\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <h2 className=\"ml-3 text-lg font-semibold text-white\">صور المنتج</h2>\n                </div>\n              </div>\n              \n              <div className=\"p-6\">\n                {/* منطقة رفع الصور */}\n                <div className=\"mb-6\">\n                  <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                    اختر الصور\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type=\"file\"\n                      multiple\n                      accept=\"image/*\"\n                      onChange={(e) => handleImageSelect(e.target.files)}\n                      className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                    />\n                    <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-rose-400 hover:bg-rose-50 transition-colors duration-200\">\n                      <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\n                        <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                      </svg>\n                      <p className=\"text-sm text-gray-600 font-medium\">اضغط أو اسحب الصور هنا</p>\n                      <p className=\"text-xs text-gray-500 mt-1\">PNG, JPG, GIF حتى 5MB</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* معاينة الصور المختارة */}\n                {selectedImages.length > 0 && (\n                  <div className=\"space-y-4\">\n                    <h3 className=\"text-sm font-semibold text-gray-700\">الصور المختارة ({selectedImages.length})</h3>\n                    <div className=\"grid grid-cols-2 gap-3\">\n                      {selectedImages.map((file, index) => (\n                        <div key={index} className=\"relative group\">\n                          <div className=\"aspect-square relative overflow-hidden rounded-lg border-2 border-gray-200 bg-gray-100\">\n                            <Image\n                              src={URL.createObjectURL(file)}\n                              alt={file.name}\n                              fill\n                              className=\"object-cover\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={() => removeImage(index)}\n                              className=\"absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity\"\n                            >\n                              ×\n                            </button>\n                            {index === 0 && (\n                              <div className=\"absolute bottom-1 left-1 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium\">\n                                رئيسية\n                              </div>\n                            )}\n                          </div>\n                          <p className=\"mt-1 text-xs text-gray-500 truncate\">\n                            {file.name}\n                          </p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* نصائح */}\n                <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                  <h4 className=\"text-sm font-semibold text-blue-800 mb-2\">💡 نصائح</h4>\n                  <ul className=\"text-xs text-blue-700 space-y-1\">\n                    <li>• الصورة الأولى ستكون رئيسية</li>\n                    <li>• يمكن رفع عدة صور</li>\n                    <li>• أقصى حجم: 5MB لكل صورة</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* مساحة إضافية للأزرار الثابتة */}\n        <div className=\"pb-24\"></div>\n      </div>\n\n      {/* أزرار الحفظ الثابتة */}\n      <div className=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 sm:px-6 lg:px-8 z-10\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:justify-end\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              form=\"product-form\"\n              disabled={saving}\n              className=\"inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n            >\n              {saving ? (\n                <span className=\"flex items-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                  </svg>\n                  جاري الحفظ ورفع الصور...\n                </span>\n              ) : (\n                <span className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  {selectedImages.length > 0 ? 'حفظ المنتج ورفع الصور' : 'حفظ المنتج'}\n                </span>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(AddProductPage);\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAG9C;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;IACX;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,eAAe,CAAC,WAAa,CAAC;gBAC5B,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,aAAa,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,CAAA,OAC1C,KAAK,IAAI,CAAC,UAAU,CAAC;YAEvB,kBAAkB,CAAA,OAAQ;uBAAI;uBAAS;iBAAW;QACpD;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACxD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,UAAU;QACV,SAAS;QAET,IAAI;YACF,qBAAqB;YACrB,MAAM,aAAa,MAAM,CAAA,GAAA,0GAAA,CAAA,gBAAa,AAAD,EAAE;YAEvC,4BAA4B;YAC5B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAA,OACxC,CAAA,GAAA,0GAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,EAAE,EAAE;gBAEpC,MAAM,QAAQ,GAAG,CAAC;YACpB;YAEA,6BAA6B;YAC7B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;YACxB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,IAAG;oCAAe,UAAU;oCAAc,WAAU;;sDAExD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAO,WAAU;kFAAiD;;;;;;kFAGjF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,IAAI;gFACvB,UAAU;gFACV,WAAU;gFACV,aAAY;gFACZ,QAAQ;;;;;;0FAEV,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAM7E,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAU,WAAU;kFAAiD;;;;;;kFAGpF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,OAAO;gFAC1B,UAAU;gFACV,WAAU;gFACV,aAAY;;;;;;0FAEd,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAM7E,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAAiD;;;;;;kFAGrF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,QAAQ;gFAC3B,UAAU;gFACV,WAAU;gFACV,aAAY;;;;;;0FAEd,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAM7E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAc,WAAU;kFAAiD;;;;;;kFAGxF,8OAAC;wEACC,IAAG;wEACH,MAAK;wEACL,OAAO,YAAY,WAAW;wEAC9B,UAAU;wEACV,MAAK;wEACL,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAAiD;;;;;;kFAGrF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,QAAQ;gFAC3B,UAAU;gFACV,WAAU;gFACV,aAAY;;;;;;0FAEd,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAAiD;;;;;;kFAGlF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,KAAK;gFACxB,UAAU;gFACV,WAAU;gFACV,aAAY;gFACZ,QAAQ;;;;;;0FAEV,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAoC;;;;;;;;;;;;;;;;;;;;;;;0EAK1D,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAAiD;;;;;;kFAGlF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,KAAK;gFACxB,UAAU;gFACV,WAAU;gFACV,aAAY;gFACZ,QAAQ;;;;;;0FAEV,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAM7E,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAAiD;;;;;;kFAGrF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,MAAK;gFACL,IAAG;gFACH,MAAK;gFACL,OAAO,YAAY,QAAQ;gFAC3B,UAAU;gFACV,WAAU;gFACV,aAAY;;;;;;0FAEd,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYvF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;;;;;;;;;;;;sDAI1D,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAiD;;;;;;sEAGlE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,QAAQ;oEACR,QAAO;oEACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oEACjD,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAuC,QAAO;4EAAe,MAAK;4EAAO,SAAQ;sFAC9F,cAAA,8OAAC;gFAAK,GAAE;gFAAyL,aAAY;gFAAI,eAAc;gFAAQ,gBAAe;;;;;;;;;;;sFAExP,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;;;;;;;gDAM/C,eAAe,MAAM,GAAG,mBACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEAAsC;gEAAiB,eAAe,MAAM;gEAAC;;;;;;;sEAC3F,8OAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,6HAAA,CAAA,UAAK;oFACJ,KAAK,IAAI,eAAe,CAAC;oFACzB,KAAK,KAAK,IAAI;oFACd,IAAI;oFACJ,WAAU;;;;;;8FAEZ,8OAAC;oFACC,MAAK;oFACL,SAAS,IAAM,YAAY;oFAC3B,WAAU;8FACX;;;;;;gFAGA,UAAU,mBACT,8OAAC;oFAAI,WAAU;8FAAyF;;;;;;;;;;;;sFAK5G,8OAAC;4EAAE,WAAU;sFACV,KAAK,IAAI;;;;;;;mEAtBJ;;;;;;;;;;;;;;;;8DA+BlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAGR,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,uBACC,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;;;;;yDAIR,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCAEtE,eAAe,MAAM,GAAG,IAAI,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}