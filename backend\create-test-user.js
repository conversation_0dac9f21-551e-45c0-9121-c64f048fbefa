// backend/create-test-user.js
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    const hashedPassword = await bcrypt.hash('123456', 10);
    const user = await prisma.user.create({
      data: {
        name: 'مدير النظام',
        email: '<EMAIL>',
        password: hashedPassword,
      },
    });
    console.log('✅ تم إنشاء المستخدم التجريبي بنجاح');
    console.log('البريد الإلكتروني:', user.email);
    console.log('كلمة المرور: 123456');
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('ℹ️ المستخدم موجود بالفعل');
      console.log('البريد الإلكتروني: <EMAIL>');
      console.log('كلمة المرور: 123456');
    } else {
      console.error('❌ خطأ في إنشاء المستخدم:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
