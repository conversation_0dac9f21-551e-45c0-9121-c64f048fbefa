(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/settingsService.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_settingsService_f310ec4b.js",
  "static/chunks/lib_settingsService_5289ba50.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/settingsService.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/apiSettingsService.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_apiSettingsService_5fb761b6.js",
  "static/chunks/lib_apiSettingsService_5289ba50.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/apiSettingsService.js [app-client] (ecmascript)");
    });
});
}}),
}]);