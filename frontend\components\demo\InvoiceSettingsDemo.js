// frontend/components/demo/InvoiceSettingsDemo.js
'use client';

import { useState } from 'react';
import settingsService from '@/lib/settingsService';
import InvoiceRenderer from '@/components/invoice-templates/InvoiceRenderer';

export default function InvoiceSettingsDemo() {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const data = await settingsService.loadSettings();
      setSettings(data);
      console.log('Settings loaded successfully:', data);
    } catch (error) {
      console.error('Error loading settings:', error);
      alert('❌ حدث خطأ أثناء تحميل الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const saveTestCompanySettings = async () => {
    setLoading(true);
    try {
      await settingsService.saveCompanySettings({
        companyName: 'شركة الابتكار التقني المحدودة',
        companyAddress: 'الرياض، حي الملك فهد، المملكة العربية السعودية',
        companyPhone: '+966 11 555 0123',
        companyEmail: '<EMAIL>',
        companyWebsite: 'www.innovation-tech.com', // اختياري
        taxId: '300123456789003',
        currency: 'SAR'
      });

      alert('✅ تم حفظ معلومات الشركة بنجاح!');
      loadSettings();
    } catch (error) {
      console.error('Error saving company settings:', error);
      alert('❌ حدث خطأ أثناء حفظ معلومات الشركة');
    } finally {
      setLoading(false);
    }
  };

  const saveTestDisplaySettings = async () => {
    setLoading(true);
    try {
      await settingsService.saveInvoiceDisplaySettings({
        // رأس الفاتورة
        showCompanyLogo: true,
        showCompanyName: true,
        showCompanyAddress: true,
        showCompanyPhone: true,
        showCompanyEmail: true,
        showCompanyWebsite: true,
        showTaxId: true,

        // معلومات الفاتورة
        showInvoiceNumber: true,
        showInvoiceDate: true,
        showDueDate: true,
        showPaymentTerms: true,

        // معلومات العميل
        showCustomerName: true,
        showCustomerAddress: true,
        showCustomerPhone: true,
        showCustomerEmail: true,

        // عناصر الجدول
        showProductImages: true,
        showProductCode: true,
        showProductDescription: true,
        showQuantity: true,
        showUnitPrice: true,
        showDiscount: false,
        showTotalPrice: true,
        showItemNumbers: true,

        // المجاميع
        showSubtotal: true,
        showTaxAmount: true,
        showDiscountAmount: false,
        showTotalAmount: true,

        // التذييل
        showNotes: true,
        showFooter: true,
        showSignature: false,
        showQRCode: false,
        showBankDetails: false,
        showPaymentInstructions: true
      });

      alert('✅ تم حفظ إعدادات العرض بنجاح!');
      loadSettings();
    } catch (error) {
      console.error('Error saving display settings:', error);
      alert('❌ حدث خطأ أثناء حفظ إعدادات العرض');
    } finally {
      setLoading(false);
    }
  };

  const hideCompanyInfo = async () => {
    setLoading(true);
    try {
      await settingsService.saveInvoiceDisplaySettings({
        showCompanyLogo: false,
        showCompanyName: true, // نبقي الاسم فقط
        showCompanyAddress: false,
        showCompanyPhone: false,
        showCompanyEmail: false,
        showCompanyWebsite: false, // إخفاء الموقع الإلكتروني (اختياري)
        showTaxId: false,

        showInvoiceNumber: true,
        showInvoiceDate: true,
        showCustomerName: true,
        showProductDescription: true,
        showQuantity: true,
        showUnitPrice: true,
        showTotalPrice: true,
        showSubtotal: true,
        showTaxAmount: true,
        showTotalAmount: true,
        showNotes: false,
        showFooter: false
      });

      alert('✅ تم إخفاء معلومات الشركة من الفاتورة!');
      loadSettings();
    } catch (error) {
      console.error('Error hiding company info:', error);
      alert('❌ حدث خطأ أثناء إخفاء معلومات الشركة');
    } finally {
      setLoading(false);
    }
  };

  const saveCompanyWithoutWebsite = async () => {
    setLoading(true);
    try {
      await settingsService.saveCompanySettings({
        companyName: 'شركة بدون موقع إلكتروني',
        companyAddress: 'الدمام، المملكة العربية السعودية',
        companyPhone: '+966 13 555 7890',
        companyEmail: '<EMAIL>',
        companyWebsite: '', // بدون موقع إلكتروني
        taxId: '300987654321003',
        currency: 'SAR'
      });

      alert('✅ تم حفظ معلومات الشركة بدون موقع إلكتروني!');
      loadSettings();
    } catch (error) {
      console.error('Error saving company without website:', error);
      alert('❌ حدث خطأ أثناء حفظ معلومات الشركة');
    } finally {
      setLoading(false);
    }
  };

  // بيانات فاتورة تجريبية - ستُدمج مع إعدادات الشركة
  const getSampleInvoice = () => {
    if (!settings) return {};

    return {
      invoiceNumber: 'INV-2024-001',
      date: new Date().toLocaleDateString('ar-SA'),
      customerName: 'شركة العميل التجاري',
      customerAddress: 'جدة، المملكة العربية السعودية',
      customerPhone: '+966 12 555 9876',
      customerEmail: '<EMAIL>',
      items: [
        {
          id: 1,
          code: 'SOFT-001',
          description: 'تطوير نظام إدارة المخزون',
          quantity: 1,
          price: 15000.00,
          total: 15000.00
        },
        {
          id: 2,
          code: 'CONS-002',
          description: 'استشارات تقنية متخصصة',
          quantity: 20,
          price: 300.00,
          total: 6000.00
        },
        {
          id: 3,
          code: 'SUPP-003',
          description: 'دعم فني لمدة سنة',
          quantity: 1,
          price: 5000.00,
          total: 5000.00
        }
      ],
      subtotal: 26000.00,
      tax: 3900.00,
      total: 29900.00,
      notes: 'شكراً لثقتكم بنا ونتطلع للعمل معكم مستقبلاً',
      paymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',

      // دمج معلومات الشركة من الإعدادات
      companyName: settings.company?.companyName || '',
      companyAddress: settings.company?.companyAddress || '',
      companyPhone: settings.company?.companyPhone || '',
      companyEmail: settings.company?.companyEmail || '',
      companyWebsite: settings.company?.companyWebsite || '',
      taxId: settings.company?.taxId || '',
      logoUrl: settings.company?.logoUrl || settingsService.getCompanyLogo(),
      currency: settings.company?.currency || 'ر.س',

      // إعدادات العرض
      displaySettings: settings.invoice?.display || {}
    };
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mb-8">
        <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
          <h2 className="text-xl font-bold text-white">🧪 عرض توضيحي لنظام إعدادات الفواتير</h2>
          <p className="text-purple-100 text-sm mt-1">
            اختبر كيفية تأثير الإعدادات على شكل الفاتورة في الوقت الفعلي
          </p>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <button
              onClick={loadSettings}
              disabled={loading}
              className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
            >
              {loading ? '⏳ جاري التحميل...' : '📥 تحميل الإعدادات'}
            </button>

            <button
              onClick={saveTestCompanySettings}
              disabled={loading}
              className="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 transition-colors"
            >
              {loading ? '⏳ جاري الحفظ...' : '🏢 شركة مع موقع'}
            </button>

            <button
              onClick={saveCompanyWithoutWebsite}
              disabled={loading}
              className="px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:bg-gray-400 transition-colors"
            >
              {loading ? '⏳ جاري الحفظ...' : '🏢 شركة بدون موقع'}
            </button>

            <button
              onClick={saveTestDisplaySettings}
              disabled={loading}
              className="px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-400 transition-colors"
            >
              {loading ? '⏳ جاري الحفظ...' : '👁️ إظهار كل العناصر'}
            </button>

            <button
              onClick={hideCompanyInfo}
              disabled={loading}
              className="px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:bg-gray-400 transition-colors"
            >
              {loading ? '⏳ جاري الحفظ...' : '🙈 إخفاء معلومات الشركة'}
            </button>
          </div>

          {settings && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* عرض الإعدادات */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">📋 الإعدادات المحملة</h3>
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-auto">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-700 mb-2">🏢 معلومات الشركة:</h4>
                      <div className="text-sm space-y-1">
                        <p><strong>الاسم:</strong> {settings.company?.companyName || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> {settings.company?.companyAddress || 'غير محدد'}</p>
                        <p><strong>الهاتف:</strong> {settings.company?.companyPhone || 'غير محدد'}</p>
                        <p><strong>البريد:</strong> {settings.company?.companyEmail || 'غير محدد'}</p>
                        <p><strong>الموقع:</strong> {settings.company?.companyWebsite || 'غير محدد'}</p>
                        <p><strong>الرقم الضريبي:</strong> {settings.company?.taxId || 'غير محدد'}</p>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-700 mb-2">👁️ إعدادات العرض:</h4>
                      <div className="text-xs grid grid-cols-2 gap-1">
                        <span className={settings.invoice?.display?.showCompanyName ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showCompanyName ? '✅' : '❌'} اسم الشركة
                        </span>
                        <span className={settings.invoice?.display?.showCompanyAddress ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showCompanyAddress ? '✅' : '❌'} عنوان الشركة
                        </span>
                        <span className={settings.invoice?.display?.showCompanyPhone ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showCompanyPhone ? '✅' : '❌'} هاتف الشركة
                        </span>
                        <span className={settings.invoice?.display?.showCompanyEmail ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showCompanyEmail ? '✅' : '❌'} بريد الشركة
                        </span>
                        <span className={settings.invoice?.display?.showTaxId ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showTaxId ? '✅' : '❌'} الرقم الضريبي
                        </span>
                        <span className={settings.invoice?.display?.showNotes ? 'text-green-600' : 'text-red-600'}>
                          {settings.invoice?.display?.showNotes ? '✅' : '❌'} الملاحظات
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* معاينة الفاتورة */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">📄 معاينة الفاتورة</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="transform scale-50 origin-top-left" style={{ width: '200%', height: '200%' }}>
                    <InvoiceRenderer
                      invoice={getSampleInvoice()}
                      templateId="classic"
                      preview={false}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {!settings && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-gray-600">اضغط على "تحميل الإعدادات" لبدء العرض التوضيحي</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
