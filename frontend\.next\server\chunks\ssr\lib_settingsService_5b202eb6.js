module.exports = {

"[project]/lib/settingsService.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_settingsService_3c475984.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/settingsService.js [app-ssr] (ecmascript)");
    });
});
}}),

};