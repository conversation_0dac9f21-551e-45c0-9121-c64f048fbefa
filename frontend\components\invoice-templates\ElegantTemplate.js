// frontend/components/invoice-templates/ElegantTemplate.js
'use client';

import React from 'react';

export default function ElegantTemplate({ invoice, preview = false }) {
  const sampleData = {
    invoiceNumber: 'INV-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customerName: 'مؤسسة الأناقة التجارية',
    customerDetails: 'للتجارة والمقاولات',
    items: [
      { id: 1, description: 'أثاث مكتبي فاخر', quantity: 5, price: 800.00, total: 4000.00 },
      { id: 2, description: 'ديكورات داخلية', quantity: 1, price: 2500.00, total: 2500.00 },
      { id: 3, description: 'إكسسوارات مكتبية', quantity: 10, price: 150.00, total: 1500.00 }
    ],
    subtotal: 8000.00,
    tax: 1200.00,
    total: 9200.00,
    notes: 'نتطلع لخدمتكم مرة أخرى',
    companyName: 'مؤسسة الأناقة التجارية',
    companyDetails: 'للتجارة والمقاولات'
  };

  const data = preview ? sampleData : invoice;

  return (
    <div className="bg-white" style={{ minHeight: '297mm', width: '210mm' }}>
      {/* Elegant Header */}
      <div className="relative">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600"></div>

        <div className="p-8 pt-12">
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-4xl font-serif font-bold text-gray-800 mb-2">{data.companyName}</h1>
              <p className="text-gray-600 text-lg italic">{data.companyDetails}</p>

              <div className="mt-6 w-24 h-1 bg-gradient-to-r from-amber-400 to-yellow-500"></div>
            </div>

            <div className="text-right">
              <div className="border-2 border-amber-400 rounded-lg p-4 bg-amber-50">
                <h2 className="text-2xl font-serif font-bold text-gray-800 mb-1">فاتورة</h2>
                <p className="text-amber-600 font-semibold">#{data.invoiceNumber}</p>
              </div>
            </div>
          </div>

          {/* Elegant Customer Section */}
          <div className="border border-gray-200 rounded-lg p-6 mb-8 bg-gray-50">
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2">
                  فواتير إلى
                </h3>
                <div className="space-y-2">
                  <p className="text-gray-800 font-semibold text-lg">{data.customerName}</p>
                  <p className="text-gray-600">{data.customerDetails}</p>
                </div>
              </div>

              <div className="text-right">
                <h3 className="text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2">
                  تفاصيل الفاتورة
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">تاريخ الإصدار:</span>
                    <span className="font-semibold">{data.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">رقم المرجع:</span>
                    <span className="font-semibold">{data.invoiceNumber}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Elegant Items Table */}
          <div className="mb-8">
            <h3 className="text-xl font-serif font-semibold text-gray-800 mb-4 border-b-2 border-amber-400 pb-2">
              بنود الفاتورة
            </h3>

            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-gray-100 to-amber-50">
                  <tr>
                    <th className="p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200">المبلغ</th>
                    <th className="p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200">سعر الوحدة</th>
                    <th className="p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200">الكمية</th>
                    <th className="p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200">الوصف</th>
                    <th className="p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200">م</th>
                  </tr>
                </thead>
                <tbody>
                  {data.items.map((item, index) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-amber-25">
                      <td className="p-4 text-right font-semibold text-amber-600">
                        {(Number(item.total) || 0).toFixed(2)} ر.س
                      </td>
                      <td className="p-4 text-right text-gray-700">{(Number(item.price) || 0).toFixed(2)} ر.س</td>
                      <td className="p-4 text-right">
                        <span className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium">
                          {item.quantity || 0}
                        </span>
                      </td>
                      <td className="p-4 text-right font-medium text-gray-800">{item.description || ''}</td>
                      <td className="p-4 text-right">
                        <span className="w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Elegant Summary */}
          <div className="flex justify-between items-start mb-8">
            <div className="w-1/2 pr-8">
              <div className="border-l-4 border-amber-400 pl-6">
                <h4 className="text-lg font-serif font-semibold text-gray-800 mb-3">ملاحظات خاصة</h4>
                <p className="text-gray-700 italic mb-4">{data.notes}</p>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <h5 className="font-semibold text-gray-800 mb-2">شروط وأحكام:</h5>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• الدفع خلال 15 يوم من تاريخ الفاتورة</li>
                    <li>• ضمان جودة لمدة سنة كاملة</li>
                    <li>• خدمة ما بعد البيع متاحة</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="w-80">
              <div className="border-2 border-amber-300 rounded-lg bg-gradient-to-br from-amber-50 to-yellow-50">
                <div className="bg-gradient-to-r from-amber-400 to-yellow-500 text-white p-4 rounded-t-lg">
                  <h4 className="text-lg font-serif font-semibold">الملخص المالي</h4>
                </div>

                <div className="p-6 space-y-4">
                  <div className="flex justify-between text-gray-700">
                    <span>المجموع الفرعي:</span>
                    <span className="font-semibold">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>
                  </div>
                  <div className="flex justify-between text-gray-700">
                    <span>ضريبة القيمة المضافة:</span>
                    <span className="font-semibold">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>
                  </div>
                  <div className="border-t-2 border-amber-300 pt-4">
                    <div className="flex justify-between text-xl font-bold text-gray-800">
                      <span className="font-serif">الإجمالي النهائي:</span>
                      <span className="text-amber-600">{(Number(data.total) || 0).toFixed(2)} ر.س</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Elegant Footer */}
          <div className="border-t-2 border-amber-300 pt-6">
            <div className="flex justify-between items-end">
              <div>
                <h4 className="font-serif font-semibold text-gray-800 mb-3">معلومات التواصل</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>📱 الهاتف: +966 50 123 4567</p>
                  <p>📧 البريد: <EMAIL></p>
                  <p>🌐 الموقع: www.elegant-business.com</p>
                  <p>📍 العنوان: الرياض، المملكة العربية السعودية</p>
                </div>
              </div>

              <div className="text-center">
                <h5 className="font-serif font-semibold text-gray-800 mb-4">توقيع مخول</h5>
                <div className="w-32 h-16 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center">
                  <span className="text-amber-500 text-sm">التوقيع</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
