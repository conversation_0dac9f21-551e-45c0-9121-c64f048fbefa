// frontend/components/invoice-templates/EnhancedClassicTemplate.js
'use client';

import React from 'react';

export default function EnhancedClassicTemplate({ invoice, preview = false, displaySettings = {} }) {
  // بيانات العينة للمعاينة فقط
  const sampleData = preview ? {
    invoiceNumber: 'INV-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customerName: 'شركة التقنية المتطورة',
    customerDetails: 'الحلول التقنية المتكاملة',
    customerAddress: 'الرياض، المملكة العربية السعودية',
    customerPhone: '+966 50 987 6543',
    customerEmail: '<EMAIL>',
    items: [
      { id: 1, code: 'PRD001', description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },
      { id: 2, code: 'PRD002', description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },
      { id: 3, code: 'PRD003', description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }
    ],
    subtotal: 8500.00,
    tax: 1275.00,
    total: 9775.00,
    notes: 'شكراً لثقتكم بنا',
    companyName: 'شركة التقنية المتطورة',
    companyDetails: 'الحلول التقنية المتكاملة',
    companyAddress: 'الرياض، المملكة العربية السعودية',
    companyPhone: '+966 50 123 4567',
    companyEmail: '<EMAIL>',
    companyWebsite: 'www.company.com',
    taxId: '*********',
    paymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    invoiceFooter: 'شكراً لتعاملكم معنا'
  } : invoice;

  const data = sampleData;

  return (
    <div className="bg-white p-8 shadow-lg" style={{ minHeight: '297mm', width: '210mm' }}>
      {/* Header */}
      <div className="relative mb-8">
        <div className="bg-teal-600 h-16 w-full absolute top-0 left-0"></div>
        <div className="bg-teal-600 h-8 w-3/4 absolute top-16 left-0"></div>

        <div className="relative z-10 pt-6 pb-4">
          <div className="flex justify-between items-start">
            <div className="text-white">
              {displaySettings.showCompanyName !== false && (
                <h1 className="text-2xl font-bold mb-2">{data.companyName}</h1>
              )}
              {displaySettings.showCompanyAddress !== false && data.companyAddress && (
                <p className="text-teal-100 text-sm">{data.companyAddress}</p>
              )}
              {displaySettings.showCompanyPhone !== false && data.companyPhone && (
                <p className="text-teal-100 text-sm">📞 {data.companyPhone}</p>
              )}
              {displaySettings.showCompanyEmail !== false && data.companyEmail && (
                <p className="text-teal-100 text-sm">📧 {data.companyEmail}</p>
              )}
              {displaySettings.showCompanyWebsite !== false && data.companyWebsite && (
                <p className="text-teal-100 text-sm">🌐 {data.companyWebsite}</p>
              )}
              {displaySettings.showTaxId !== false && data.taxId && (
                <p className="text-teal-100 text-sm">الرقم الضريبي: {data.taxId}</p>
              )}
            </div>

            {/* Logo Area */}
            {displaySettings.showCompanyLogo !== false && (
              <div className="bg-white p-4 rounded-lg shadow-md">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                  <div className="text-white font-bold text-xl">شعار</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Invoice Title and Info */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-teal-700 mb-4">فاتورة مبيعات</h2>

        <div className="grid grid-cols-3 gap-4 text-sm">
          {displaySettings.showInvoiceNumber !== false && (
            <div className="text-right">
              <span className="font-semibold">رقم الفاتورة: {data.invoiceNumber}</span>
            </div>
          )}
          {displaySettings.showCustomerName !== false && (
            <div className="text-right">
              <span className="font-semibold">اسم العميل: {data.customerName}</span>
            </div>
          )}
          {displaySettings.showInvoiceDate !== false && (
            <div className="text-right">
              <span className="font-semibold">التاريخ: {data.date}</span>
            </div>
          )}
        </div>
      </div>

      {/* Customer Information */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-3">معلومات العميل:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          {displaySettings.showCustomerName !== false && (
            <div><span className="font-semibold">الاسم:</span> {data.customerName}</div>
          )}
          {displaySettings.showCustomerAddress !== false && data.customerAddress && (
            <div><span className="font-semibold">العنوان:</span> {data.customerAddress}</div>
          )}
          {displaySettings.showCustomerPhone !== false && data.customerPhone && (
            <div><span className="font-semibold">الهاتف:</span> {data.customerPhone}</div>
          )}
          {displaySettings.showCustomerEmail !== false && data.customerEmail && (
            <div><span className="font-semibold">البريد:</span> {data.customerEmail}</div>
          )}
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-6">
        <table className="w-full border-collapse border border-gray-300">
          <thead className="bg-teal-600 text-white">
            <tr>
              {displaySettings.showTotalPrice !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">الإجمالي</th>
              )}
              {displaySettings.showUnitPrice !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">السعر</th>
              )}
              {displaySettings.showQuantity !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">الكمية</th>
              )}
              {displaySettings.showProductDescription !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">الوصف</th>
              )}
              {displaySettings.showProductCode !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">الكود</th>
              )}
              {displaySettings.showItemNumbers !== false && (
                <th className="border border-gray-300 p-3 text-right font-semibold">م</th>
              )}
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id}>
                {displaySettings.showTotalPrice !== false && (
                  <td className="border border-gray-300 p-3 text-right">{(Number(item.total) || 0).toFixed(2)}</td>
                )}
                {displaySettings.showUnitPrice !== false && (
                  <td className="border border-gray-300 p-3 text-right">{(Number(item.price) || 0).toFixed(2)}</td>
                )}
                {displaySettings.showQuantity !== false && (
                  <td className="border border-gray-300 p-3 text-right">{item.quantity || 0}</td>
                )}
                {displaySettings.showProductDescription !== false && (
                  <td className="border border-gray-300 p-3 text-right">{item.description || ''}</td>
                )}
                {displaySettings.showProductCode !== false && (
                  <td className="border border-gray-300 p-3 text-right">{item.code || ''}</td>
                )}
                {displaySettings.showItemNumbers !== false && (
                  <td className="border border-gray-300 p-3 text-right">{index + 1}</td>
                )}
              </tr>
            ))}

            {/* Empty rows for spacing */}
            {[...Array(3)].map((_, i) => (
              <tr key={`empty-${i}`}>
                {displaySettings.showTotalPrice !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
                {displaySettings.showUnitPrice !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
                {displaySettings.showQuantity !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
                {displaySettings.showProductDescription !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
                {displaySettings.showProductCode !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
                {displaySettings.showItemNumbers !== false && <td className="border border-gray-300 p-3">&nbsp;</td>}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary and Notes */}
      <div className="flex justify-between items-start mb-6">
        <div className="w-1/2 pr-4">
          {displaySettings.showNotes !== false && data.notes && (
            <div className="mb-4">
              <h4 className="font-semibold text-gray-800 mb-2">ملاحظات:</h4>
              <p className="text-gray-700">{data.notes}</p>
            </div>
          )}
          
          {displaySettings.showPaymentTerms !== false && data.paymentTerms && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">شروط الدفع:</h4>
              <p className="text-gray-700 text-sm">{data.paymentTerms}</p>
            </div>
          )}
        </div>
        
        <div className="w-64">
          <div className="bg-yellow-100 p-4 rounded-lg">
            {displaySettings.showSubtotal !== false && (
              <div className="flex justify-between mb-2">
                <span className="font-semibold">المجموع الفرعي:</span>
                <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>
              </div>
            )}
            {displaySettings.showTaxAmount !== false && (
              <div className="flex justify-between mb-2">
                <span className="font-semibold">الضريبة:</span>
                <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>
              </div>
            )}
            {displaySettings.showTotalAmount !== false && (
              <div className="border-t border-gray-300 pt-2">
                <div className="flex justify-between font-bold text-lg">
                  <span>الإجمالي:</span>
                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      {displaySettings.showFooter !== false && data.invoiceFooter && (
        <div className="text-center border-t border-gray-300 pt-4">
          <p className="text-gray-600">{data.invoiceFooter}</p>
        </div>
      )}

      {/* Signature Area */}
      {displaySettings.showSignature === true && (
        <div className="mt-8 flex justify-end">
          <div className="text-center">
            <div className="w-32 h-16 border-b border-gray-400 mb-2"></div>
            <p className="text-sm text-gray-600">التوقيع</p>
          </div>
        </div>
      )}
    </div>
  );
}
