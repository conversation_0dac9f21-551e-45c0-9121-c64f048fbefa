// frontend/components/invoice-templates/TemplatePreview.js
'use client';

import React, { useState } from 'react';
import ClassicTemplate from './ClassicTemplate';
import ModernTemplate from './ModernTemplate';
import ElegantTemplate from './ElegantTemplate';
import MinimalTemplate from './MinimalTemplate';

const templates = {
  classic: {
    id: 'classic',
    name: 'القالب الكلاسيكي',
    description: 'تصميم تقليدي أنيق مع ألوان الأخضر والذهبي',
    component: ClassicTemplate,
    preview: '/images/templates/classic-preview.jpg',
    features: ['تصميم تقليدي', 'ألوان هادئة', 'مناسب للشركات التقليدية']
  },
  modern: {
    id: 'modern',
    name: 'القالب الحديث',
    description: 'تصميم عصري مع تدرجات لونية جذابة',
    component: ModernTemplate,
    preview: '/images/templates/modern-preview.jpg',
    features: ['تصميم عصري', 'تدرجات لونية', 'مناسب للشركات التقنية']
  },
  elegant: {
    id: 'elegant',
    name: 'القالب الأنيق',
    description: 'تصميم راقي مع لمسات ذهبية فاخرة',
    component: ElegantTemplate,
    preview: '/images/templates/elegant-preview.jpg',
    features: ['تصميم راقي', 'لمسات ذهبية', 'مناسب للشركات الفاخرة']
  },
  minimal: {
    id: 'minimal',
    name: 'القالب البسيط',
    description: 'تصميم بسيط ونظيف للاستخدام العام',
    component: MinimalTemplate,
    preview: '/images/templates/minimal-preview.jpg',
    features: ['تصميم بسيط', 'سهل القراءة', 'مناسب لجميع الأعمال']
  }
};

export default function TemplatePreview({ onSelectTemplate, selectedTemplate }) {
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [scale, setScale] = useState(0.3);

  const handlePreview = (templateId) => {
    setPreviewTemplate(templateId);
  };

  const closePreview = () => {
    setPreviewTemplate(null);
  };

  const handleSelect = (templateId) => {
    onSelectTemplate(templateId);
    closePreview();
  };

  return (
    <div>
      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {Object.values(templates).map((template) => (
          <div
            key={template.id}
            className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:scale-105 ${
              selectedTemplate === template.id
                ? 'border-blue-500 ring-4 ring-blue-100'
                : 'border-gray-200 hover:border-blue-300'
            }`}
          >
            {/* Template Preview Image */}
            <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="transform scale-[0.15] origin-top-left">
                  <template.component preview={true} />
                </div>
              </div>
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                <button
                  onClick={() => handlePreview(template.id)}
                  className="bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-105"
                >
                  معاينة كاملة
                </button>
              </div>
            </div>

            {/* Template Info */}
            <div className="p-4">
              <h3 className="text-lg font-bold text-gray-800 mb-2">{template.name}</h3>
              <p className="text-gray-600 text-sm mb-3">{template.description}</p>
              
              {/* Features */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {template.features.map((feature, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => handlePreview(template.id)}
                  className="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                >
                  معاينة
                </button>
                <button
                  onClick={() => handleSelect(template.id)}
                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                    selectedTemplate === template.id
                      ? 'bg-green-500 text-white'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {selectedTemplate === template.id ? 'محدد' : 'اختيار'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Full Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-bold text-gray-800">
                  معاينة {templates[previewTemplate].name}
                </h3>
                <p className="text-gray-600 text-sm">
                  {templates[previewTemplate].description}
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Zoom Controls */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setScale(Math.max(0.1, scale - 0.1))}
                    className="bg-gray-100 hover:bg-gray-200 p-2 rounded-lg"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4" />
                    </svg>
                  </button>
                  <span className="text-sm font-medium min-w-[60px] text-center">
                    {Math.round(scale * 100)}%
                  </span>
                  <button
                    onClick={() => setScale(Math.min(1, scale + 0.1))}
                    className="bg-gray-100 hover:bg-gray-200 p-2 rounded-lg"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                </div>
                
                <button
                  onClick={closePreview}
                  className="bg-gray-100 hover:bg-gray-200 p-2 rounded-lg"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Preview Content */}
            <div className="flex-1 overflow-auto p-4 bg-gray-100">
              <div className="flex justify-center">
                <div 
                  className="bg-white shadow-lg"
                  style={{ 
                    transform: `scale(${scale})`,
                    transformOrigin: 'top center'
                  }}
                >
                  {React.createElement(templates[previewTemplate].component, { preview: true })}
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  {templates[previewTemplate].features.map((feature, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={closePreview}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    إغلاق
                  </button>
                  <button
                    onClick={() => handleSelect(previewTemplate)}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                  >
                    اختيار هذا القالب
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export { templates };
