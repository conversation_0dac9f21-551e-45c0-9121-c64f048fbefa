// frontend/app/dashboard/invoices/create/page.js
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { createInvoice, getCustomers, getProducts } from '@/lib/api';
import withAuth from '@/components/withAuth';

function CreateInvoicePage() {
  const [invoiceData, setInvoiceData] = useState({
    customerId: '',
    items: [],
    taxRate: 0,
    discountRate: 0,
    notes: '',
    terms: '',
    dueDate: '',
    showImages: true
  });
  
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [productQuantity, setProductQuantity] = useState(1);
  const [productPrice, setProductPrice] = useState('');
  const [productDiscount, setProductDiscount] = useState(0);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loadingData, setLoadingData] = useState(true);
  
  const router = useRouter();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      const [customersData, productsData] = await Promise.all([
        getCustomers(),
        getProducts()
      ]);
      setCustomers(customersData);
      setProducts(productsData);
    } catch (err) {
      setError('Failed to load data. Please refresh the page.');
      console.error(err);
    } finally {
      setLoadingData(false);
    }
  };

  const handleInputChange = (field, value) => {
    setInvoiceData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addProductToInvoice = () => {
    if (!selectedProduct || productQuantity <= 0) {
      setError('Please select a product and enter a valid quantity.');
      return;
    }

    const product = products.find(p => p.id === parseInt(selectedProduct));
    if (!product) {
      setError('Selected product not found.');
      return;
    }

    if (product.stock < productQuantity) {
      setError(`Insufficient stock. Available: ${product.stock}`);
      return;
    }

    // التحقق من عدم إضافة نفس المنتج مرتين
    const existingItem = invoiceData.items.find(item => item.productId === product.id);
    if (existingItem) {
      setError('This product is already added to the invoice.');
      return;
    }

    const unitPrice = productPrice || product.price;
    const discount = productDiscount || 0;
    const total = (parseFloat(unitPrice) * productQuantity) * (1 - discount / 100);

    const newItem = {
      productId: product.id,
      product: product,
      quantity: productQuantity,
      unitPrice: parseFloat(unitPrice),
      discount: discount,
      total: total
    };

    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));

    // إعادة تعيين النموذج
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice('');
    setProductDiscount(0);
    setError(null);
  };

  const removeProductFromInvoice = (index) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItemQuantity = (index, quantity) => {
    if (quantity <= 0) return;

    const item = invoiceData.items[index];
    const product = products.find(p => p.id === item.productId);
    
    if (product && product.stock < quantity) {
      setError(`Insufficient stock for ${product.name}. Available: ${product.stock}`);
      return;
    }

    const newTotal = (item.unitPrice * quantity) * (1 - item.discount / 100);

    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index 
          ? { ...item, quantity, total: newTotal }
          : item
      )
    }));
    setError(null);
  };

  const calculateTotals = () => {
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = subtotal * (invoiceData.discountRate / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const taxAmount = subtotalAfterDiscount * (invoiceData.taxRate / 100);
    const total = subtotalAfterDiscount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!invoiceData.customerId) {
      setError('Please select a customer.');
      return;
    }

    if (invoiceData.items.length === 0) {
      setError('Please add at least one product to the invoice.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const invoicePayload = {
        customerId: parseInt(invoiceData.customerId),
        items: invoiceData.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount
        })),
        taxRate: invoiceData.taxRate,
        discountRate: invoiceData.discountRate,
        notes: invoiceData.notes,
        terms: invoiceData.terms,
        dueDate: invoiceData.dueDate || null,
        showImages: invoiceData.showImages
      };

      const result = await createInvoice(invoicePayload);
      router.push(`/dashboard/invoices/${result.invoice.id}`);
    } catch (err) {
      setError(err.message || 'Failed to create invoice. Please try again.');
      console.error('Invoice creation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  if (loadingData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                العودة
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">إنشاء فاتورة جديدة</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* معلومات الفاتورة الأساسية */}
            <div className="lg:col-span-2 space-y-6">
              {/* معلومات العميل */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4">
                  <h2 className="text-lg font-semibold text-white">معلومات العميل</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        العميل *
                      </label>
                      <select
                        value={invoiceData.customerId}
                        onChange={(e) => handleInputChange('customerId', e.target.value)}
                        className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                        required
                      >
                        <option value="">اختر العميل...</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name} - {customer.email}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        تاريخ الاستحقاق
                      </label>
                      <input
                        type="date"
                        value={invoiceData.dueDate}
                        onChange={(e) => handleInputChange('dueDate', e.target.value)}
                        className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                      />
                    </div>

                    <div>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={invoiceData.showImages}
                          onChange={(e) => handleInputChange('showImages', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">إظهار صور المنتجات في الفاتورة</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* إضافة المنتجات */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
                  <h2 className="text-lg font-semibold text-white">إضافة المنتجات</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">المنتج</label>
                      <select
                        value={selectedProduct}
                        onChange={(e) => {
                          setSelectedProduct(e.target.value);
                          const product = products.find(p => p.id === parseInt(e.target.value));
                          if (product) {
                            setProductPrice(product.price);
                          }
                        }}
                        className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      >
                        <option value="">اختر المنتج...</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name} - المخزون: {product.stock}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">الكمية</label>
                      <input
                        type="number"
                        min="1"
                        value={productQuantity}
                        onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                        className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">السعر</label>
                      <input
                        type="number"
                        step="0.01"
                        value={productPrice}
                        onChange={(e) => setProductPrice(e.target.value)}
                        className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">خصم %</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={productDiscount}
                        onChange={(e) => setProductDiscount(parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      />
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={addProductToInvoice}
                    className="w-full md:w-auto px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                  >
                    <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    إضافة المنتج
                  </button>
                </div>
              </div>

              {/* قائمة المنتجات المضافة */}
              {invoiceData.items.length > 0 && (
                <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-600 px-6 py-4">
                    <h2 className="text-lg font-semibold text-white">المنتجات المضافة</h2>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">خصم</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {invoiceData.items.map((item, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                {item.product.images && item.product.images.length > 0 && (
                                  <div className="flex-shrink-0 h-10 w-10 mr-3">
                                    <Image
                                      src={`http://localhost:5000/${item.product.images[0].path}`}
                                      alt={item.product.name}
                                      width={40}
                                      height={40}
                                      className="h-10 w-10 rounded-lg object-cover"
                                    />
                                  </div>
                                )}
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{item.product.name}</div>
                                  <div className="text-sm text-gray-500">{item.product.category}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => updateItemQuantity(index, parseInt(e.target.value) || 1)}
                                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ${item.unitPrice.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.discount}%
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              ${item.total.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <button
                                type="button"
                                onClick={() => removeProductFromInvoice(index)}
                                className="text-red-600 hover:text-red-900"
                              >
                                حذف
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>

            {/* الشريط الجانبي - الملخص والإعدادات */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                {/* ملخص الفاتورة */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8">
                  <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                    <h2 className="text-lg font-semibold text-white">ملخص الفاتورة</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">المجموع الفرعي:</span>
                      <span className="font-medium">${totals.subtotal.toFixed(2)}</span>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">خصم عام (%)</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={invoiceData.discountRate}
                        onChange={(e) => handleInputChange('discountRate', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500"
                      />
                    </div>
                    
                    {invoiceData.discountRate > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>خصم:</span>
                        <span>-${totals.discountAmount.toFixed(2)}</span>
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">ضريبة (%)</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={invoiceData.taxRate}
                        onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500"
                      />
                    </div>
                    
                    {invoiceData.taxRate > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">ضريبة:</span>
                        <span className="font-medium">${totals.taxAmount.toFixed(2)}</span>
                      </div>
                    )}
                    
                    <hr />
                    <div className="flex justify-between text-lg font-bold">
                      <span>المجموع الكلي:</span>
                      <span className="text-indigo-600">${totals.total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* ملاحظات وشروط */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4">
                    <h2 className="text-lg font-semibold text-white">ملاحظات وشروط</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                      <textarea
                        value={invoiceData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 resize-none"
                        placeholder="ملاحظات إضافية..."
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">شروط الدفع</label>
                      <textarea
                        value={invoiceData.terms}
                        onChange={(e) => handleInputChange('terms', e.target.value)}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 resize-none"
                        placeholder="شروط وأحكام الدفع..."
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* أزرار الحفظ */}
          <div className="flex flex-col sm:flex-row gap-4 sm:justify-end">
            <button
              type="button"
              onClick={() => router.back()}
              className="inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading || invoiceData.items.length === 0}
              className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z"></path>
                  </svg>
                  جاري إنشاء الفاتورة...
                </span>
              ) : (
                <span className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  إنشاء الفاتورة
                </span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default withAuth(CreateInvoicePage);
