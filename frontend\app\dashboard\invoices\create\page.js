'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import withAuth from '@/components/withAuth';
import DashboardLayout from '@/components/DashboardLayout';
import TemplateSelector from '@/components/invoice-templates/TemplateSelector';
import InvoiceRenderer from '@/components/invoice-templates/InvoiceRenderer';
import { templates } from '@/components/invoice-templates/TemplatePreview';
import { getCustomers, getProducts } from '@/lib/api';

function CreateInvoicePage() {
  const [step, setStep] = useState(1); // 1: Details, 2: Preview
  const [selectedTemplate, setSelectedTemplate] = useState('classic');
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [invoiceData, setInvoiceData] = useState({
    customerId: '',
    customerName: '',
    customerDetails: '',
    items: [],
    notes: '',
    dueDate: '',
    subtotal: 0,
    tax: 0,
    total: 0
  });

  const router = useRouter();

  useEffect(() => {
    loadData();
    // Load saved template from localStorage
    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');
    if (savedTemplate) {
      setSelectedTemplate(savedTemplate);
    }
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [customersData, productsData] = await Promise.all([
        getCustomers(),
        getProducts()
      ]);

      // التأكد من أن البيانات في الشكل الصحيح
      setCustomers(Array.isArray(customersData) ? customersData : customersData?.customers || []);
      setProducts(Array.isArray(productsData) ? productsData : productsData?.products || []);
    } catch (err) {
      setError('فشل في تحميل البيانات');
      console.error('Error loading data:', err);
      // تعيين arrays فارغة في حالة الخطأ
      setCustomers([]);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerChange = (customerId) => {
    const customer = Array.isArray(customers) ? customers.find(c => c.id === parseInt(customerId)) : null;
    setInvoiceData(prev => ({
      ...prev,
      customerId,
      customerName: customer?.name || '',
      customerDetails: customer?.companyName || customer?.email || ''
    }));
  };

  const addItem = () => {
    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, {
        id: Date.now(),
        productId: '',
        description: '',
        quantity: 1,
        price: 0,
        total: 0
      }]
    }));
  };

  const updateItem = (itemId, field, value) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };

          // Auto-fill from product if productId is selected
          if (field === 'productId' && value) {
            const product = Array.isArray(products) ? products.find(p => p.id === parseInt(value)) : null;
            if (product) {
              updatedItem.description = product.name;
              updatedItem.price = Number(product.price) || 0;
            }
          }

          // Ensure numeric values
          if (field === 'quantity') {
            updatedItem.quantity = Number(value) || 0;
          }
          if (field === 'price') {
            updatedItem.price = Number(value) || 0;
          }

          // Calculate total
          updatedItem.total = Number(updatedItem.quantity || 0) * Number(updatedItem.price || 0);

          return updatedItem;
        }
        return item;
      })
    }));
  };

  const removeItem = (itemId) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const calculateTotals = () => {
    const subtotal = invoiceData.items.reduce((sum, item) => sum + (Number(item.total) || 0), 0);
    const tax = subtotal * 0.15; // 15% VAT
    const total = subtotal + tax;

    setInvoiceData(prev => ({
      ...prev,
      subtotal: Number(subtotal) || 0,
      tax: Number(tax) || 0,
      total: Number(total) || 0
    }));
  };

  useEffect(() => {
    calculateTotals();
  }, [invoiceData.items]);

  const nextStep = () => {
    if (step < 2) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      // Here you would call the API to create the invoice
      // await createInvoice({ ...invoiceData, templateId: selectedTemplate });

      // For now, just simulate success
      setTimeout(() => {
        router.push('/dashboard/invoices');
      }, 1000);
    } catch (err) {
      setError('فشل في إنشاء الفاتورة');
      console.error('Error creating invoice:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">تفاصيل الفاتورة</h2>
              <p className="text-gray-600">أدخل بيانات الفاتورة والعناصر</p>
            </div>

            {/* Customer Selection */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اختر العميل
                  </label>
                  <select
                    value={invoiceData.customerId}
                    onChange={(e) => handleCustomerChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={loading}
                  >
                    <option value="">
                      {loading ? 'جاري التحميل...' : 'اختر عميل...'}
                    </option>
                    {Array.isArray(customers) && customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                  {!loading && (!Array.isArray(customers) || customers.length === 0) && (
                    <p className="text-sm text-red-600 mt-1">
                      لا توجد عملاء متاحون. يرجى إضافة عملاء أولاً.
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ الاستحقاق
                  </label>
                  <input
                    type="date"
                    value={invoiceData.dueDate}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Items */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">عناصر الفاتورة</h3>
                <button
                  onClick={addItem}
                  className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                >
                  إضافة عنصر
                </button>
              </div>

              <div className="space-y-4">
                {invoiceData.items.map((item, index) => (
                  <div key={item.id} className="grid grid-cols-12 gap-4 items-end">
                    <div className="col-span-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        المنتج
                      </label>
                      <select
                        value={item.productId}
                        onChange={(e) => updateItem(item.id, 'productId', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">اختر منتج...</option>
                        {Array.isArray(products) && products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        الكمية
                      </label>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="1"
                      />
                    </div>

                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        السعر
                      </label>
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        step="0.01"
                      />
                    </div>

                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        الإجمالي
                      </label>
                      <input
                        type="text"
                        value={(Number(item.total) || 0).toFixed(2)}
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                      />
                    </div>

                    <div className="col-span-2">
                      <button
                        onClick={() => removeItem(item.id)}
                        className="w-full bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {invoiceData.items.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد عناصر. اضغط "إضافة عنصر" لبدء إضافة المنتجات.
                </div>
              )}
            </div>

            {/* Notes */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">ملاحظات</h3>
              <textarea
                value={invoiceData.notes}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="ملاحظات إضافية..."
              />
            </div>

            {/* Totals Summary */}
            {invoiceData.items.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص المبالغ</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span>{(Number(invoiceData.subtotal) || 0).toFixed(2)} ر.س</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{(Number(invoiceData.tax) || 0).toFixed(2)} ر.س</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>الإجمالي:</span>
                    <span>{(Number(invoiceData.total) || 0).toFixed(2)} ر.س</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">معاينة الفاتورة</h2>
              <p className="text-gray-600">راجع الفاتورة قبل الحفظ</p>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="transform scale-75 origin-top">
                <InvoiceRenderer
                  invoice={{
                    invoiceNumber: 'INV-' + Date.now(),
                    date: new Date().toLocaleDateString('ar-SA'),
                    customerName: invoiceData.customerName,
                    customerDetails: invoiceData.customerDetails,
                    items: invoiceData.items,
                    subtotal: invoiceData.subtotal,
                    tax: invoiceData.tax,
                    total: invoiceData.total,
                    notes: invoiceData.notes || 'شكراً لكم...',
                    companyName: 'شركتك',
                    companyDetails: 'تفاصيل شركتك'
                  }}
                  templateId={selectedTemplate}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <nav className="flex" aria-label="Breadcrumb">
                  <ol className="flex items-center space-x-4">
                    <li>
                      <Link href="/dashboard" className="text-gray-400 hover:text-gray-500">
                        <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                      </Link>
                    </li>
                    <li>
                      <div className="flex items-center">
                        <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                        <Link href="/dashboard/invoices" className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                          الفواتير
                        </Link>
                      </div>
                    </li>
                    <li>
                      <div className="flex items-center">
                        <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="ml-4 text-sm font-medium text-gray-900">إنشاء فاتورة جديدة</span>
                      </div>
                    </li>
                  </ol>
                </nav>
                <h1 className="mt-2 text-3xl font-bold text-gray-900">إنشاء فاتورة جديدة</h1>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Current Template Info */}
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
                <span className="text-blue-800 font-medium">
                  القالب المحدد: <span className="font-bold">{templates[selectedTemplate]?.name || 'القالب الكلاسيكي'}</span>
                </span>
              </div>
              <Link
                href="/dashboard/invoice-templates"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium underline"
              >
                تغيير القالب
              </Link>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-center">
              {[1, 2].map((stepNumber) => (
                <div key={stepNumber} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                    step >= stepNumber
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {stepNumber}
                  </div>
                  <div className={`ml-4 ${step >= stepNumber ? 'text-blue-600' : 'text-gray-500'}`}>
                    {stepNumber === 1 && 'تفاصيل الفاتورة'}
                    {stepNumber === 2 && 'المعاينة'}
                  </div>
                  {stepNumber < 2 && (
                    <div className={`w-16 h-1 mx-4 ${
                      step > stepNumber ? 'bg-blue-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Step Content */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <div>
              {step > 1 && (
                <button
                  onClick={prevStep}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  السابق
                </button>
              )}
            </div>

            <div className="flex gap-4">
              <Link
                href="/dashboard/invoices"
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </Link>

              {step < 2 ? (
                <button
                  onClick={nextStep}
                  disabled={step === 1 && (invoiceData.items.length === 0 || !invoiceData.customerId)}
                  className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {loading ? 'جاري الحفظ...' : 'حفظ الفاتورة'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(CreateInvoicePage);
