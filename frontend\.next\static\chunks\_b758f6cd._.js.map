{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  return apiRequest(`${API_BASE_URL}/customers`, {\n    method: 'POST',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function getCustomerById(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, { method: 'GET' }, true);\n}\n\nexport async function updateCustomer(id, customerData) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function deleteCustomer(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'DELETE',\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QAAE,QAAQ;IAAM,GAAG;AAC1E;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العرض التوضيحي',\n      href: '/dashboard/demo',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,6LAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,6LAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAtQwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/InputField.js"], "sourcesContent": ["// frontend/components/auth/InputField.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function InputField({ \n  label, \n  type = 'text', \n  name, \n  value, \n  onChange, \n  placeholder, \n  required = false,\n  icon,\n  showPasswordToggle = false,\n  error = null\n}) {\n  const [showPassword, setShowPassword] = useState(false);\n\n  const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type;\n\n  return (\n    <div>\n      <label htmlFor={name} className=\"block text-sm font-semibold text-gray-700 mb-2\">\n        {label}\n        {required && <span className=\"text-red-500 mr-1\">*</span>}\n      </label>\n      <div className=\"relative\">\n        <input\n          type={inputType}\n          id={name}\n          name={name}\n          value={value}\n          onChange={onChange}\n          className={`w-full px-4 py-3 ${icon ? 'pl-12' : ''} ${showPasswordToggle ? 'pr-12' : ''} border-2 rounded-xl focus:outline-none transition-all duration-200 text-gray-900 placeholder-gray-400 ${\n            error \n              ? 'border-red-300 focus:border-red-500 focus:ring-4 focus:ring-red-100' \n              : 'border-gray-200 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100'\n          }`}\n          placeholder={placeholder}\n          required={required}\n        />\n        \n        {/* Icon */}\n        {icon && (\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <span className=\"h-5 w-5 text-gray-400\">\n              {icon}\n            </span>\n          </div>\n        )}\n\n        {/* Password Toggle */}\n        {showPasswordToggle && (\n          <button\n            type=\"button\"\n            onClick={() => setShowPassword(!showPassword)}\n            className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n          >\n            {showPassword ? (\n              <svg className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n              </svg>\n            ) : (\n              <svg className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n            )}\n          </button>\n        )}\n      </div>\n      \n      {/* Error Message */}\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600 flex items-center\">\n          <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          {error}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;;;AAFA;;AAIe,SAAS,WAAW,EACjC,KAAK,EACL,OAAO,MAAM,EACb,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,KAAK,EAChB,IAAI,EACJ,qBAAqB,KAAK,EAC1B,QAAQ,IAAI,EACb;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,YAAY,qBAAsB,eAAe,SAAS,aAAc;IAE9E,qBACE,6LAAC;;0BACC,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAEnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,WAAW,CAAC,iBAAiB,EAAE,OAAO,UAAU,GAAG,CAAC,EAAE,qBAAqB,UAAU,GAAG,uGAAuG,EAC7L,QACI,wEACA,8EACJ;wBACF,aAAa;wBACb,UAAU;;;;;;oBAIX,sBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;oBAMN,oCACC,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAU;kCAET,6BACC,6LAAC;4BAAI,WAAU;4BAA4C,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnG,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;iDAGvE,6LAAC;4BAAI,WAAU;4BAA4C,MAAK;4BAAO,QAAO;4BAAe,SAAQ;;8CACnG,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAE;;;;;;8CACrE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;oBAEtE;;;;;;;;;;;;;AAKX;GA/EwB;KAAA", "debugId": null}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/ErrorMessage.js"], "sourcesContent": ["// frontend/components/auth/ErrorMessage.js\n'use client';\n\nexport default function ErrorMessage({ message, type = 'error' }) {\n  if (!message) return null;\n\n  const styles = {\n    error: {\n      bg: 'bg-red-50',\n      border: 'border-red-400',\n      text: 'text-red-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    success: {\n      bg: 'bg-green-50',\n      border: 'border-green-400',\n      text: 'text-green-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    warning: {\n      bg: 'bg-yellow-50',\n      border: 'border-yellow-400',\n      text: 'text-yellow-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    info: {\n      bg: 'bg-blue-50',\n      border: 'border-blue-400',\n      text: 'text-blue-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    }\n  };\n\n  const currentStyle = styles[type];\n\n  return (\n    <div className={`${currentStyle.bg} border-l-4 ${currentStyle.border} p-4 rounded-r-lg animate-fadeIn`}>\n      <div className=\"flex\">\n        <div className=\"flex-shrink-0\">\n          {currentStyle.icon}\n        </div>\n        <div className=\"ml-3\">\n          <p className={`text-sm ${currentStyle.text} font-medium`}>\n            {message}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAC3C;;AAEe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,OAAO,EAAE;IAC9D,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,SAAS;QACb,OAAO;YACL,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAuB,SAAQ;gBAAY,MAAK;0BAC7D,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAA0N,UAAS;;;;;;;;;;;QAGpQ;QACA,SAAS;YACP,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAyB,SAAQ;gBAAY,MAAK;0BAC/D,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAwI,UAAS;;;;;;;;;;;QAGlL;QACA,SAAS;YACP,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAA0B,SAAQ;gBAAY,MAAK;0BAChE,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAoN,UAAS;;;;;;;;;;;QAG9P;QACA,MAAM;YACJ,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,SAAQ;gBAAY,MAAK;0BAC9D,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAmI,UAAS;;;;;;;;;;;QAG7K;IACF;IAEA,MAAM,eAAe,MAAM,CAAC,KAAK;IAEjC,qBACE,6LAAC;QAAI,WAAW,GAAG,aAAa,EAAE,CAAC,YAAY,EAAE,aAAa,MAAM,CAAC,gCAAgC,CAAC;kBACpG,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,aAAa,IAAI;;;;;;8BAEpB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,IAAI,CAAC,YAAY,CAAC;kCACrD;;;;;;;;;;;;;;;;;;;;;;AAMb;KA9DwB", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/SubmitButton.js"], "sourcesContent": ["// frontend/components/auth/SubmitButton.js\n'use client';\n\nexport default function SubmitButton({ \n  loading = false, \n  children, \n  loadingText = 'جاري المعالجة...', \n  disabled = false,\n  variant = 'primary',\n  icon = null,\n  onClick = null,\n  type = 'submit'\n}) {\n  const variants = {\n    primary: 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white',\n    success: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white',\n    danger: 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={loading || disabled}\n      className={`w-full flex justify-center items-center px-6 py-3 border border-transparent text-base font-semibold rounded-xl focus:outline-none focus:ring-4 focus:ring-indigo-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${variants[variant]}`}\n    >\n      {loading ? (\n        <>\n          <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n          </svg>\n          {loadingText}\n        </>\n      ) : (\n        <>\n          {icon && <span className=\"mr-2\">{icon}</span>}\n          {children}\n        </>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAC3C;;AAEe,SAAS,aAAa,EACnC,UAAU,KAAK,EACf,QAAQ,EACR,cAAc,kBAAkB,EAChC,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,IAAI,EACd,OAAO,QAAQ,EAChB;IACC,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IAEA,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,WAAW;QACrB,WAAW,CAAC,4SAA4S,EAAE,QAAQ,CAAC,QAAQ,EAAE;kBAE5U,wBACC;;8BACE,6LAAC;oBAAI,WAAU;oBAA6C,OAAM;oBAA6B,MAAK;oBAAO,SAAQ;;sCACjH,6LAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,6LAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAEpD;;yCAGH;;gBACG,sBAAQ,6LAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;AAKX;KAxCwB", "debugId": null}}, {"offset": {"line": 1688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/LoadingSpinner.js"], "sourcesContent": ["// frontend/components/LoadingSpinner.js\n'use client';\n\nexport default function LoadingSpinner({ size = 'md', text = 'جاري التحميل...' }) {\n  const sizes = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n    xl: 'h-16 w-16'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <div className={`animate-spin rounded-full border-b-2 border-indigo-600 ${sizes[size]}`}></div>\n      {text && (\n        <p className=\"mt-4 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;;AAEe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,OAAO,iBAAiB,EAAE;IAC9E,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAW,CAAC,uDAAuD,EAAE,KAAK,CAAC,KAAK,EAAE;;;;;;YACtF,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;KAhBwB", "debugId": null}}, {"offset": {"line": 1739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/customers/edit/%5Bid%5D/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { getCustomerById, updateCustomer } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport InputField from '@/components/auth/InputField';\nimport ErrorMessage from '@/components/auth/ErrorMessage';\nimport SubmitButton from '@/components/auth/SubmitButton';\nimport LoadingSpinner from '@/components/LoadingSpinner';\n\nfunction EditCustomerPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    company: '',\n    taxId: '',\n    notes: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [pageLoading, setPageLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [fieldErrors, setFieldErrors] = useState({});\n  const router = useRouter();\n  const params = useParams();\n  const customerId = params.id;\n\n  useEffect(() => {\n    if (customerId) {\n      loadCustomer();\n    }\n  }, [customerId]);\n\n  const loadCustomer = async () => {\n    try {\n      setPageLoading(true);\n      const customer = await getCustomerById(customerId);\n      setFormData({\n        name: customer.name || '',\n        email: customer.email || '',\n        phone: customer.phone || '',\n        address: customer.address || '',\n        company: customer.companyName || '',\n        taxId: customer.taxId || '',\n        notes: customer.notes || ''\n      });\n    } catch (err) {\n      setError('فشل في تحميل بيانات العميل');\n      console.error('Error loading customer:', err);\n    } finally {\n      setPageLoading(false);\n    }\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (fieldErrors[name]) {\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      errors.name = 'اسم العميل مطلوب';\n    } else if (formData.name.trim().length < 2) {\n      errors.name = 'اسم العميل يجب أن يكون حرفين على الأقل';\n    }\n\n    // Email validation\n    if (formData.email) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        errors.email = 'البريد الإلكتروني غير صحيح';\n      }\n    }\n\n    // Phone validation\n    if (formData.phone) {\n      const phoneRegex = /^[\\+]?[0-9\\s\\-\\(\\)]{10,}$/;\n      if (!phoneRegex.test(formData.phone)) {\n        errors.phone = 'رقم الهاتف غير صحيح';\n      }\n    }\n\n    return errors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    // Validate form\n    const errors = validateForm();\n    if (Object.keys(errors).length > 0) {\n      setFieldErrors(errors);\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setFieldErrors({});\n\n    try {\n      // Prepare data for API\n      const customerData = {\n        name: formData.name.trim(),\n        email: formData.email.trim() || null,\n        phone: formData.phone.trim() || null,\n        address: formData.address.trim() || null,\n        companyName: formData.company.trim() || null,\n        taxId: formData.taxId.trim() || null,\n        notes: formData.notes.trim() || null\n      };\n\n      await updateCustomer(customerId, customerData);\n      setSuccess('تم تحديث بيانات العميل بنجاح!');\n      \n      setTimeout(() => {\n        router.push('/dashboard/customers');\n      }, 1500);\n    } catch (err) {\n      setError(err.message || 'فشل في تحديث بيانات العميل. يرجى المحاولة مرة أخرى.');\n      console.error('Error updating customer:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (pageLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <LoadingSpinner size=\"lg\" text=\"جاري تحميل بيانات العميل...\" />\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <nav className=\"flex\" aria-label=\"Breadcrumb\">\n                  <ol className=\"flex items-center space-x-4\">\n                    <li>\n                      <Link href=\"/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                        <svg className=\"flex-shrink-0 h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n                        </svg>\n                      </Link>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <Link href=\"/dashboard/customers\" className=\"ml-4 text-sm font-medium text-gray-500 hover:text-gray-700\">\n                          العملاء\n                        </Link>\n                      </div>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <span className=\"ml-4 text-sm font-medium text-gray-900\">تعديل العميل</span>\n                      </div>\n                    </li>\n                  </ol>\n                </nav>\n                <h1 className=\"mt-2 text-2xl font-bold text-gray-900\">تعديل بيانات العميل</h1>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  تحديث بيانات العميل\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {error && <div className=\"mb-6\"><ErrorMessage message={error} type=\"error\" /></div>}\n          {success && <div className=\"mb-6\"><ErrorMessage message={success} type=\"success\" /></div>}\n\n          <div className=\"bg-white shadow-sm rounded-xl border border-gray-200\">\n            <div className=\"px-6 py-8\">\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Name Field */}\n                  <div className=\"md:col-span-2\">\n                    <InputField\n                      label=\"اسم العميل\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleChange}\n                      placeholder=\"أدخل اسم العميل الكامل\"\n                      required\n                      error={fieldErrors.name}\n                      icon={\n                        <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                      }\n                    />\n                  </div>\n\n                  {/* Email Field */}\n                  <InputField\n                    label=\"البريد الإلكتروني\"\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    placeholder=\"<EMAIL>\"\n                    error={fieldErrors.email}\n                    icon={\n                      <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                      </svg>\n                    }\n                  />\n\n                  {/* Phone Field */}\n                  <InputField\n                    label=\"رقم الهاتف\"\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    placeholder=\"+966 50 123 4567\"\n                    error={fieldErrors.phone}\n                    icon={\n                      <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                      </svg>\n                    }\n                  />\n\n                  {/* Company Field */}\n                  <InputField\n                    label=\"اسم الشركة\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleChange}\n                    placeholder=\"اسم الشركة (اختياري)\"\n                    icon={\n                      <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m4 0V9a1 1 0 011-1h4a1 1 0 011 1v12m-6 0h6\" />\n                      </svg>\n                    }\n                  />\n\n                  {/* Tax ID Field */}\n                  <InputField\n                    label=\"الرقم الضريبي\"\n                    name=\"taxId\"\n                    value={formData.taxId}\n                    onChange={handleChange}\n                    placeholder=\"الرقم الضريبي (اختياري)\"\n                    icon={\n                      <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    }\n                  />\n\n                  {/* Address Field */}\n                  <div className=\"md:col-span-2\">\n                    <label htmlFor=\"address\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                      العنوان\n                    </label>\n                    <div className=\"relative\">\n                      <textarea\n                        id=\"address\"\n                        name=\"address\"\n                        value={formData.address}\n                        onChange={handleChange}\n                        rows={3}\n                        className=\"w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none\"\n                        placeholder=\"أدخل العنوان الكامل (اختياري)\"\n                      />\n                      <div className=\"absolute top-3 left-3 pointer-events-none\">\n                        <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Notes Field */}\n                  <div className=\"md:col-span-2\">\n                    <label htmlFor=\"notes\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                      ملاحظات\n                    </label>\n                    <div className=\"relative\">\n                      <textarea\n                        id=\"notes\"\n                        name=\"notes\"\n                        value={formData.notes}\n                        onChange={handleChange}\n                        rows={3}\n                        className=\"w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none\"\n                        placeholder=\"ملاحظات إضافية عن العميل (اختياري)\"\n                      />\n                      <div className=\"absolute top-3 left-3 pointer-events-none\">\n                        <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                        </svg>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200\">\n                  <SubmitButton\n                    loading={loading}\n                    loadingText=\"جاري تحديث البيانات...\"\n                    variant=\"success\"\n                    icon={\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                    }\n                  >\n                    حفظ التغييرات\n                  </SubmitButton>\n                  \n                  <Link\n                    href=\"/dashboard/customers\"\n                    className=\"inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-base font-semibold rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-4 focus:ring-gray-100 transition-all duration-200\"\n                  >\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                    </svg>\n                    إلغاء\n                  </Link>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(EditCustomerPage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY;gBACd;YACF;QACF;qCAAG;QAAC;KAAW;IAEf,MAAM,eAAe;QACnB,IAAI;YACF,eAAe;YACf,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,kBAAe,AAAD,EAAE;YACvC,YAAY;gBACV,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,SAAS,KAAK,IAAI;gBACzB,OAAO,SAAS,KAAK,IAAI;gBACzB,SAAS,SAAS,OAAO,IAAI;gBAC7B,SAAS,SAAS,WAAW,IAAI;gBACjC,OAAO,SAAS,KAAK,IAAI;gBACzB,OAAO,SAAS,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,4CAA4C;QAC5C,IAAI,WAAW,CAAC,KAAK,EAAE;YACrB,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,CAAC;QAEhB,kBAAkB;QAClB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,OAAO,IAAI,GAAG;QAChB;QAEA,mBAAmB;QACnB,IAAI,SAAS,KAAK,EAAE;YAClB,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;gBACpC,OAAO,KAAK,GAAG;YACjB;QACF;QAEA,mBAAmB;QACnB,IAAI,SAAS,KAAK,EAAE;YAClB,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;gBACpC,OAAO,KAAK,GAAG;YACjB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,MAAM,SAAS;QACf,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;YAClC,eAAe;YACf;QACF;QAEA,WAAW;QACX,SAAS;QACT,eAAe,CAAC;QAEhB,IAAI;YACF,uBAAuB;YACvB,MAAM,eAAe;gBACnB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,SAAS,SAAS,OAAO,CAAC,IAAI,MAAM;gBACpC,aAAa,SAAS,OAAO,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;YAClC;YAEA,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;YACjC,WAAW;YAEX,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;YACxB,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC,gIAAA,CAAA,UAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,MAAK;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;wCAAO,cAAW;kDAC/B,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAChC,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8DAId,6LAAC;8DACC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAuB,WAAU;0EAA6D;;;;;;;;;;;;;;;;;8DAK7G,6LAAC;8DACC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,6LAAC;gEAAK,WAAU;0EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjE,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAI,WAAU;sCAAO,cAAA,6LAAC,qIAAA,CAAA,UAAY;gCAAC,SAAS;gCAAO,MAAK;;;;;;;;;;;wBAClE,yBAAW,6LAAC;4BAAI,WAAU;sCAAO,cAAA,6LAAC,qIAAA,CAAA,UAAY;gCAAC,SAAS;gCAAS,MAAK;;;;;;;;;;;sCAEvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;wDACT,OAAM;wDACN,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,aAAY;wDACZ,QAAQ;wDACR,OAAO,YAAY,IAAI;wDACvB,oBACE,6LAAC;4DAAI,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC7C,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAO7E,6LAAC,mIAAA,CAAA,UAAU;oDACT,OAAM;oDACN,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,aAAY;oDACZ,OAAO,YAAY,KAAK;oDACxB,oBACE,6LAAC;wDAAI,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7C,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAM3E,6LAAC,mIAAA,CAAA,UAAU;oDACT,OAAM;oDACN,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,aAAY;oDACZ,OAAO,YAAY,KAAK;oDACxB,oBACE,6LAAC;wDAAI,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7C,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAM3E,6LAAC,mIAAA,CAAA,UAAU;oDACT,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,aAAY;oDACZ,oBACE,6LAAC;wDAAI,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7C,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAM3E,6LAAC,mIAAA,CAAA,UAAU;oDACT,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,aAAY;oDACZ,oBACE,6LAAC;wDAAI,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7C,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAM3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAiD;;;;;;sEAGpF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,OAAO;oEACvB,UAAU;oEACV,MAAM;oEACN,WAAU;oEACV,aAAY;;;;;;8EAEd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAAwB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;;0FAC/E,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;0FACrE,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAO7E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAAiD;;;;;;sEAGlF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,MAAM;oEACN,WAAU;oEACV,aAAY;;;;;;8EAEd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAAwB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC/E,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ/E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,UAAY;oDACX,SAAS;oDACT,aAAY;oDACZ,SAAQ;oDACR,oBACE,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;8DAG1E;;;;;;8DAID,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;GAjWS;;QAeQ,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAhBjB;6CAmWM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}