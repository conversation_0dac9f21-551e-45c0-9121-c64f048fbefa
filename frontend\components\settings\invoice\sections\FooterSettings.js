// frontend/components/settings/invoice/sections/FooterSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function FooterSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const footerFields = [
    { field: 'showNotes', label: 'الملاحظات', description: 'عرض ملاحظات الفاتورة' },
    { field: 'showFooter', label: 'تذييل مخصص', description: 'عرض نص التذييل المخصص' },
    { field: 'showSignature', label: 'مكان التوقيع', description: 'عرض مساحة للتوقيع' },
    { field: 'showQRCode', label: 'رمز QR', description: 'عرض رمز QR للفاتورة' },
    { field: 'showBankDetails', label: 'تفاصيل البنك', description: 'عرض معلومات الحساب البنكي' },
    { field: 'showPaymentInstructions', label: 'تعليمات الدفع', description: 'عرض تعليمات الدفع' }
  ];

  return (
    <SectionHeader
      title="تذييل الفاتورة"
      section="footer"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
        </svg>
      }
    >
      {footerFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
