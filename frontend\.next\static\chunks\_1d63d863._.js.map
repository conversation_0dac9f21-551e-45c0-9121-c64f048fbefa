{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,6LAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,6LAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA7PwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ClassicTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // بيانات العينة للمعاينة فقط\n  const sampleData = preview ? {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة',\n    companyPhone: '+966 50 123 4567',\n    companyEmail: '<EMAIL>',\n    companyWebsite: 'www.company.com',\n    taxId: '*********'\n  } : invoice;\n\n  const data = sampleData;\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header with Teal Design */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              <p className=\"text-teal-100\">{data.companyDetails}</p>\n            </div>\n\n            {/* Logo Area */}\n            <div className=\"bg-white p-4 rounded-lg shadow-md\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                <div className=\"text-white font-bold text-xl\">شعار</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">رقم الفاتورة:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">اسم العميل:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">التاريخ:</span>\n          </div>\n\n          <div>{data.invoiceNumber}</div>\n          <div>{data.customerName}</div>\n          <div>{data.date}</div>\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"bg-yellow-100\">\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">البيان</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Totals */}\n      <div className=\"flex justify-between items-end mb-8\">\n        <div className=\"text-right\">\n          <p className=\"text-lg font-semibold mb-4\">{data.notes}</p>\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">السعر</span>\n              <span>{(Number(data.subtotal) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">الضريبة</span>\n              <span>{(Number(data.tax) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"border-t border-gray-300 pt-2\">\n              <div className=\"flex justify-between font-bold text-lg\">\n                <span>الإجمالي</span>\n                <span>{(Number(data.total) || 0).toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"flex justify-between items-end\">\n        <div>\n          <h4 className=\"font-semibold mb-2\">ملاحظات</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• البضاعة المباعة لا ترد ولا تستبدل</li>\n            <li>• التأكد من استلام جميع بنود الفاتورة</li>\n          </ul>\n        </div>\n\n        <div className=\"text-center\">\n          <h4 className=\"font-semibold mb-4\">توقيع البائع</h4>\n          <div className=\"w-32 border-b-2 border-gray-400 mb-2\"></div>\n        </div>\n      </div>\n\n      {/* Contact Info */}\n      <div className=\"mt-8 pt-4 border-t border-gray-300\">\n        <div className=\"flex justify-between text-sm text-gray-600\">\n          <div>\n            <p>+123-456-7890</p>\n            <p>www.reallygreatsite.com</p>\n            <p><EMAIL></p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"w-32 h-8 bg-gray-800 flex items-center justify-center\">\n              <div className=\"text-white text-xs\">|||||||||||||||||||||||</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IACxF,6BAA6B;IAC7B,MAAM,aAAa,UAAU;QAC3B,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;IACT,IAAI;IAEJ,MAAM,OAAO;IAEb,qBACE,6LAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAiB,KAAK,cAAc;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAGlC,6LAAC;0CAAK,KAAK,aAAa;;;;;;0CACxB,6LAAC;0CAAK,KAAK,YAAY;;;;;;0CACvB,6LAAC;0CAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAGxE,6LAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,6LAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;0DACxE,6LAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;0DAC3E,6LAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCALxD,KAAK,EAAE;;;;;gCAUjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCALpC,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAa7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA8B,KAAK,KAAK;;;;;;;;;;;kCAGvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;;;;;;;sCAEL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;KAvKwB", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ModernTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ModernTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ModernTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Modern Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 text-white\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold mb-2\">{data.companyName}</h1>\n            <p className=\"text-blue-100 text-lg\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4\">\n              <h2 className=\"text-2xl font-bold mb-2\">فاتورة</h2>\n              <p className=\"text-blue-100\">#{data.invoiceNumber}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-8\">\n        {/* Customer Info Card */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8\">\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات العميل</h3>\n              <div className=\"space-y-2\">\n                <p className=\"text-gray-700 font-medium\">{data.customerName}</p>\n                <p className=\"text-gray-600\">{data.customerDetails}</p>\n              </div>\n            </div>\n\n            <div className=\"text-right\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">تفاصيل الفاتورة</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">التاريخ:</span>\n                  <span className=\"font-medium\">{data.date}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                  <span className=\"font-medium\">{data.invoiceNumber}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Items Table */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">تفاصيل الخدمات</h3>\n\n          <div className=\"overflow-hidden rounded-xl border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n                <tr>\n                  <th className=\"p-4 text-right font-semibold\">الإجمالي</th>\n                  <th className=\"p-4 text-right font-semibold\">السعر</th>\n                  <th className=\"p-4 text-right font-semibold\">الكمية</th>\n                  <th className=\"p-4 text-right font-semibold\">الوصف</th>\n                  <th className=\"p-4 text-right font-semibold\">#</th>\n                </tr>\n              </thead>\n              <tbody>\n                {data.items.map((item, index) => (\n                  <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>\n                    <td className=\"p-4 text-right font-semibold text-blue-600\">\n                      {(Number(item.total) || 0).toFixed(2)} ر.س\n                    </td>\n                    <td className=\"p-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm\">\n                        {item.quantity || 0}\n                      </span>\n                    </td>\n                    <td className=\"p-4 text-right font-medium\">{item.description || ''}</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm font-medium\">\n                        {index + 1}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Modern Totals */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div className=\"w-1/2\">\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6\">\n              <h4 className=\"text-lg font-semibold text-gray-800 mb-3\">ملاحظات</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h5 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h5>\n                <ul className=\"text-sm text-gray-600 space-y-1\">\n                  <li>• الدفع خلال 30 يوم من تاريخ الفاتورة</li>\n                  <li>• جميع الأسعار شاملة ضريبة القيمة المضافة</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"w-80\">\n            <div className=\"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white\">\n              <h4 className=\"text-lg font-semibold mb-4\">ملخص الفاتورة</h4>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"border-t border-white border-opacity-30 pt-3\">\n                  <div className=\"flex justify-between text-xl font-bold\">\n                    <span>الإجمالي النهائي:</span>\n                    <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Footer */}\n        <div className=\"bg-gray-50 rounded-xl p-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">معلومات التواصل</h4>\n              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                <span>📞 +966 50 123 4567</span>\n                <span>📧 <EMAIL></span>\n                <span>🌐 www.company.com</span>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-32 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold\">\n                شعار الشركة\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;AAFA;;;AAIe,SAAS,eAAe,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IACjE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAEpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B,KAAK,WAAW;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAyB,KAAK,cAAc;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAE,WAAU;;4CAAgB;4CAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B,KAAK,YAAY;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,IAAI;;;;;;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;sDACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;oDAAiB,WAAW,QAAQ,MAAM,IAAI,eAAe;;sEAC5D,6LAAC;4DAAG,WAAU;;gEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,6LAAC;4DAAG,WAAU;;gEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACrE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;sEAGtB,6LAAC;4DAAG,WAAU;sEAA8B,KAAK,WAAW,IAAI;;;;;;sEAChE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;;;;;;;mDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwB1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAE5C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvJ;KA3KwB", "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ElegantTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ElegantTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ElegantTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'مؤسسة الأناقة التجارية',\n    customerDetails: 'للتجارة والمقاولات',\n    items: [\n      { id: 1, description: 'أثاث مكتبي فاخر', quantity: 5, price: 800.00, total: 4000.00 },\n      { id: 2, description: 'ديكورات داخلية', quantity: 1, price: 2500.00, total: 2500.00 },\n      { id: 3, description: 'إكسسوارات مكتبية', quantity: 10, price: 150.00, total: 1500.00 }\n    ],\n    subtotal: 8000.00,\n    tax: 1200.00,\n    total: 9200.00,\n    notes: 'نتطلع لخدمتكم مرة أخرى',\n    companyName: 'مؤسسة الأناقة التجارية',\n    companyDetails: 'للتجارة والمقاولات'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Elegant Header */}\n      <div className=\"relative\">\n        <div className=\"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600\"></div>\n\n        <div className=\"p-8 pt-12\">\n          <div className=\"flex justify-between items-start mb-8\">\n            <div>\n              <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n              <p className=\"text-gray-600 text-lg italic\">{data.companyDetails}</p>\n\n              <div className=\"mt-6 w-24 h-1 bg-gradient-to-r from-amber-400 to-yellow-500\"></div>\n            </div>\n\n            <div className=\"text-right\">\n              <div className=\"border-2 border-amber-400 rounded-lg p-4 bg-amber-50\">\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-1\">فاتورة</h2>\n                <p className=\"text-amber-600 font-semibold\">#{data.invoiceNumber}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Customer Section */}\n          <div className=\"border border-gray-200 rounded-lg p-6 mb-8 bg-gray-50\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  فواتير إلى\n                </h3>\n                <div className=\"space-y-2\">\n                  <p className=\"text-gray-800 font-semibold text-lg\">{data.customerName}</p>\n                  <p className=\"text-gray-600\">{data.customerDetails}</p>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  تفاصيل الفاتورة\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                    <span className=\"font-semibold\">{data.date}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">رقم المرجع:</span>\n                    <span className=\"font-semibold\">{data.invoiceNumber}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Items Table */}\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-serif font-semibold text-gray-800 mb-4 border-b-2 border-amber-400 pb-2\">\n              بنود الفاتورة\n            </h3>\n\n            <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gradient-to-r from-gray-100 to-amber-50\">\n                  <tr>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">المبلغ</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">سعر الوحدة</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الكمية</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الوصف</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">م</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {data.items.map((item, index) => (\n                    <tr key={item.id} className=\"border-b border-gray-100 hover:bg-amber-25\">\n                      <td className=\"p-4 text-right font-semibold text-amber-600\">\n                        {(Number(item.total) || 0).toFixed(2)} ر.س\n                      </td>\n                      <td className=\"p-4 text-right text-gray-700\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium\">\n                          {item.quantity || 0}\n                        </span>\n                      </td>\n                      <td className=\"p-4 text-right font-medium text-gray-800\">{item.description || ''}</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                          {index + 1}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Elegant Summary */}\n          <div className=\"flex justify-between items-start mb-8\">\n            <div className=\"w-1/2 pr-8\">\n              <div className=\"border-l-4 border-amber-400 pl-6\">\n                <h4 className=\"text-lg font-serif font-semibold text-gray-800 mb-3\">ملاحظات خاصة</h4>\n                <p className=\"text-gray-700 italic mb-4\">{data.notes}</p>\n\n                <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n                  <h5 className=\"font-semibold text-gray-800 mb-2\">شروط وأحكام:</h5>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• الدفع خلال 15 يوم من تاريخ الفاتورة</li>\n                    <li>• ضمان جودة لمدة سنة كاملة</li>\n                    <li>• خدمة ما بعد البيع متاحة</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"w-80\">\n              <div className=\"border-2 border-amber-300 rounded-lg bg-gradient-to-br from-amber-50 to-yellow-50\">\n                <div className=\"bg-gradient-to-r from-amber-400 to-yellow-500 text-white p-4 rounded-t-lg\">\n                  <h4 className=\"text-lg font-serif font-semibold\">الملخص المالي</h4>\n                </div>\n\n                <div className=\"p-6 space-y-4\">\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>المجموع الفرعي:</span>\n                    <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>ضريبة القيمة المضافة:</span>\n                    <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"border-t-2 border-amber-300 pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold text-gray-800\">\n                      <span className=\"font-serif\">الإجمالي النهائي:</span>\n                      <span className=\"text-amber-600\">{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Footer */}\n          <div className=\"border-t-2 border-amber-300 pt-6\">\n            <div className=\"flex justify-between items-end\">\n              <div>\n                <h4 className=\"font-serif font-semibold text-gray-800 mb-3\">معلومات التواصل</h4>\n                <div className=\"space-y-1 text-sm text-gray-600\">\n                  <p>📱 الهاتف: +966 50 123 4567</p>\n                  <p>📧 البريد: <EMAIL></p>\n                  <p>🌐 الموقع: www.elegant-business.com</p>\n                  <p>📍 العنوان: الرياض، المملكة العربية السعودية</p>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <h5 className=\"font-serif font-semibold text-gray-800 mb-4\">توقيع مخول</h5>\n                <div className=\"w-32 h-16 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-amber-500 text-sm\">التوقيع</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAmB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAoB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;SACvF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;kBAEpE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoD,KAAK,WAAW;;;;;;sDAClF,6LAAC;4CAAE,WAAU;sDAAgC,KAAK,cAAc;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;;oDAA+B;oDAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAMtE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuC,KAAK,YAAY;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kDAItD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAIrG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAGnG,6LAAC;0DACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEAAG,WAAU;;oEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EAExC,6LAAC;gEAAG,WAAU;;oEAAgC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACnF,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;0EAGtB,6LAAC;gEAAG,WAAU;0EAA4C,KAAK,WAAW,IAAI;;;;;;0EAC9E,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EACb,QAAQ;;;;;;;;;;;;uDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE3E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAa;;;;;;8EAC7B,6LAAC;oEAAK,WAAU;;wEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;kDAIP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;KA1LwB", "debugId": null}}, {"offset": {"line": 3012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/MinimalTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/MinimalTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function MinimalTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة البساطة للأعمال',\n    customerDetails: 'الحلول البسيطة والفعالة',\n    items: [\n      { id: 1, description: 'خدمة استشارية', quantity: 2, price: 500.00, total: 1000.00 },\n      { id: 2, description: 'تطوير موقع إلكتروني', quantity: 1, price: 3000.00, total: 3000.00 },\n      { id: 3, description: 'صيانة شهرية', quantity: 6, price: 200.00, total: 1200.00 }\n    ],\n    subtotal: 5200.00,\n    tax: 780.00,\n    total: 5980.00,\n    notes: 'شكراً لاختياركم خدماتنا',\n    companyName: 'شركة البساطة للأعمال',\n    companyDetails: 'الحلول البسيطة والفعالة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white p-8\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Minimal Header */}\n      <div className=\"border-b-4 border-gray-800 pb-6 mb-8\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n            <p className=\"text-gray-600\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <h2 className=\"text-4xl font-bold text-gray-800 mb-2\">فاتورة</h2>\n            <p className=\"text-gray-600 text-lg\">#{data.invoiceNumber}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Customer and Invoice Info */}\n      <div className=\"grid grid-cols-2 gap-12 mb-8\">\n        <div>\n          <h3 className=\"text-lg font-bold text-gray-800 mb-3\">إلى:</h3>\n          <div className=\"space-y-1\">\n            <p className=\"text-gray-800 font-semibold\">{data.customerName}</p>\n            <p className=\"text-gray-600\">{data.customerDetails}</p>\n          </div>\n        </div>\n\n        <div className=\"text-right\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">تاريخ الفاتورة:</span>\n              <span className=\"font-semibold\">{data.date}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">رقم الفاتورة:</span>\n              <span className=\"font-semibold\">{data.invoiceNumber}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"border-b-2 border-gray-800\">\n              <th className=\"py-3 text-right font-bold text-gray-800\">المبلغ</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">السعر</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الكمية</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الوصف</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id} className=\"border-b border-gray-200\">\n                <td className=\"py-4 text-right font-semibold\">{(Number(item.total) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{item.quantity || 0}</td>\n                <td className=\"py-4 text-right\">{item.description || ''}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Simple Totals */}\n      <div className=\"flex justify-between items-start mb-12\">\n        <div className=\"w-1/2\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3\">ملاحظات:</h4>\n          <p className=\"text-gray-700\">{data.notes}</p>\n\n          <div className=\"mt-6\">\n            <h5 className=\"font-bold text-gray-800 mb-2\">شروط الدفع:</h5>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• الدفع خلال 30 يوم</li>\n              <li>• تطبق غرامة تأخير 2% شهرياً</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"w-80\">\n          <div className=\"border-2 border-gray-800 p-6\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">المجموع الفرعي:</span>\n                <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">الضريبة (15%):</span>\n                <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"border-t-2 border-gray-800 pt-3\">\n                <div className=\"flex justify-between text-xl font-bold\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Footer */}\n      <div className=\"border-t border-gray-300 pt-6\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-600\">\n            <p>الهاتف: +966 50 123 4567 | البريد: <EMAIL> | الموقع: www.simple.com</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-24 h-12 border border-gray-400 flex items-center justify-center text-gray-500 text-sm\">\n              الشعار\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAiB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YAClF;gBAAE,IAAI;gBAAG,aAAa;gBAAuB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACzF;gBAAE,IAAI;gBAAG,aAAa;gBAAe,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;SACjF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAExE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC,KAAK,WAAW;;;;;;8CACvE,6LAAC;oCAAE,WAAU;8CAAiB,KAAK,cAAc;;;;;;;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B,KAAK,YAAY;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;sDAAiB,KAAK,IAAI;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;sDAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;;;;;;;;;;;;sCAG5D,6LAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAG,WAAU;;gDAAiC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACpF,6LAAC;4CAAG,WAAU;;gDAAmB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACtE,6LAAC;4CAAG,WAAU;sDAAmB,KAAK,QAAQ,IAAI;;;;;;sDAClD,6LAAC;4CAAG,WAAU;sDAAmB,KAAK,WAAW,IAAI;;;;;;;mCAJ9C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAYxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;sCAGL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAA0F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrH;KA3IwB", "debugId": null}}, {"offset": {"line": 3615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/TemplatePreview.js"], "sourcesContent": ["// frontend/components/invoice-templates/TemplatePreview.js\n'use client';\n\nimport React, { useState } from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\n\nconst templates = {\n  classic: {\n    id: 'classic',\n    name: 'القالب الكلاسيكي',\n    description: 'تصميم تقليدي أنيق مع ألوان الأخضر والذهبي',\n    component: ClassicTemplate,\n    preview: '/images/templates/classic-preview.jpg',\n    features: ['تصميم تقليدي', 'ألوان هادئة', 'مناسب للشركات التقليدية']\n  },\n  modern: {\n    id: 'modern',\n    name: 'القالب الحديث',\n    description: 'تصميم عصري مع تدرجات لونية جذابة',\n    component: ModernTemplate,\n    preview: '/images/templates/modern-preview.jpg',\n    features: ['تصميم عصري', 'تدرجات لونية', 'مناسب للشركات التقنية']\n  },\n  elegant: {\n    id: 'elegant',\n    name: 'القالب الأنيق',\n    description: 'تصميم راقي مع لمسات ذهبية فاخرة',\n    component: ElegantTemplate,\n    preview: '/images/templates/elegant-preview.jpg',\n    features: ['تصميم راقي', 'لمسات ذهبية', 'مناسب للشركات الفاخرة']\n  },\n  minimal: {\n    id: 'minimal',\n    name: 'القالب البسيط',\n    description: 'تصميم بسيط ونظيف للاستخدام العام',\n    component: MinimalTemplate,\n    preview: '/images/templates/minimal-preview.jpg',\n    features: ['تصميم بسيط', 'سهل القراءة', 'مناسب لجميع الأعمال']\n  }\n};\n\nexport default function TemplatePreview({ onSelectTemplate, selectedTemplate }) {\n  const [previewTemplate, setPreviewTemplate] = useState(null);\n  const [scale, setScale] = useState(0.3);\n\n  const handlePreview = (templateId) => {\n    setPreviewTemplate(templateId);\n  };\n\n  const closePreview = () => {\n    setPreviewTemplate(null);\n  };\n\n  const handleSelect = (templateId) => {\n    onSelectTemplate(templateId);\n    closePreview();\n  };\n\n  return (\n    <div>\n      {/* Templates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {Object.values(templates).map((template) => (\n          <div\n            key={template.id}\n            className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:scale-105 ${\n              selectedTemplate === template.id\n                ? 'border-blue-500 ring-4 ring-blue-100'\n                : 'border-gray-200 hover:border-blue-300'\n            }`}\n          >\n            {/* Template Preview Image */}\n            <div className=\"relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden\">\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"transform scale-[0.15] origin-top-left\">\n                  <template.component preview={true} />\n                </div>\n              </div>\n              \n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-105\"\n                >\n                  معاينة كاملة\n                </button>\n              </div>\n            </div>\n\n            {/* Template Info */}\n            <div className=\"p-4\">\n              <h3 className=\"text-lg font-bold text-gray-800 mb-2\">{template.name}</h3>\n              <p className=\"text-gray-600 text-sm mb-3\">{template.description}</p>\n              \n              {/* Features */}\n              <div className=\"mb-4\">\n                <div className=\"flex flex-wrap gap-1\">\n                  {template.features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-2\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors\"\n                >\n                  معاينة\n                </button>\n                <button\n                  onClick={() => handleSelect(template.id)}\n                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${\n                    selectedTemplate === template.id\n                      ? 'bg-green-500 text-white'\n                      : 'bg-blue-500 text-white hover:bg-blue-600'\n                  }`}\n                >\n                  {selectedTemplate === template.id ? 'محدد' : 'اختيار'}\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Full Preview Modal */}\n      {previewTemplate && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white rounded-xl max-w-6xl max-h-[90vh] overflow-hidden flex flex-col\">\n            {/* Modal Header */}\n            <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\n              <div>\n                <h3 className=\"text-xl font-bold text-gray-800\">\n                  معاينة {templates[previewTemplate].name}\n                </h3>\n                <p className=\"text-gray-600 text-sm\">\n                  {templates[previewTemplate].description}\n                </p>\n              </div>\n              \n              <div className=\"flex items-center gap-4\">\n                {/* Zoom Controls */}\n                <div className=\"flex items-center gap-2\">\n                  <button\n                    onClick={() => setScale(Math.max(0.1, scale - 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 12H4\" />\n                    </svg>\n                  </button>\n                  <span className=\"text-sm font-medium min-w-[60px] text-center\">\n                    {Math.round(scale * 100)}%\n                  </span>\n                  <button\n                    onClick={() => setScale(Math.min(1, scale + 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                  </button>\n                </div>\n                \n                <button\n                  onClick={closePreview}\n                  className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Preview Content */}\n            <div className=\"flex-1 overflow-auto p-4 bg-gray-100\">\n              <div className=\"flex justify-center\">\n                <div \n                  className=\"bg-white shadow-lg\"\n                  style={{ \n                    transform: `scale(${scale})`,\n                    transformOrigin: 'top center'\n                  }}\n                >\n                  {React.createElement(templates[previewTemplate].component, { preview: true })}\n                </div>\n              </div>\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex gap-2\">\n                  {templates[previewTemplate].features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={closePreview}\n                    className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    إغلاق\n                  </button>\n                  <button\n                    onClick={() => handleSelect(previewTemplate)}\n                    className=\"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium\"\n                  >\n                    اختيار هذا القالب\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport { templates };\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,YAAY;IAChB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,wJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAgB;YAAe;SAA0B;IACtE;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,uJAAA,CAAA,UAAc;QACzB,SAAS;QACT,UAAU;YAAC;YAAc;YAAgB;SAAwB;IACnE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,wJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAwB;IAClE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,wJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAsB;IAChE;AACF;AAEe,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB,CAAC;QACrB,mBAAmB;IACrB;IAEA,MAAM,eAAe;QACnB,mBAAmB;IACrB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB;IACF;IAEA,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,yBAC7B,6LAAC;wBAEC,WAAW,CAAC,mGAAmG,EAC7G,qBAAqB,SAAS,EAAE,GAC5B,yCACA,yCACJ;;0CAGF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,SAAS,SAAS;gDAAC,SAAS;;;;;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,cAAc,SAAS,EAAE;4CACxC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC,SAAS,IAAI;;;;;;kDACnE,6LAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;kDAUb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,cAAc,SAAS,EAAE;gDACxC,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,aAAa,SAAS,EAAE;gDACvC,WAAW,CAAC,kEAAkE,EAC5E,qBAAqB,SAAS,EAAE,GAC5B,4BACA,4CACJ;0DAED,qBAAqB,SAAS,EAAE,GAAG,SAAS;;;;;;;;;;;;;;;;;;;uBA7D9C,SAAS,EAAE;;;;;;;;;;YAsErB,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAAkC;gDACtC,SAAS,CAAC,gBAAgB,CAAC,IAAI;;;;;;;sDAEzC,6LAAC;4CAAE,WAAU;sDACV,SAAS,CAAC,gBAAgB,CAAC,WAAW;;;;;;;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,KAAK,QAAQ;oDAC9C,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,QAAQ;wDAAK;;;;;;;8DAE3B,6LAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,GAAG,QAAQ;oDAC5C,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAK3E,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;wCAC5B,iBAAiB;oCACnB;8CAEC,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE;wCAAE,SAAS;oCAAK;;;;;;;;;;;;;;;;sCAMjF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjD,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;kDAQX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAhMwB;KAAA", "debugId": null}}, {"offset": {"line": 4108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/TemplateSelector.js"], "sourcesContent": ["// frontend/components/invoice-templates/TemplateSelector.js\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { templates } from './TemplatePreview';\n\nexport default function TemplateSelector({ selectedTemplate, onTemplateChange, compact = false }) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentTemplate, setCurrentTemplate] = useState(selectedTemplate || 'classic');\n\n  useEffect(() => {\n    // Load saved template from localStorage\n    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');\n    if (savedTemplate && templates[savedTemplate]) {\n      setCurrentTemplate(savedTemplate);\n      onTemplateChange?.(savedTemplate);\n    }\n  }, [onTemplateChange]);\n\n  const handleTemplateSelect = (templateId) => {\n    setCurrentTemplate(templateId);\n    onTemplateChange?.(templateId);\n    setIsOpen(false);\n    \n    // Save to localStorage\n    localStorage.setItem('selectedInvoiceTemplate', templateId);\n  };\n\n  if (compact) {\n    return (\n      <div className=\"relative\">\n        <button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n        >\n          <svg className=\"w-4 h-4 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n          </svg>\n          <span className=\"text-sm font-medium\">{templates[currentTemplate]?.name}</span>\n          <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n            <div className=\"p-2\">\n              {Object.values(templates).map((template) => (\n                <button\n                  key={template.id}\n                  onClick={() => handleTemplateSelect(template.id)}\n                  className={`w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors ${\n                    currentTemplate === template.id ? 'bg-blue-50 border border-blue-200' : ''\n                  }`}\n                >\n                  <div className=\"font-medium text-sm\">{template.name}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">{template.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">اختيار قالب الفاتورة</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {Object.values(templates).map((template) => (\n          <div\n            key={template.id}\n            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${\n              currentTemplate === template.id\n                ? 'border-blue-500 bg-blue-50'\n                : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'\n            }`}\n            onClick={() => handleTemplateSelect(template.id)}\n          >\n            <div className=\"flex items-start gap-3\">\n              <div className=\"flex-shrink-0\">\n                <div className={`w-4 h-4 rounded-full border-2 ${\n                  currentTemplate === template.id\n                    ? 'border-blue-500 bg-blue-500'\n                    : 'border-gray-300'\n                }`}>\n                  {currentTemplate === template.id && (\n                    <div className=\"w-full h-full rounded-full bg-white scale-50\"></div>\n                  )}\n                </div>\n              </div>\n              \n              <div className=\"flex-1\">\n                <h4 className=\"font-medium text-gray-900\">{template.name}</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">{template.description}</p>\n                \n                <div className=\"flex flex-wrap gap-1 mt-2\">\n                  {template.features.slice(0, 2).map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"text-sm text-gray-600\">\n        <p>💡 يمكنك تغيير القالب في أي وقت من صفحة قوالب الفواتير</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;AAG5D;AACA;;;AAHA;;;AAKe,SAAS,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,KAAK,EAAE;;IAC9F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,wCAAwC;YACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,iBAAiB,wJAAA,CAAA,YAAS,CAAC,cAAc,EAAE;gBAC7C,mBAAmB;gBACnB,mBAAmB;YACrB;QACF;qCAAG;QAAC;KAAiB;IAErB,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,mBAAmB;QACnB,UAAU;QAEV,uBAAuB;QACvB,aAAa,OAAO,CAAC,2BAA2B;IAClD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAK,WAAU;sCAAuB,wJAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;;;;;;sCACnE,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;;;;;;;gBAIxE,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,CAAC,wJAAA,CAAA,YAAS,EAAE,GAAG,CAAC,CAAC,yBAC7B,6LAAC;gCAEC,SAAS,IAAM,qBAAqB,SAAS,EAAE;gCAC/C,WAAW,CAAC,mEAAmE,EAC7E,oBAAoB,SAAS,EAAE,GAAG,sCAAsC,IACxE;;kDAEF,6LAAC;wCAAI,WAAU;kDAAuB,SAAS,IAAI;;;;;;kDACnD,6LAAC;wCAAI,WAAU;kDAA8B,SAAS,WAAW;;;;;;;+BAP5D,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;IAehC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BAEpD,6LAAC;gBAAI,WAAU;0BACZ,OAAO,MAAM,CAAC,wJAAA,CAAA,YAAS,EAAE,GAAG,CAAC,CAAC,yBAC7B,6LAAC;wBAEC,WAAW,CAAC,sDAAsD,EAChE,oBAAoB,SAAS,EAAE,GAC3B,+BACA,0DACJ;wBACF,SAAS,IAAM,qBAAqB,SAAS,EAAE;kCAE/C,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,CAAC,8BAA8B,EAC7C,oBAAoB,SAAS,EAAE,GAC3B,gCACA,mBACJ;kDACC,oBAAoB,SAAS,EAAE,kBAC9B,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAKrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B,SAAS,IAAI;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAA8B,SAAS,WAAW;;;;;;sDAE/D,6LAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC3C,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;;uBA5BV,SAAS,EAAE;;;;;;;;;;0BAyCtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX;GAjHwB;KAAA", "debugId": null}}, {"offset": {"line": 4382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/InvoiceRenderer.js"], "sourcesContent": ["// frontend/components/invoice-templates/InvoiceRenderer.js\n'use client';\n\nimport React from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\n\nconst templateComponents = {\n  classic: ClassicTemplate,\n  modern: ModernTemplate,\n  elegant: ElegantTemplate,\n  minimal: MinimalTemplate\n};\n\nexport default function InvoiceRenderer({ invoice, templateId, preview = false }) {\n  // Get template from props or localStorage\n  const selectedTemplate = templateId || localStorage.getItem('selectedInvoiceTemplate') || 'classic';\n  \n  // Get the template component\n  const TemplateComponent = templateComponents[selectedTemplate] || ClassicTemplate;\n  \n  if (!TemplateComponent) {\n    return (\n      <div className=\"bg-white p-8 text-center\">\n        <p className=\"text-red-600\">خطأ: القالب المحدد غير موجود</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"invoice-container\">\n      <TemplateComponent invoice={invoice} preview={preview} />\n    </div>\n  );\n}\n\n// Export template components for direct use\nexport {\n  ClassicTemplate,\n  ModernTemplate,\n  ElegantTemplate,\n  MinimalTemplate,\n  templateComponents\n};\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,qBAAqB;IACzB,SAAS,wJAAA,CAAA,UAAe;IACxB,QAAQ,uJAAA,CAAA,UAAc;IACtB,SAAS,wJAAA,CAAA,UAAe;IACxB,SAAS,wJAAA,CAAA,UAAe;AAC1B;AAEe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,KAAK,EAAE;IAC9E,0CAA0C;IAC1C,MAAM,mBAAmB,cAAc,aAAa,OAAO,CAAC,8BAA8B;IAE1F,6BAA6B;IAC7B,MAAM,oBAAoB,kBAAkB,CAAC,iBAAiB,IAAI,wJAAA,CAAA,UAAe;IAEjF,IAAI,CAAC,mBAAmB;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAE,WAAU;0BAAe;;;;;;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAkB,SAAS;YAAS,SAAS;;;;;;;;;;;AAGpD;KApBwB", "debugId": null}}, {"offset": {"line": 4471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  return apiRequest(`${API_BASE_URL}/customers`, {\n    method: 'POST',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function getCustomerById(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, { method: 'GET' }, true);\n}\n\nexport async function updateCustomer(id, customerData) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function deleteCustomer(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'DELETE',\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QAAE,QAAQ;IAAM,GAAG;AAC1E;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 4898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoices/create/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport TemplateSelector from '@/components/invoice-templates/TemplateSelector';\nimport InvoiceRenderer from '@/components/invoice-templates/InvoiceRenderer';\nimport { templates } from '@/components/invoice-templates/TemplatePreview';\nimport { getCustomers, getProducts } from '@/lib/api';\n\nfunction CreateInvoicePage() {\n  const [step, setStep] = useState(1); // 1: Details, 2: Preview\n  const [selectedTemplate, setSelectedTemplate] = useState('classic');\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [invoiceData, setInvoiceData] = useState({\n    customerId: '',\n    customerName: '',\n    customerDetails: '',\n    items: [],\n    notes: '',\n    dueDate: '',\n    subtotal: 0,\n    tax: 0,\n    total: 0\n  });\n\n  const router = useRouter();\n\n  useEffect(() => {\n    loadData();\n    // Load saved template from localStorage\n    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');\n    if (savedTemplate) {\n      setSelectedTemplate(savedTemplate);\n    }\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [customersData, productsData] = await Promise.all([\n        getCustomers(),\n        getProducts()\n      ]);\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (err) {\n      setError('فشل في تحميل البيانات');\n      console.error('Error loading data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCustomerChange = (customerId) => {\n    const customer = customers.find(c => c.id === parseInt(customerId));\n    setInvoiceData(prev => ({\n      ...prev,\n      customerId,\n      customerName: customer?.name || '',\n      customerDetails: customer?.companyName || customer?.email || ''\n    }));\n  };\n\n  const addItem = () => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        id: Date.now(),\n        productId: '',\n        description: '',\n        quantity: 1,\n        price: 0,\n        total: 0\n      }]\n    }));\n  };\n\n  const updateItem = (itemId, field, value) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.map(item => {\n        if (item.id === itemId) {\n          const updatedItem = { ...item, [field]: value };\n\n          // Auto-fill from product if productId is selected\n          if (field === 'productId' && value) {\n            const product = products.find(p => p.id === parseInt(value));\n            if (product) {\n              updatedItem.description = product.name;\n              updatedItem.price = Number(product.price) || 0;\n            }\n          }\n\n          // Ensure numeric values\n          if (field === 'quantity') {\n            updatedItem.quantity = Number(value) || 0;\n          }\n          if (field === 'price') {\n            updatedItem.price = Number(value) || 0;\n          }\n\n          // Calculate total\n          updatedItem.total = Number(updatedItem.quantity || 0) * Number(updatedItem.price || 0);\n\n          return updatedItem;\n        }\n        return item;\n      })\n    }));\n  };\n\n  const removeItem = (itemId) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n  };\n\n  const calculateTotals = () => {\n    const subtotal = invoiceData.items.reduce((sum, item) => sum + (Number(item.total) || 0), 0);\n    const tax = subtotal * 0.15; // 15% VAT\n    const total = subtotal + tax;\n\n    setInvoiceData(prev => ({\n      ...prev,\n      subtotal: Number(subtotal) || 0,\n      tax: Number(tax) || 0,\n      total: Number(total) || 0\n    }));\n  };\n\n  useEffect(() => {\n    calculateTotals();\n  }, [invoiceData.items]);\n\n  const nextStep = () => {\n    if (step < 2) {\n      setStep(step + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (step > 1) {\n      setStep(step - 1);\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      // Here you would call the API to create the invoice\n      // await createInvoice({ ...invoiceData, templateId: selectedTemplate });\n\n      // For now, just simulate success\n      setTimeout(() => {\n        router.push('/dashboard/invoices');\n      }, 1000);\n    } catch (err) {\n      setError('فشل في إنشاء الفاتورة');\n      console.error('Error creating invoice:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">تفاصيل الفاتورة</h2>\n              <p className=\"text-gray-600\">أدخل بيانات الفاتورة والعناصر</p>\n            </div>\n\n            {/* Customer Selection */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات العميل</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    اختر العميل\n                  </label>\n                  <select\n                    value={invoiceData.customerId}\n                    onChange={(e) => handleCustomerChange(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required\n                  >\n                    <option value=\"\">اختر عميل...</option>\n                    {customers.map(customer => (\n                      <option key={customer.id} value={customer.id}>\n                        {customer.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    تاريخ الاستحقاق\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={invoiceData.dueDate}\n                    onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Items */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">عناصر الفاتورة</h3>\n                <button\n                  onClick={addItem}\n                  className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\"\n                >\n                  إضافة عنصر\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {invoiceData.items.map((item, index) => (\n                  <div key={item.id} className=\"grid grid-cols-12 gap-4 items-end\">\n                    <div className=\"col-span-4\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        المنتج\n                      </label>\n                      <select\n                        value={item.productId}\n                        onChange={(e) => updateItem(item.id, 'productId', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"\">اختر منتج...</option>\n                        {products.map(product => (\n                          <option key={product.id} value={product.id}>\n                            {product.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        الكمية\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={item.quantity}\n                        onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        min=\"1\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        السعر\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={item.price}\n                        onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        step=\"0.01\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        الإجمالي\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={(Number(item.total) || 0).toFixed(2)}\n                        readOnly\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <button\n                        onClick={() => removeItem(item.id)}\n                        className=\"w-full bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors\"\n                      >\n                        حذف\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {invoiceData.items.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  لا توجد عناصر. اضغط \"إضافة عنصر\" لبدء إضافة المنتجات.\n                </div>\n              )}\n            </div>\n\n            {/* Notes */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملاحظات</h3>\n              <textarea\n                value={invoiceData.notes}\n                onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"ملاحظات إضافية...\"\n              />\n            </div>\n\n            {/* Totals Summary */}\n            {invoiceData.items.length > 0 && (\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملخص المبالغ</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{(Number(invoiceData.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>ضريبة القيمة المضافة (15%):</span>\n                    <span>{(Number(invoiceData.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between font-bold text-lg border-t pt-2\">\n                    <span>الإجمالي:</span>\n                    <span>{(Number(invoiceData.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">معاينة الفاتورة</h2>\n              <p className=\"text-gray-600\">راجع الفاتورة قبل الحفظ</p>\n            </div>\n\n            <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n              <div className=\"transform scale-75 origin-top\">\n                <InvoiceRenderer\n                  invoice={{\n                    invoiceNumber: 'INV-' + Date.now(),\n                    date: new Date().toLocaleDateString('ar-SA'),\n                    customerName: invoiceData.customerName,\n                    customerDetails: invoiceData.customerDetails,\n                    items: invoiceData.items,\n                    subtotal: invoiceData.subtotal,\n                    tax: invoiceData.tax,\n                    total: invoiceData.total,\n                    notes: invoiceData.notes || 'شكراً لكم...',\n                    companyName: 'شركتك',\n                    companyDetails: 'تفاصيل شركتك'\n                  }}\n                  templateId={selectedTemplate}\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <nav className=\"flex\" aria-label=\"Breadcrumb\">\n                  <ol className=\"flex items-center space-x-4\">\n                    <li>\n                      <Link href=\"/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                        <svg className=\"flex-shrink-0 h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n                        </svg>\n                      </Link>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <Link href=\"/dashboard/invoices\" className=\"ml-4 text-sm font-medium text-gray-500 hover:text-gray-700\">\n                          الفواتير\n                        </Link>\n                      </div>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <span className=\"ml-4 text-sm font-medium text-gray-900\">إنشاء فاتورة جديدة</span>\n                      </div>\n                    </li>\n                  </ol>\n                </nav>\n                <h1 className=\"mt-2 text-3xl font-bold text-gray-900\">إنشاء فاتورة جديدة</h1>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Current Template Info */}\n          <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n                </svg>\n                <span className=\"text-blue-800 font-medium\">\n                  القالب المحدد: <span className=\"font-bold\">{templates[selectedTemplate]?.name || 'القالب الكلاسيكي'}</span>\n                </span>\n              </div>\n              <Link\n                href=\"/dashboard/invoice-templates\"\n                className=\"text-blue-600 hover:text-blue-800 text-sm font-medium underline\"\n              >\n                تغيير القالب\n              </Link>\n            </div>\n          </div>\n\n          {/* Progress Steps */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-center\">\n              {[1, 2].map((stepNumber) => (\n                <div key={stepNumber} className=\"flex items-center\">\n                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${\n                    step >= stepNumber\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {stepNumber}\n                  </div>\n                  <div className={`ml-4 ${step >= stepNumber ? 'text-blue-600' : 'text-gray-500'}`}>\n                    {stepNumber === 1 && 'تفاصيل الفاتورة'}\n                    {stepNumber === 2 && 'المعاينة'}\n                  </div>\n                  {stepNumber < 2 && (\n                    <div className={`w-16 h-1 mx-4 ${\n                      step > stepNumber ? 'bg-blue-500' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\n              <p className=\"text-red-800\">{error}</p>\n            </div>\n          )}\n\n          {/* Step Content */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n            {renderStepContent()}\n          </div>\n\n          {/* Navigation Buttons */}\n          <div className=\"flex justify-between\">\n            <div>\n              {step > 1 && (\n                <button\n                  onClick={prevStep}\n                  className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  السابق\n                </button>\n              )}\n            </div>\n\n            <div className=\"flex gap-4\">\n              <Link\n                href=\"/dashboard/invoices\"\n                className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                إلغاء\n              </Link>\n\n              {step < 2 ? (\n                <button\n                  onClick={nextStep}\n                  disabled={step === 1 && (invoiceData.items.length === 0 || !invoiceData.customerId)}\n                  className=\"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                >\n                  التالي\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={loading}\n                  className=\"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                >\n                  {loading ? 'جاري الحفظ...' : 'حفظ الفاتورة'}\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(CreateInvoicePage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,SAAS;;IACP,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,yBAAyB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,OAAO,EAAE;QACT,OAAO;QACP,SAAS;QACT,UAAU;QACV,KAAK;QACL,OAAO;IACT;IAEA,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;YACA,wCAAwC;YACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,oBAAoB;YACtB;QACF;sCAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,MAAM,CAAC,eAAe,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,CAAA,GAAA,6GAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD;aACX;YACD,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;QACvD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;gBACA,cAAc,UAAU,QAAQ;gBAChC,iBAAiB,UAAU,eAAe,UAAU,SAAS;YAC/D,CAAC;IACH;IAEA,MAAM,UAAU;QACd,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO;uBAAI,KAAK,KAAK;oBAAE;wBACrB,IAAI,KAAK,GAAG;wBACZ,WAAW;wBACX,aAAa;wBACb,UAAU;wBACV,OAAO;wBACP,OAAO;oBACT;iBAAE;YACJ,CAAC;IACH;IAEA,MAAM,aAAa,CAAC,QAAQ,OAAO;QACjC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA;oBACpB,IAAI,KAAK,EAAE,KAAK,QAAQ;wBACtB,MAAM,cAAc;4BAAE,GAAG,IAAI;4BAAE,CAAC,MAAM,EAAE;wBAAM;wBAE9C,kDAAkD;wBAClD,IAAI,UAAU,eAAe,OAAO;4BAClC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BACrD,IAAI,SAAS;gCACX,YAAY,WAAW,GAAG,QAAQ,IAAI;gCACtC,YAAY,KAAK,GAAG,OAAO,QAAQ,KAAK,KAAK;4BAC/C;wBACF;wBAEA,wBAAwB;wBACxB,IAAI,UAAU,YAAY;4BACxB,YAAY,QAAQ,GAAG,OAAO,UAAU;wBAC1C;wBACA,IAAI,UAAU,SAAS;4BACrB,YAAY,KAAK,GAAG,OAAO,UAAU;wBACvC;wBAEA,kBAAkB;wBAClB,YAAY,KAAK,GAAG,OAAO,YAAY,QAAQ,IAAI,KAAK,OAAO,YAAY,KAAK,IAAI;wBAEpF,OAAO;oBACT;oBACA,OAAO;gBACT;YACF,CAAC;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC/C,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW,YAAY,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG;QAC1F,MAAM,MAAM,WAAW,MAAM,UAAU;QACvC,MAAM,QAAQ,WAAW;QAEzB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,UAAU,OAAO,aAAa;gBAC9B,KAAK,OAAO,QAAQ;gBACpB,OAAO,OAAO,UAAU;YAC1B,CAAC;IACH;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,WAAW;QACf,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,WAAW;QACf,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,oDAAoD;YACpD,yEAAyE;YAEzE,iCAAiC;YACjC,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAI/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,YAAY,UAAU;oDAC7B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,WAAU;oDACV,QAAQ;;sEAER,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC;gEAAyB,OAAO,SAAS,EAAE;0EACzC,SAAS,IAAI;+DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;sDAO9B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,OAAO;oDAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,OAAO,KAAK,SAAS;4DACrB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAChE,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;wEAAwB,OAAO,QAAQ,EAAE;kFACvC,QAAQ,IAAI;uEADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;8DAO7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,KAAK,QAAQ;4DACpB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC7E,WAAU;4DACV,KAAI;;;;;;;;;;;;8DAIR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,KAAK,KAAK;4DACjB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC5E,WAAU;4DACV,MAAK;;;;;;;;;;;;8DAIT,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;4DACzC,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS,IAAM,WAAW,KAAK,EAAE;wDACjC,WAAU;kEACX;;;;;;;;;;;;2CA7DK,KAAK,EAAE;;;;;;;;;;gCAqEpB,YAAY,KAAK,CAAC,MAAM,KAAK,mBAC5B,6LAAC;oCAAI,WAAU;8CAAiC;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCACC,OAAO,YAAY,KAAK;oCACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC3E,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,YAAY,KAAK,CAAC,MAAM,GAAG,mBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,CAAC,OAAO,YAAY,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,CAAC,OAAO,YAAY,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,CAAC,OAAO,YAAY,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQjE,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wKAAA,CAAA,UAAe;oCACd,SAAS;wCACP,eAAe,SAAS,KAAK,GAAG;wCAChC,MAAM,IAAI,OAAO,kBAAkB,CAAC;wCACpC,cAAc,YAAY,YAAY;wCACtC,iBAAiB,YAAY,eAAe;wCAC5C,OAAO,YAAY,KAAK;wCACxB,UAAU,YAAY,QAAQ;wCAC9B,KAAK,YAAY,GAAG;wCACpB,OAAO,YAAY,KAAK;wCACxB,OAAO,YAAY,KAAK,IAAI;wCAC5B,aAAa;wCACb,gBAAgB;oCAClB;oCACA,YAAY;;;;;;;;;;;;;;;;;;;;;;YAOxB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;wCAAO,cAAW;kDAC/B,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAChC,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8DAId,6LAAC;8DACC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAsB,WAAU;0EAA6D;;;;;;;;;;;;;;;;;8DAK5G,6LAAC;8DACC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,6LAAC;gEAAK,WAAU;0EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjE,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAK,WAAU;;oDAA4B;kEAC3B,6LAAC;wDAAK,WAAU;kEAAa,wJAAA,CAAA,YAAS,CAAC,iBAAiB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;kDAGrF,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,2BACX,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAI,WAAW,CAAC,sEAAsE,EACrF,QAAQ,aACJ,2BACA,6BACJ;0DACC;;;;;;0DAEH,6LAAC;gDAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,aAAa,kBAAkB,iBAAiB;;oDAC7E,eAAe,KAAK;oDACpB,eAAe,KAAK;;;;;;;4CAEtB,aAAa,mBACZ,6LAAC;gDAAI,WAAW,CAAC,cAAc,EAC7B,OAAO,aAAa,gBAAgB,eACpC;;;;;;;uCAfI;;;;;;;;;;;;;;;wBAuBf,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CACE,OAAO,mBACN,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;8CAML,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;wCAIA,OAAO,kBACN,6LAAC;4CACC,SAAS;4CACT,UAAU,SAAS,KAAK,CAAC,YAAY,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,UAAU;4CAClF,WAAU;sDACX;;;;;iEAID,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GAngBS;;QAoBQ,qIAAA,CAAA,YAAS;;;KApBjB;6CAqgBM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}