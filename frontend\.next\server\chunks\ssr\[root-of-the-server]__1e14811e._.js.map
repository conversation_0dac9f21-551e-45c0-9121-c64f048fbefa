{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/AuthBackground.js"], "sourcesContent": ["// frontend/components/auth/AuthBackground.js\n'use client';\n\nexport default function AuthBackground({ children, variant = 'default' }) {\n  const variants = {\n    default: 'from-indigo-50 via-white to-cyan-50',\n    register: 'from-purple-50 via-white to-pink-50',\n    login: 'from-blue-50 via-white to-indigo-50'\n  };\n\n  return (\n    <div className={`min-h-screen bg-gradient-to-br ${variants[variant]} flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden`}>\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n        \n        {/* Floating Elements */}\n        <div className=\"absolute top-10 left-10 w-20 h-20 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full opacity-10 animate-float\"></div>\n        <div className=\"absolute top-32 right-20 w-16 h-16 bg-gradient-to-r from-pink-400 to-red-400 rounded-full opacity-10 animate-float-delayed\"></div>\n        <div className=\"absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-10 animate-float\"></div>\n        <div className=\"absolute bottom-32 right-10 w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-10 animate-float-delayed\"></div>\n        \n        {/* Gradient Overlays */}\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/20 to-transparent\"></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n\n      {/* Custom Styles */}\n      <style jsx>{`\n        .bg-grid-pattern {\n          background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);\n          background-size: 20px 20px;\n        }\n        \n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-20px) rotate(180deg); }\n        }\n        \n        @keyframes float-delayed {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-15px) rotate(-180deg); }\n        }\n        \n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        .animate-float-delayed {\n          animation: float-delayed 8s ease-in-out infinite;\n        }\n        \n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        \n        .animate-fadeIn {\n          animation: fadeIn 0.3s ease-out;\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;AAC7C;;;AAEe,SAAS,eAAe,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE;IACtE,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,OAAO;IACT;IAEA,qBACE,8OAAC;kDAAe,CAAC,+BAA+B,EAAE,QAAQ,CAAC,QAAQ,CAAC,4EAA4E,CAAC;;0BAE/I,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;;;;;;kCAGf,8OAAC;kEAAc;;;;;;kCACf,8OAAC;kEAAc;;;;;;kCACf,8OAAC;kEAAc;;;;;;kCACf,8OAAC;kEAAc;;;;;;kCAGf,8OAAC;kEAAc;;;;;;;;;;;;0BAIjB,8OAAC;0DAAc;0BACZ;;;;;;;;;;;;;;;;AAuCT", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/AuthHeader.js"], "sourcesContent": ["// frontend/components/auth/AuthHeader.js\n'use client';\n\nexport default function AuthHeader({ title, subtitle, showLogo = true }) {\n  return (\n    <div className=\"sm:mx-auto sm:w-full sm:max-w-md relative z-10\">\n      {showLogo && (\n        <div className=\"flex justify-center\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200\">\n            <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n          </div>\n        </div>\n      )}\n      \n      <h2 className=\"mt-6 text-center text-3xl font-bold text-gray-900\">\n        {title}\n      </h2>\n      \n      {subtitle && (\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          {subtitle}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AACzC;;AAEe,SAAS,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAE;IACrE,qBACE,8OAAC;QAAI,WAAU;;YACZ,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAqB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC5E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAGF,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/InputField.js"], "sourcesContent": ["// frontend/components/auth/InputField.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function InputField({ \n  label, \n  type = 'text', \n  name, \n  value, \n  onChange, \n  placeholder, \n  required = false,\n  icon,\n  showPasswordToggle = false,\n  error = null\n}) {\n  const [showPassword, setShowPassword] = useState(false);\n\n  const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type;\n\n  return (\n    <div>\n      <label htmlFor={name} className=\"block text-sm font-semibold text-gray-700 mb-2\">\n        {label}\n        {required && <span className=\"text-red-500 mr-1\">*</span>}\n      </label>\n      <div className=\"relative\">\n        <input\n          type={inputType}\n          id={name}\n          name={name}\n          value={value}\n          onChange={onChange}\n          className={`w-full px-4 py-3 ${icon ? 'pl-12' : ''} ${showPasswordToggle ? 'pr-12' : ''} border-2 rounded-xl focus:outline-none transition-all duration-200 text-gray-900 placeholder-gray-400 ${\n            error \n              ? 'border-red-300 focus:border-red-500 focus:ring-4 focus:ring-red-100' \n              : 'border-gray-200 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100'\n          }`}\n          placeholder={placeholder}\n          required={required}\n        />\n        \n        {/* Icon */}\n        {icon && (\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <span className=\"h-5 w-5 text-gray-400\">\n              {icon}\n            </span>\n          </div>\n        )}\n\n        {/* Password Toggle */}\n        {showPasswordToggle && (\n          <button\n            type=\"button\"\n            onClick={() => setShowPassword(!showPassword)}\n            className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n          >\n            {showPassword ? (\n              <svg className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n              </svg>\n            ) : (\n              <svg className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n            )}\n          </button>\n        )}\n      </div>\n      \n      {/* Error Message */}\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600 flex items-center\">\n          <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          {error}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AAFA;;;AAIe,SAAS,WAAW,EACjC,KAAK,EACL,OAAO,MAAM,EACb,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,KAAK,EAChB,IAAI,EACJ,qBAAqB,KAAK,EAC1B,QAAQ,IAAI,EACb;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,YAAY,qBAAsB,eAAe,SAAS,aAAc;IAE9E,qBACE,8OAAC;;0BACC,8OAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAEnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,WAAW,CAAC,iBAAiB,EAAE,OAAO,UAAU,GAAG,CAAC,EAAE,qBAAqB,UAAU,GAAG,uGAAuG,EAC7L,QACI,wEACA,8EACJ;wBACF,aAAa;wBACb,UAAU;;;;;;oBAIX,sBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;oBAMN,oCACC,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAU;kCAET,6BACC,8OAAC;4BAAI,WAAU;4BAA4C,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnG,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;iDAGvE,8OAAC;4BAAI,WAAU;4BAA4C,MAAK;4BAAO,QAAO;4BAAe,SAAQ;;8CACnG,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAE;;;;;;8CACrE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;oBAEtE;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/ErrorMessage.js"], "sourcesContent": ["// frontend/components/auth/ErrorMessage.js\n'use client';\n\nexport default function ErrorMessage({ message, type = 'error' }) {\n  if (!message) return null;\n\n  const styles = {\n    error: {\n      bg: 'bg-red-50',\n      border: 'border-red-400',\n      text: 'text-red-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    success: {\n      bg: 'bg-green-50',\n      border: 'border-green-400',\n      text: 'text-green-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    warning: {\n      bg: 'bg-yellow-50',\n      border: 'border-yellow-400',\n      text: 'text-yellow-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    info: {\n      bg: 'bg-blue-50',\n      border: 'border-blue-400',\n      text: 'text-blue-700',\n      icon: (\n        <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    }\n  };\n\n  const currentStyle = styles[type];\n\n  return (\n    <div className={`${currentStyle.bg} border-l-4 ${currentStyle.border} p-4 rounded-r-lg animate-fadeIn`}>\n      <div className=\"flex\">\n        <div className=\"flex-shrink-0\">\n          {currentStyle.icon}\n        </div>\n        <div className=\"ml-3\">\n          <p className={`text-sm ${currentStyle.text} font-medium`}>\n            {message}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAC3C;;AAEe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,OAAO,EAAE;IAC9D,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,SAAS;QACb,OAAO;YACL,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAuB,SAAQ;gBAAY,MAAK;0BAC7D,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAA0N,UAAS;;;;;;;;;;;QAGpQ;QACA,SAAS;YACP,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAyB,SAAQ;gBAAY,MAAK;0BAC/D,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAwI,UAAS;;;;;;;;;;;QAGlL;QACA,SAAS;YACP,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAA0B,SAAQ;gBAAY,MAAK;0BAChE,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAoN,UAAS;;;;;;;;;;;QAG9P;QACA,MAAM;YACJ,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,SAAQ;gBAAY,MAAK;0BAC9D,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAmI,UAAS;;;;;;;;;;;QAG7K;IACF;IAEA,MAAM,eAAe,MAAM,CAAC,KAAK;IAEjC,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,EAAE,CAAC,YAAY,EAAE,aAAa,MAAM,CAAC,gCAAgC,CAAC;kBACpG,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,aAAa,IAAI;;;;;;8BAEpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,IAAI,CAAC,YAAY,CAAC;kCACrD;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/auth/SubmitButton.js"], "sourcesContent": ["// frontend/components/auth/SubmitButton.js\n'use client';\n\nexport default function SubmitButton({ \n  loading = false, \n  children, \n  loadingText = 'جاري المعالجة...', \n  disabled = false,\n  variant = 'primary',\n  icon = null,\n  onClick = null,\n  type = 'submit'\n}) {\n  const variants = {\n    primary: 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white',\n    success: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white',\n    danger: 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={loading || disabled}\n      className={`w-full flex justify-center items-center px-6 py-3 border border-transparent text-base font-semibold rounded-xl focus:outline-none focus:ring-4 focus:ring-indigo-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${variants[variant]}`}\n    >\n      {loading ? (\n        <>\n          <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n          </svg>\n          {loadingText}\n        </>\n      ) : (\n        <>\n          {icon && <span className=\"mr-2\">{icon}</span>}\n          {children}\n        </>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAC3C;;AAEe,SAAS,aAAa,EACnC,UAAU,KAAK,EACf,QAAQ,EACR,cAAc,kBAAkB,EAChC,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,IAAI,EACd,OAAO,QAAQ,EAChB;IACC,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,WAAW;QACrB,WAAW,CAAC,4SAA4S,EAAE,QAAQ,CAAC,QAAQ,EAAE;kBAE5U,wBACC;;8BACE,8OAAC;oBAAI,WAAU;oBAA6C,OAAM;oBAA6B,MAAK;oBAAO,SAAQ;;sCACjH,8OAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,8OAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAEpD;;yCAGH;;gBACG,sBAAQ,8OAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/login/page.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { login } from '@/lib/api';\r\nimport AuthBackground from '@/components/auth/AuthBackground';\r\nimport AuthHeader from '@/components/auth/AuthHeader';\r\nimport InputField from '@/components/auth/InputField';\r\nimport ErrorMessage from '@/components/auth/ErrorMessage';\r\nimport SubmitButton from '@/components/auth/SubmitButton';\r\n\r\nexport default function LoginPage() {\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    rememberMe: false\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const router = useRouter();\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      [name]: type === 'checkbox' ? checked : value,\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const user = await login(formData);\r\n      localStorage.setItem('user', JSON.stringify(user));\r\n      router.push('/dashboard');\r\n    } catch (err) {\r\n      setError(err.message || 'فشل في تسجيل الدخول. يرجى التحقق من البيانات.');\r\n      console.error('Login error:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AuthBackground variant=\"login\">\r\n      <AuthHeader\r\n        title=\"مرحباً بك مرة أخرى\"\r\n        subtitle=\"سجل دخولك للوصول إلى نظام إدارة الفواتير\"\r\n      />\r\n\r\n      {/* Form */}\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md relative z-10\">\r\n        <div className=\"bg-white py-8 px-4 shadow-2xl sm:rounded-2xl sm:px-10 border border-gray-100\">\r\n          {error && <div className=\"mb-6\"><ErrorMessage message={error} type=\"error\" /></div>}\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* Email Field */}\r\n            <InputField\r\n              label=\"البريد الإلكتروني\"\r\n              type=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              placeholder=\"أدخل بريدك الإلكتروني\"\r\n              required\r\n              icon={\r\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                </svg>\r\n              }\r\n            />\r\n\r\n            {/* Password Field */}\r\n            <InputField\r\n              label=\"كلمة المرور\"\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={formData.password}\r\n              onChange={handleChange}\r\n              placeholder=\"أدخل كلمة المرور\"\r\n              required\r\n              showPasswordToggle\r\n              icon={\r\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\r\n                </svg>\r\n              }\r\n            />\r\n\r\n            {/* Remember Me & Forgot Password */}\r\n            <div className=\"flex items-center justify-between\">\r\n              <label className=\"flex items-center\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"rememberMe\"\r\n                  checked={formData.rememberMe}\r\n                  onChange={handleChange}\r\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\r\n                />\r\n                <span className=\"ml-2 text-sm text-gray-700\">تذكرني</span>\r\n              </label>\r\n              <Link\r\n                href=\"/forgot-password\"\r\n                className=\"text-sm text-indigo-600 hover:text-indigo-800 font-medium transition-colors\"\r\n              >\r\n                نسيت كلمة المرور؟\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Submit Button */}\r\n            <SubmitButton\r\n              loading={loading}\r\n              loadingText=\"جاري تسجيل الدخول...\"\r\n              icon={\r\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\r\n                </svg>\r\n              }\r\n            >\r\n              تسجيل الدخول\r\n            </SubmitButton>\r\n          </form>\r\n\r\n          {/* Demo Credentials */}\r\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200\">\r\n            <h4 className=\"text-sm font-semibold text-blue-800 mb-2 flex items-center\">\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n              </svg>\r\n              بيانات تجريبية للاختبار:\r\n            </h4>\r\n            <div className=\"text-xs text-blue-700 space-y-1\">\r\n              <p><strong>البريد:</strong> <EMAIL></p>\r\n              <p><strong>كلمة المرور:</strong> password123</p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sign Up Link */}\r\n          <div className=\"mt-6\">\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <div className=\"w-full border-t border-gray-300\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-sm\">\r\n                <span className=\"px-2 bg-white text-gray-500\">أو</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-6 text-center\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                ليس لديك حساب؟{' '}\r\n                <Link\r\n                  href=\"/register\"\r\n                  className=\"font-semibold text-indigo-600 hover:text-indigo-800 transition-colors\"\r\n                >\r\n                  إنشاء حساب جديد\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Back to Home */}\r\n          <div className=\"mt-4 text-center\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\r\n              </svg>\r\n              العودة للصفحة الرئيسية\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </AuthBackground>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAC,WAAa,CAAC;gBACzB,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,0GAAA,CAAA,QAAK,AAAD,EAAE;YACzB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;YACxB,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,oIAAA,CAAA,UAAc;QAAC,SAAQ;;0BACtB,8OAAC,gIAAA,CAAA,UAAU;gBACT,OAAM;gBACN,UAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,uBAAS,8OAAC;4BAAI,WAAU;sCAAO,cAAA,8OAAC,kIAAA,CAAA,UAAY;gCAAC,SAAS;gCAAO,MAAK;;;;;;;;;;;sCAEnE,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC,gIAAA,CAAA,UAAU;oCACT,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,oBACE,8OAAC;wCAAI,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7C,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAM3E,8OAAC,gIAAA,CAAA,UAAU;oCACT,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,kBAAkB;oCAClB,oBACE,8OAAC;wCAAI,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7C,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAM3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,SAAS,SAAS,UAAU;oDAC5B,UAAU;oDACV,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC,kIAAA,CAAA,UAAY;oCACX,SAAS;oCACT,aAAY;oCACZ,oBACE,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;8CAG1E;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAgB;;;;;;;sDAC3B,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAqB;;;;;;;;;;;;;;;;;;;sCAKpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAwB;4CACpB;0DACf,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}