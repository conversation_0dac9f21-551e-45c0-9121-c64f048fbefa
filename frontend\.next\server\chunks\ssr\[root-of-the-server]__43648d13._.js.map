{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العرض التوضيحي',\n      href: '/dashboard/demo',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAJA;;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,8OAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,8OAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,8OAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,8OAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ClassicTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // بيانات العينة للمعاينة فقط\n  const sampleData = preview ? {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة',\n    companyPhone: '+966 50 123 4567',\n    companyEmail: '<EMAIL>',\n    companyWebsite: 'www.company.com',\n    taxId: '*********'\n  } : invoice;\n\n  const data = sampleData;\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header with Teal Design */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              <p className=\"text-teal-100\">{data.companyDetails}</p>\n            </div>\n\n            {/* Logo Area */}\n            <div className=\"bg-white p-4 rounded-lg shadow-md\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                <div className=\"text-white font-bold text-xl\">شعار</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">رقم الفاتورة:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">اسم العميل:</span>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"font-semibold\">التاريخ:</span>\n          </div>\n\n          <div>{data.invoiceNumber}</div>\n          <div>{data.customerName}</div>\n          <div>{data.date}</div>\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"bg-yellow-100\">\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">البيان</th>\n              <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n                <td className=\"border border-gray-300 p-3\">-</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Totals */}\n      <div className=\"flex justify-between items-end mb-8\">\n        <div className=\"text-right\">\n          <p className=\"text-lg font-semibold mb-4\">{data.notes}</p>\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">السعر</span>\n              <span>{(Number(data.subtotal) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"font-semibold\">الضريبة</span>\n              <span>{(Number(data.tax) || 0).toFixed(2)}</span>\n            </div>\n            <div className=\"border-t border-gray-300 pt-2\">\n              <div className=\"flex justify-between font-bold text-lg\">\n                <span>الإجمالي</span>\n                <span>{(Number(data.total) || 0).toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"flex justify-between items-end\">\n        <div>\n          <h4 className=\"font-semibold mb-2\">ملاحظات</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• البضاعة المباعة لا ترد ولا تستبدل</li>\n            <li>• التأكد من استلام جميع بنود الفاتورة</li>\n          </ul>\n        </div>\n\n        <div className=\"text-center\">\n          <h4 className=\"font-semibold mb-4\">توقيع البائع</h4>\n          <div className=\"w-32 border-b-2 border-gray-400 mb-2\"></div>\n        </div>\n      </div>\n\n      {/* Contact Info */}\n      <div className=\"mt-8 pt-4 border-t border-gray-300\">\n        <div className=\"flex justify-between text-sm text-gray-600\">\n          <div>\n            <p>+123-456-7890</p>\n            <p>www.reallygreatsite.com</p>\n            <p><EMAIL></p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"w-32 h-8 bg-gray-800 flex items-center justify-center\">\n              <div className=\"text-white text-xs\">|||||||||||||||||||||||</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IACxF,6BAA6B;IAC7B,MAAM,aAAa,UAAU;QAC3B,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;IACT,IAAI;IAEJ,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAiB,KAAK,cAAc;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAGlC,8OAAC;0CAAK,KAAK,aAAa;;;;;;0CACxB,8OAAC;0CAAK,KAAK,YAAY;;;;;;0CACvB,8OAAC;0CAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAGxE,8OAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;0DACzF,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;0DAC3E,8OAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCALxD,KAAK,EAAE;;;;;gCAUjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCALpC,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAa7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA8B,KAAK,KAAK;;;;;;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;sDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;sDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;sCAEL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}, {"offset": {"line": 1524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ModernTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ModernTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ModernTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة التقنية المتطورة',\n    customerDetails: 'الحلول التقنية المتكاملة',\n    items: [\n      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },\n      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },\n      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }\n    ],\n    subtotal: 8500.00,\n    tax: 1275.00,\n    total: 9775.00,\n    notes: 'شكراً لثقتكم بنا',\n    companyName: 'شركة التقنية المتطورة',\n    companyDetails: 'الحلول التقنية المتكاملة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Modern Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 text-white\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold mb-2\">{data.companyName}</h1>\n            <p className=\"text-blue-100 text-lg\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4\">\n              <h2 className=\"text-2xl font-bold mb-2\">فاتورة</h2>\n              <p className=\"text-blue-100\">#{data.invoiceNumber}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-8\">\n        {/* Customer Info Card */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8\">\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات العميل</h3>\n              <div className=\"space-y-2\">\n                <p className=\"text-gray-700 font-medium\">{data.customerName}</p>\n                <p className=\"text-gray-600\">{data.customerDetails}</p>\n              </div>\n            </div>\n\n            <div className=\"text-right\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">تفاصيل الفاتورة</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">التاريخ:</span>\n                  <span className=\"font-medium\">{data.date}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                  <span className=\"font-medium\">{data.invoiceNumber}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Items Table */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">تفاصيل الخدمات</h3>\n\n          <div className=\"overflow-hidden rounded-xl border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white\">\n                <tr>\n                  <th className=\"p-4 text-right font-semibold\">الإجمالي</th>\n                  <th className=\"p-4 text-right font-semibold\">السعر</th>\n                  <th className=\"p-4 text-right font-semibold\">الكمية</th>\n                  <th className=\"p-4 text-right font-semibold\">الوصف</th>\n                  <th className=\"p-4 text-right font-semibold\">#</th>\n                </tr>\n              </thead>\n              <tbody>\n                {data.items.map((item, index) => (\n                  <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>\n                    <td className=\"p-4 text-right font-semibold text-blue-600\">\n                      {(Number(item.total) || 0).toFixed(2)} ر.س\n                    </td>\n                    <td className=\"p-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm\">\n                        {item.quantity || 0}\n                      </span>\n                    </td>\n                    <td className=\"p-4 text-right font-medium\">{item.description || ''}</td>\n                    <td className=\"p-4 text-right\">\n                      <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm font-medium\">\n                        {index + 1}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Modern Totals */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div className=\"w-1/2\">\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6\">\n              <h4 className=\"text-lg font-semibold text-gray-800 mb-3\">ملاحظات</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h5 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h5>\n                <ul className=\"text-sm text-gray-600 space-y-1\">\n                  <li>• الدفع خلال 30 يوم من تاريخ الفاتورة</li>\n                  <li>• جميع الأسعار شاملة ضريبة القيمة المضافة</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"w-80\">\n            <div className=\"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white\">\n              <h4 className=\"text-lg font-semibold mb-4\">ملخص الفاتورة</h4>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"border-t border-white border-opacity-30 pt-3\">\n                  <div className=\"flex justify-between text-xl font-bold\">\n                    <span>الإجمالي النهائي:</span>\n                    <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Modern Footer */}\n        <div className=\"bg-gray-50 rounded-xl p-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">معلومات التواصل</h4>\n              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                <span>📞 +966 50 123 4567</span>\n                <span>📧 <EMAIL></span>\n                <span>🌐 www.company.com</span>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-32 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold\">\n                شعار الشركة\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;AAFA;;;AAIe,SAAS,eAAe,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IACjE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAyB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAgB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;SACnF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAEpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2B,KAAK,WAAW;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAyB,KAAK,cAAc;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;;4CAAgB;4CAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA6B,KAAK,YAAY;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,KAAK,IAAI;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGjD,8OAAC;sDACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;oDAAiB,WAAW,QAAQ,MAAM,IAAI,eAAe;;sEAC5D,8OAAC;4DAAG,WAAU;;gEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,8OAAC;4DAAG,WAAU;;gEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACrE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;sEAGtB,8OAAC;4DAAG,WAAU;sEAA8B,KAAK,WAAW,IAAI;;;;;;sEAChE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;;;;;;;mDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwB1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAE5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvJ", "debugId": null}}, {"offset": {"line": 2246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/ElegantTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/ElegantTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function ElegantTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'مؤسسة الأناقة التجارية',\n    customerDetails: 'للتجارة والمقاولات',\n    items: [\n      { id: 1, description: 'أثاث مكتبي فاخر', quantity: 5, price: 800.00, total: 4000.00 },\n      { id: 2, description: 'ديكورات داخلية', quantity: 1, price: 2500.00, total: 2500.00 },\n      { id: 3, description: 'إكسسوارات مكتبية', quantity: 10, price: 150.00, total: 1500.00 }\n    ],\n    subtotal: 8000.00,\n    tax: 1200.00,\n    total: 9200.00,\n    notes: 'نتطلع لخدمتكم مرة أخرى',\n    companyName: 'مؤسسة الأناقة التجارية',\n    companyDetails: 'للتجارة والمقاولات'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Elegant Header */}\n      <div className=\"relative\">\n        <div className=\"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600\"></div>\n\n        <div className=\"p-8 pt-12\">\n          <div className=\"flex justify-between items-start mb-8\">\n            <div>\n              <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n              <p className=\"text-gray-600 text-lg italic\">{data.companyDetails}</p>\n\n              <div className=\"mt-6 w-24 h-1 bg-gradient-to-r from-amber-400 to-yellow-500\"></div>\n            </div>\n\n            <div className=\"text-right\">\n              <div className=\"border-2 border-amber-400 rounded-lg p-4 bg-amber-50\">\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-1\">فاتورة</h2>\n                <p className=\"text-amber-600 font-semibold\">#{data.invoiceNumber}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Customer Section */}\n          <div className=\"border border-gray-200 rounded-lg p-6 mb-8 bg-gray-50\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  فواتير إلى\n                </h3>\n                <div className=\"space-y-2\">\n                  <p className=\"text-gray-800 font-semibold text-lg\">{data.customerName}</p>\n                  <p className=\"text-gray-600\">{data.customerDetails}</p>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <h3 className=\"text-lg font-serif font-semibold text-gray-800 mb-3 border-b border-amber-300 pb-2\">\n                  تفاصيل الفاتورة\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                    <span className=\"font-semibold\">{data.date}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">رقم المرجع:</span>\n                    <span className=\"font-semibold\">{data.invoiceNumber}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Items Table */}\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-serif font-semibold text-gray-800 mb-4 border-b-2 border-amber-400 pb-2\">\n              بنود الفاتورة\n            </h3>\n\n            <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gradient-to-r from-gray-100 to-amber-50\">\n                  <tr>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">المبلغ</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">سعر الوحدة</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الكمية</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">الوصف</th>\n                    <th className=\"p-4 text-right font-serif font-semibold text-gray-800 border-b border-gray-200\">م</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {data.items.map((item, index) => (\n                    <tr key={item.id} className=\"border-b border-gray-100 hover:bg-amber-25\">\n                      <td className=\"p-4 text-right font-semibold text-amber-600\">\n                        {(Number(item.total) || 0).toFixed(2)} ر.س\n                      </td>\n                      <td className=\"p-4 text-right text-gray-700\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium\">\n                          {item.quantity || 0}\n                        </span>\n                      </td>\n                      <td className=\"p-4 text-right font-medium text-gray-800\">{item.description || ''}</td>\n                      <td className=\"p-4 text-right\">\n                        <span className=\"w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                          {index + 1}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Elegant Summary */}\n          <div className=\"flex justify-between items-start mb-8\">\n            <div className=\"w-1/2 pr-8\">\n              <div className=\"border-l-4 border-amber-400 pl-6\">\n                <h4 className=\"text-lg font-serif font-semibold text-gray-800 mb-3\">ملاحظات خاصة</h4>\n                <p className=\"text-gray-700 italic mb-4\">{data.notes}</p>\n\n                <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n                  <h5 className=\"font-semibold text-gray-800 mb-2\">شروط وأحكام:</h5>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• الدفع خلال 15 يوم من تاريخ الفاتورة</li>\n                    <li>• ضمان جودة لمدة سنة كاملة</li>\n                    <li>• خدمة ما بعد البيع متاحة</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"w-80\">\n              <div className=\"border-2 border-amber-300 rounded-lg bg-gradient-to-br from-amber-50 to-yellow-50\">\n                <div className=\"bg-gradient-to-r from-amber-400 to-yellow-500 text-white p-4 rounded-t-lg\">\n                  <h4 className=\"text-lg font-serif font-semibold\">الملخص المالي</h4>\n                </div>\n\n                <div className=\"p-6 space-y-4\">\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>المجموع الفرعي:</span>\n                    <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between text-gray-700\">\n                    <span>ضريبة القيمة المضافة:</span>\n                    <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"border-t-2 border-amber-300 pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold text-gray-800\">\n                      <span className=\"font-serif\">الإجمالي النهائي:</span>\n                      <span className=\"text-amber-600\">{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Elegant Footer */}\n          <div className=\"border-t-2 border-amber-300 pt-6\">\n            <div className=\"flex justify-between items-end\">\n              <div>\n                <h4 className=\"font-serif font-semibold text-gray-800 mb-3\">معلومات التواصل</h4>\n                <div className=\"space-y-1 text-sm text-gray-600\">\n                  <p>📱 الهاتف: +966 50 123 4567</p>\n                  <p>📧 البريد: <EMAIL></p>\n                  <p>🌐 الموقع: www.elegant-business.com</p>\n                  <p>📍 العنوان: الرياض، المملكة العربية السعودية</p>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <h5 className=\"font-serif font-semibold text-gray-800 mb-4\">توقيع مخول</h5>\n                <div className=\"w-32 h-16 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-amber-500 text-sm\">التوقيع</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAmB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAkB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACpF;gBAAE,IAAI;gBAAG,aAAa;gBAAoB,UAAU;gBAAI,OAAO;gBAAQ,OAAO;YAAQ;SACvF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAW,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoD,KAAK,WAAW;;;;;;sDAClF,8OAAC;4CAAE,WAAU;sDAAgC,KAAK,cAAc;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;;oDAA+B;oDAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuC,KAAK,YAAY;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kDAItD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqF;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,IAAI;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAIrG,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,WAAU;0DACf,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAC/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAGnG,8OAAC;0DACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wDAAiB,WAAU;;0EAC1B,8OAAC;gEAAG,WAAU;;oEACX,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EAExC,8OAAC;gEAAG,WAAU;;oEAAgC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACnF,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ,IAAI;;;;;;;;;;;0EAGtB,8OAAC;gEAAG,WAAU;0EAA4C,KAAK,WAAW,IAAI;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAU;8EACb,QAAQ;;;;;;;;;;;;uDAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAa;;;;;;8EAC7B,8OAAC;oEAAK,WAAU;;wEAAkB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D", "debugId": null}}, {"offset": {"line": 3024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/MinimalTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/MinimalTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function MinimalTemplate({ invoice, preview = false }) {\n  const sampleData = {\n    invoiceNumber: 'INV-001',\n    date: new Date().toLocaleDateString('ar-SA'),\n    customerName: 'شركة البساطة للأعمال',\n    customerDetails: 'الحلول البسيطة والفعالة',\n    items: [\n      { id: 1, description: 'خدمة استشارية', quantity: 2, price: 500.00, total: 1000.00 },\n      { id: 2, description: 'تطوير موقع إلكتروني', quantity: 1, price: 3000.00, total: 3000.00 },\n      { id: 3, description: 'صيانة شهرية', quantity: 6, price: 200.00, total: 1200.00 }\n    ],\n    subtotal: 5200.00,\n    tax: 780.00,\n    total: 5980.00,\n    notes: 'شكراً لاختياركم خدماتنا',\n    companyName: 'شركة البساطة للأعمال',\n    companyDetails: 'الحلول البسيطة والفعالة'\n  };\n\n  const data = preview ? sampleData : invoice;\n\n  return (\n    <div className=\"bg-white p-8\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Minimal Header */}\n      <div className=\"border-b-4 border-gray-800 pb-6 mb-8\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">{data.companyName}</h1>\n            <p className=\"text-gray-600\">{data.companyDetails}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <h2 className=\"text-4xl font-bold text-gray-800 mb-2\">فاتورة</h2>\n            <p className=\"text-gray-600 text-lg\">#{data.invoiceNumber}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Customer and Invoice Info */}\n      <div className=\"grid grid-cols-2 gap-12 mb-8\">\n        <div>\n          <h3 className=\"text-lg font-bold text-gray-800 mb-3\">إلى:</h3>\n          <div className=\"space-y-1\">\n            <p className=\"text-gray-800 font-semibold\">{data.customerName}</p>\n            <p className=\"text-gray-600\">{data.customerDetails}</p>\n          </div>\n        </div>\n\n        <div className=\"text-right\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">تاريخ الفاتورة:</span>\n              <span className=\"font-semibold\">{data.date}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">رقم الفاتورة:</span>\n              <span className=\"font-semibold\">{data.invoiceNumber}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Items Table */}\n      <div className=\"mb-8\">\n        <table className=\"w-full border-collapse\">\n          <thead>\n            <tr className=\"border-b-2 border-gray-800\">\n              <th className=\"py-3 text-right font-bold text-gray-800\">المبلغ</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">السعر</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الكمية</th>\n              <th className=\"py-3 text-right font-bold text-gray-800\">الوصف</th>\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id} className=\"border-b border-gray-200\">\n                <td className=\"py-4 text-right font-semibold\">{(Number(item.total) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{(Number(item.price) || 0).toFixed(2)} ر.س</td>\n                <td className=\"py-4 text-right\">{item.quantity || 0}</td>\n                <td className=\"py-4 text-right\">{item.description || ''}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Simple Totals */}\n      <div className=\"flex justify-between items-start mb-12\">\n        <div className=\"w-1/2\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3\">ملاحظات:</h4>\n          <p className=\"text-gray-700\">{data.notes}</p>\n\n          <div className=\"mt-6\">\n            <h5 className=\"font-bold text-gray-800 mb-2\">شروط الدفع:</h5>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• الدفع خلال 30 يوم</li>\n              <li>• تطبق غرامة تأخير 2% شهرياً</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"w-80\">\n          <div className=\"border-2 border-gray-800 p-6\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">المجموع الفرعي:</span>\n                <span className=\"font-semibold\">{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-700\">الضريبة (15%):</span>\n                <span className=\"font-semibold\">{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n              <div className=\"border-t-2 border-gray-800 pt-3\">\n                <div className=\"flex justify-between text-xl font-bold\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Simple Footer */}\n      <div className=\"border-t border-gray-300 pt-6\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-600\">\n            <p>الهاتف: +966 50 123 4567 | البريد: <EMAIL> | الموقع: www.simple.com</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-24 h-12 border border-gray-400 flex items-center justify-center text-gray-500 text-sm\">\n              الشعار\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAG3D;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;IAClE,MAAM,aAAa;QACjB,eAAe;QACf,MAAM,IAAI,OAAO,kBAAkB,CAAC;QACpC,cAAc;QACd,iBAAiB;QACjB,OAAO;YACL;gBAAE,IAAI;gBAAG,aAAa;gBAAiB,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;YAClF;gBAAE,IAAI;gBAAG,aAAa;gBAAuB,UAAU;gBAAG,OAAO;gBAAS,OAAO;YAAQ;YACzF;gBAAE,IAAI;gBAAG,aAAa;gBAAe,UAAU;gBAAG,OAAO;gBAAQ,OAAO;YAAQ;SACjF;QACD,UAAU;QACV,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,OAAO,UAAU,aAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;QAAe,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAExE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC,KAAK,WAAW;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAAiB,KAAK,cAAc;;;;;;;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;;wCAAwB;wCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA+B,KAAK,YAAY;;;;;;kDAC7D,8OAAC;wCAAE,WAAU;kDAAiB,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,IAAI;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;;;;;;;;;;;;sCAG5D,8OAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAG,WAAU;;gDAAiC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACpF,8OAAC;4CAAG,WAAU;;gDAAmB,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACtE,8OAAC;4CAAG,WAAU;sDAAmB,KAAK,QAAQ,IAAI;;;;;;sDAClD,8OAAC;4CAAG,WAAU;sDAAmB,KAAK,WAAW,IAAI;;;;;;;mCAJ9C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAYxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAiB,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;sCAGL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA0F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrH", "debugId": null}}, {"offset": {"line": 3621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/TemplatePreview.js"], "sourcesContent": ["// frontend/components/invoice-templates/TemplatePreview.js\n'use client';\n\nimport React, { useState } from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\n\nconst templates = {\n  classic: {\n    id: 'classic',\n    name: 'القالب الكلاسيكي',\n    description: 'تصميم تقليدي أنيق مع ألوان الأخضر والذهبي',\n    component: ClassicTemplate,\n    preview: '/images/templates/classic-preview.jpg',\n    features: ['تصميم تقليدي', 'ألوان هادئة', 'مناسب للشركات التقليدية']\n  },\n  modern: {\n    id: 'modern',\n    name: 'القالب الحديث',\n    description: 'تصميم عصري مع تدرجات لونية جذابة',\n    component: ModernTemplate,\n    preview: '/images/templates/modern-preview.jpg',\n    features: ['تصميم عصري', 'تدرجات لونية', 'مناسب للشركات التقنية']\n  },\n  elegant: {\n    id: 'elegant',\n    name: 'القالب الأنيق',\n    description: 'تصميم راقي مع لمسات ذهبية فاخرة',\n    component: ElegantTemplate,\n    preview: '/images/templates/elegant-preview.jpg',\n    features: ['تصميم راقي', 'لمسات ذهبية', 'مناسب للشركات الفاخرة']\n  },\n  minimal: {\n    id: 'minimal',\n    name: 'القالب البسيط',\n    description: 'تصميم بسيط ونظيف للاستخدام العام',\n    component: MinimalTemplate,\n    preview: '/images/templates/minimal-preview.jpg',\n    features: ['تصميم بسيط', 'سهل القراءة', 'مناسب لجميع الأعمال']\n  }\n};\n\nexport default function TemplatePreview({ onSelectTemplate, selectedTemplate }) {\n  const [previewTemplate, setPreviewTemplate] = useState(null);\n  const [scale, setScale] = useState(0.3);\n\n  const handlePreview = (templateId) => {\n    setPreviewTemplate(templateId);\n  };\n\n  const closePreview = () => {\n    setPreviewTemplate(null);\n  };\n\n  const handleSelect = (templateId) => {\n    onSelectTemplate(templateId);\n    closePreview();\n  };\n\n  return (\n    <div>\n      {/* Templates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {Object.values(templates).map((template) => (\n          <div\n            key={template.id}\n            className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:scale-105 ${\n              selectedTemplate === template.id\n                ? 'border-blue-500 ring-4 ring-blue-100'\n                : 'border-gray-200 hover:border-blue-300'\n            }`}\n          >\n            {/* Template Preview Image */}\n            <div className=\"relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl overflow-hidden\">\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"transform scale-[0.15] origin-top-left\">\n                  <template.component preview={true} />\n                </div>\n              </div>\n              \n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold opacity-0 hover:opacity-100 transition-opacity duration-300 transform hover:scale-105\"\n                >\n                  معاينة كاملة\n                </button>\n              </div>\n            </div>\n\n            {/* Template Info */}\n            <div className=\"p-4\">\n              <h3 className=\"text-lg font-bold text-gray-800 mb-2\">{template.name}</h3>\n              <p className=\"text-gray-600 text-sm mb-3\">{template.description}</p>\n              \n              {/* Features */}\n              <div className=\"mb-4\">\n                <div className=\"flex flex-wrap gap-1\">\n                  {template.features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-2\">\n                <button\n                  onClick={() => handlePreview(template.id)}\n                  className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors\"\n                >\n                  معاينة\n                </button>\n                <button\n                  onClick={() => handleSelect(template.id)}\n                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${\n                    selectedTemplate === template.id\n                      ? 'bg-green-500 text-white'\n                      : 'bg-blue-500 text-white hover:bg-blue-600'\n                  }`}\n                >\n                  {selectedTemplate === template.id ? 'محدد' : 'اختيار'}\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Full Preview Modal */}\n      {previewTemplate && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white rounded-xl max-w-6xl max-h-[90vh] overflow-hidden flex flex-col\">\n            {/* Modal Header */}\n            <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\n              <div>\n                <h3 className=\"text-xl font-bold text-gray-800\">\n                  معاينة {templates[previewTemplate].name}\n                </h3>\n                <p className=\"text-gray-600 text-sm\">\n                  {templates[previewTemplate].description}\n                </p>\n              </div>\n              \n              <div className=\"flex items-center gap-4\">\n                {/* Zoom Controls */}\n                <div className=\"flex items-center gap-2\">\n                  <button\n                    onClick={() => setScale(Math.max(0.1, scale - 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 12H4\" />\n                    </svg>\n                  </button>\n                  <span className=\"text-sm font-medium min-w-[60px] text-center\">\n                    {Math.round(scale * 100)}%\n                  </span>\n                  <button\n                    onClick={() => setScale(Math.min(1, scale + 0.1))}\n                    className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                    </svg>\n                  </button>\n                </div>\n                \n                <button\n                  onClick={closePreview}\n                  className=\"bg-gray-100 hover:bg-gray-200 p-2 rounded-lg\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Preview Content */}\n            <div className=\"flex-1 overflow-auto p-4 bg-gray-100\">\n              <div className=\"flex justify-center\">\n                <div \n                  className=\"bg-white shadow-lg\"\n                  style={{ \n                    transform: `scale(${scale})`,\n                    transformOrigin: 'top center'\n                  }}\n                >\n                  {React.createElement(templates[previewTemplate].component, { preview: true })}\n                </div>\n              </div>\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex gap-2\">\n                  {templates[previewTemplate].features.map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={closePreview}\n                    className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    إغلاق\n                  </button>\n                  <button\n                    onClick={() => handleSelect(previewTemplate)}\n                    className=\"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium\"\n                  >\n                    اختيار هذا القالب\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport { templates };\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,YAAY;IAChB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAgB;YAAe;SAA0B;IACtE;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,oJAAA,CAAA,UAAc;QACzB,SAAS;QACT,UAAU;YAAC;YAAc;YAAgB;SAAwB;IACnE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAwB;IAClE;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,WAAW,qJAAA,CAAA,UAAe;QAC1B,SAAS;QACT,UAAU;YAAC;YAAc;YAAe;SAAsB;IAChE;AACF;AAEe,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB,CAAC;QACrB,mBAAmB;IACrB;IAEA,MAAM,eAAe;QACnB,mBAAmB;IACrB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB;IACF;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,yBAC7B,8OAAC;wBAEC,WAAW,CAAC,mGAAmG,EAC7G,qBAAqB,SAAS,EAAE,GAC5B,yCACA,yCACJ;;0CAGF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,SAAS,SAAS;gDAAC,SAAS;;;;;;;;;;;;;;;;kDAKjC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,cAAc,SAAS,EAAE;4CACxC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC,SAAS,IAAI;;;;;;kDACnE,8OAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;kDAUb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,cAAc,SAAS,EAAE;gDACxC,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,aAAa,SAAS,EAAE;gDACvC,WAAW,CAAC,kEAAkE,EAC5E,qBAAqB,SAAS,EAAE,GAC5B,4BACA,4CACJ;0DAED,qBAAqB,SAAS,EAAE,GAAG,SAAS;;;;;;;;;;;;;;;;;;;uBA7D9C,SAAS,EAAE;;;;;;;;;;YAsErB,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAkC;gDACtC,SAAS,CAAC,gBAAgB,CAAC,IAAI;;;;;;;sDAEzC,8OAAC;4CAAE,WAAU;sDACV,SAAS,CAAC,gBAAgB,CAAC,WAAW;;;;;;;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,KAAK,QAAQ;oDAC9C,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,QAAQ;wDAAK;;;;;;;8DAE3B,8OAAC;oDACC,SAAS,IAAM,SAAS,KAAK,GAAG,CAAC,GAAG,QAAQ;oDAC5C,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAK3E,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;wCAC5B,iBAAiB;oCACnB;8CAEC,cAAA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE;wCAAE,SAAS;oCAAK;;;;;;;;;;;;;;;;sCAMjF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;kDAQX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 4105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/TemplateSelector.js"], "sourcesContent": ["// frontend/components/invoice-templates/TemplateSelector.js\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { templates } from './TemplatePreview';\n\nexport default function TemplateSelector({ selectedTemplate, onTemplateChange, compact = false }) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentTemplate, setCurrentTemplate] = useState(selectedTemplate || 'classic');\n\n  useEffect(() => {\n    // Load saved template from localStorage\n    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');\n    if (savedTemplate && templates[savedTemplate]) {\n      setCurrentTemplate(savedTemplate);\n      onTemplateChange?.(savedTemplate);\n    }\n  }, [onTemplateChange]);\n\n  const handleTemplateSelect = (templateId) => {\n    setCurrentTemplate(templateId);\n    onTemplateChange?.(templateId);\n    setIsOpen(false);\n    \n    // Save to localStorage\n    localStorage.setItem('selectedInvoiceTemplate', templateId);\n  };\n\n  if (compact) {\n    return (\n      <div className=\"relative\">\n        <button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n        >\n          <svg className=\"w-4 h-4 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n          </svg>\n          <span className=\"text-sm font-medium\">{templates[currentTemplate]?.name}</span>\n          <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n            <div className=\"p-2\">\n              {Object.values(templates).map((template) => (\n                <button\n                  key={template.id}\n                  onClick={() => handleTemplateSelect(template.id)}\n                  className={`w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors ${\n                    currentTemplate === template.id ? 'bg-blue-50 border border-blue-200' : ''\n                  }`}\n                >\n                  <div className=\"font-medium text-sm\">{template.name}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">{template.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">اختيار قالب الفاتورة</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {Object.values(templates).map((template) => (\n          <div\n            key={template.id}\n            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${\n              currentTemplate === template.id\n                ? 'border-blue-500 bg-blue-50'\n                : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'\n            }`}\n            onClick={() => handleTemplateSelect(template.id)}\n          >\n            <div className=\"flex items-start gap-3\">\n              <div className=\"flex-shrink-0\">\n                <div className={`w-4 h-4 rounded-full border-2 ${\n                  currentTemplate === template.id\n                    ? 'border-blue-500 bg-blue-500'\n                    : 'border-gray-300'\n                }`}>\n                  {currentTemplate === template.id && (\n                    <div className=\"w-full h-full rounded-full bg-white scale-50\"></div>\n                  )}\n                </div>\n              </div>\n              \n              <div className=\"flex-1\">\n                <h4 className=\"font-medium text-gray-900\">{template.name}</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">{template.description}</p>\n                \n                <div className=\"flex flex-wrap gap-1 mt-2\">\n                  {template.features.slice(0, 2).map((feature, index) => (\n                    <span\n                      key={index}\n                      className=\"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"text-sm text-gray-600\">\n        <p>💡 يمكنك تغيير القالب في أي وقت من صفحة قوالب الفواتير</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;AAG5D;AACA;AAHA;;;;AAKe,SAAS,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,KAAK,EAAE;IAC9F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,qJAAA,CAAA,YAAS,CAAC,cAAc,EAAE;YAC7C,mBAAmB;YACnB,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,mBAAmB;QACnB,UAAU;QAEV,uBAAuB;QACvB,aAAa,OAAO,CAAC,2BAA2B;IAClD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAK,WAAU;sCAAuB,qJAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;;;;;;sCACnE,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;;;;;;;gBAIxE,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,CAAC,qJAAA,CAAA,YAAS,EAAE,GAAG,CAAC,CAAC,yBAC7B,8OAAC;gCAEC,SAAS,IAAM,qBAAqB,SAAS,EAAE;gCAC/C,WAAW,CAAC,mEAAmE,EAC7E,oBAAoB,SAAS,EAAE,GAAG,sCAAsC,IACxE;;kDAEF,8OAAC;wCAAI,WAAU;kDAAuB,SAAS,IAAI;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDAA8B,SAAS,WAAW;;;;;;;+BAP5D,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;IAehC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAsC;;;;;;0BAEpD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,MAAM,CAAC,qJAAA,CAAA,YAAS,EAAE,GAAG,CAAC,CAAC,yBAC7B,8OAAC;wBAEC,WAAW,CAAC,sDAAsD,EAChE,oBAAoB,SAAS,EAAE,GAC3B,+BACA,0DACJ;wBACF,SAAS,IAAM,qBAAqB,SAAS,EAAE;kCAE/C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,8BAA8B,EAC7C,oBAAoB,SAAS,EAAE,GAC3B,gCACA,mBACJ;kDACC,oBAAoB,SAAS,EAAE,kBAC9B,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B,SAAS,IAAI;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDAA8B,SAAS,WAAW;;;;;;sDAE/D,8OAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC3C,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;;uBA5BV,SAAS,EAAE;;;;;;;;;;0BAyCtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 4368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/EnhancedClassicTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/EnhancedClassicTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function EnhancedClassicTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // استخدام البيانات الحقيقية فقط - لا توجد بيانات افتراضية\n  const data = invoice || {};\n\n  // استخدام إعدادات العرض من البيانات إذا لم تُمرر كخاصية منفصلة\n  const settings = displaySettings || data.displaySettings || {};\n\n  // إذا لم تكن هناك بيانات، عرض رسالة\n  if (!data.companyName && !preview) {\n    return (\n      <div className=\"bg-white p-8 text-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"flex flex-col items-center justify-center h-full\">\n          <svg className=\"w-16 h-16 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">لا توجد بيانات للعرض</h3>\n          <p className=\"text-gray-500 text-center max-w-md\">\n            يرجى إدخال معلومات الشركة في الإعدادات وإنشاء فاتورة جديدة لعرض البيانات الحقيقية.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              {settings.showCompanyName !== false && data.companyName && (\n                <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              )}\n              {displaySettings.showCompanyAddress !== false && data.companyAddress && (\n                <p className=\"text-teal-100 text-sm\">{data.companyAddress}</p>\n              )}\n              {displaySettings.showCompanyPhone !== false && data.companyPhone && (\n                <p className=\"text-teal-100 text-sm\">📞 {data.companyPhone}</p>\n              )}\n              {displaySettings.showCompanyEmail !== false && data.companyEmail && (\n                <p className=\"text-teal-100 text-sm\">📧 {data.companyEmail}</p>\n              )}\n              {displaySettings.showCompanyWebsite !== false && data.companyWebsite && (\n                <p className=\"text-teal-100 text-sm\">🌐 {data.companyWebsite}</p>\n              )}\n              {displaySettings.showTaxId !== false && data.taxId && (\n                <p className=\"text-teal-100 text-sm\">الرقم الضريبي: {data.taxId}</p>\n              )}\n            </div>\n\n            {/* Logo Area */}\n            {displaySettings.showCompanyLogo !== false && (\n              <div className=\"bg-white p-4 rounded-lg shadow-md\">\n                {data.logoUrl ? (\n                  <img\n                    src={data.logoUrl}\n                    alt=\"شعار الشركة\"\n                    className=\"w-16 h-16 object-contain rounded-lg\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-white font-bold text-sm\">شعار</div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title and Info */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          {displaySettings.showInvoiceNumber !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">رقم الفاتورة: {data.invoiceNumber}</span>\n            </div>\n          )}\n          {displaySettings.showCustomerName !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">اسم العميل: {data.customerName}</span>\n            </div>\n          )}\n          {displaySettings.showInvoiceDate !== false && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">التاريخ: {data.date}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Customer Information */}\n      <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n        <h3 className=\"font-semibold text-gray-800 mb-3\">معلومات العميل:</h3>\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          {displaySettings.showCustomerName !== false && (\n            <div><span className=\"font-semibold\">الاسم:</span> {data.customerName}</div>\n          )}\n          {displaySettings.showCustomerAddress !== false && data.customerAddress && (\n            <div><span className=\"font-semibold\">العنوان:</span> {data.customerAddress}</div>\n          )}\n          {displaySettings.showCustomerPhone !== false && data.customerPhone && (\n            <div><span className=\"font-semibold\">الهاتف:</span> {data.customerPhone}</div>\n          )}\n          {displaySettings.showCustomerEmail !== false && data.customerEmail && (\n            <div><span className=\"font-semibold\">البريد:</span> {data.customerEmail}</div>\n          )}\n        </div>\n      </div>\n\n      {/* Items Table */}\n      <div className=\"mb-6\">\n        <table className=\"w-full border-collapse border border-gray-300\">\n          <thead className=\"bg-teal-600 text-white\">\n            <tr>\n              {displaySettings.showTotalPrice !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n              )}\n              {displaySettings.showUnitPrice !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n              )}\n              {displaySettings.showQuantity !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n              )}\n              {displaySettings.showProductDescription !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الوصف</th>\n              )}\n              {displaySettings.showProductCode !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكود</th>\n              )}\n              {displaySettings.showItemNumbers !== false && (\n                <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n              )}\n            </tr>\n          </thead>\n          <tbody>\n            {data.items.map((item, index) => (\n              <tr key={item.id}>\n                {displaySettings.showTotalPrice !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                )}\n                {displaySettings.showUnitPrice !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                )}\n                {displaySettings.showQuantity !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                )}\n                {displaySettings.showProductDescription !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                )}\n                {displaySettings.showProductCode !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{item.code || ''}</td>\n                )}\n                {displaySettings.showItemNumbers !== false && (\n                  <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n                )}\n              </tr>\n            ))}\n\n            {/* Empty rows for spacing */}\n            {[...Array(3)].map((_, i) => (\n              <tr key={`empty-${i}`}>\n                {displaySettings.showTotalPrice !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showUnitPrice !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showQuantity !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showProductDescription !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showProductCode !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n                {displaySettings.showItemNumbers !== false && <td className=\"border border-gray-300 p-3\">&nbsp;</td>}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Summary and Notes */}\n      <div className=\"flex justify-between items-start mb-6\">\n        <div className=\"w-1/2 pr-4\">\n          {displaySettings.showNotes !== false && data.notes && (\n            <div className=\"mb-4\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">ملاحظات:</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n            </div>\n          )}\n\n          {displaySettings.showPaymentTerms !== false && data.paymentTerms && (\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h4>\n              <p className=\"text-gray-700 text-sm\">{data.paymentTerms}</p>\n            </div>\n          )}\n        </div>\n\n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            {displaySettings.showSubtotal !== false && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">المجموع الفرعي:</span>\n                <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>\n              </div>\n            )}\n            {displaySettings.showTaxAmount !== false && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">الضريبة:</span>\n                <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>\n              </div>\n            )}\n            {displaySettings.showTotalAmount !== false && (\n              <div className=\"border-t border-gray-300 pt-2\">\n                <div className=\"flex justify-between font-bold text-lg\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      {displaySettings.showFooter !== false && data.invoiceFooter && (\n        <div className=\"text-center border-t border-gray-300 pt-4\">\n          <p className=\"text-gray-600\">{data.invoiceFooter}</p>\n        </div>\n      )}\n\n      {/* Signature Area */}\n      {displaySettings.showSignature === true && (\n        <div className=\"mt-8 flex justify-end\">\n          <div className=\"text-center\">\n            <div className=\"w-32 h-16 border-b border-gray-400 mb-2\"></div>\n            <p className=\"text-sm text-gray-600\">التوقيع</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AAGnE;AAFA;;;AAIe,SAAS,wBAAwB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IAChG,0DAA0D;IAC1D,MAAM,OAAO,WAAW,CAAC;IAEzB,+DAA+D;IAC/D,MAAM,WAAW,mBAAmB,KAAK,eAAe,IAAI,CAAC;IAE7D,oCAAoC;IACpC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,SAAS;QACjC,qBACE,8OAAC;YAAI,WAAU;YAA2B,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACpF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAM1D;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,eAAe,KAAK,SAAS,KAAK,WAAW,kBACrD,8OAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;wCAE1D,gBAAgB,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAClE,8OAAC;4CAAE,WAAU;sDAAyB,KAAK,cAAc;;;;;;wCAE1D,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,gBAAgB,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAClE,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,cAAc;;;;;;;wCAE7D,gBAAgB,SAAS,KAAK,SAAS,KAAK,KAAK,kBAChD,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,KAAK,KAAK;;;;;;;;;;;;;gCAKlE,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;oCAAI,WAAU;8CACZ,KAAK,OAAO,iBACX,8OAAC;wCACC,KAAK,KAAK,OAAO;wCACjB,KAAI;wCACJ,WAAU;;;;;6DAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,iBAAiB,KAAK,uBACrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAe,KAAK,aAAa;;;;;;;;;;;;4BAGpE,gBAAgB,gBAAgB,KAAK,uBACpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAa,KAAK,YAAY;;;;;;;;;;;;4BAGjE,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAU,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,gBAAgB,KAAK,uBACpC,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;oCAAE,KAAK,YAAY;;;;;;;4BAEtE,gBAAgB,mBAAmB,KAAK,SAAS,KAAK,eAAe,kBACpE,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAe;oCAAE,KAAK,eAAe;;;;;;;4BAE3E,gBAAgB,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBAChE,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;4BAExE,gBAAgB,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBAChE,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;oCACE,gBAAgB,cAAc,KAAK,uBAClC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,aAAa,KAAK,uBACjC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,YAAY,KAAK,uBAChC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,sBAAsB,KAAK,uBAC1C,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAI1E,8OAAC;;gCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;;4CACE,gBAAgB,cAAc,KAAK,uBAClC,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;4CAE1F,gBAAgB,aAAa,KAAK,uBACjC,8OAAC;gDAAG,WAAU;0DAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;4CAE1F,gBAAgB,YAAY,KAAK,uBAChC,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,QAAQ,IAAI;;;;;;4CAEzE,gBAAgB,sBAAsB,KAAK,uBAC1C,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,WAAW,IAAI;;;;;;4CAE5E,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,IAAI,IAAI;;;;;;4CAErE,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;gDAAG,WAAU;0DAAyC,QAAQ;;;;;;;uCAjB1D,KAAK,EAAE;;;;;gCAuBjB;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;;4CACE,gBAAgB,cAAc,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACvF,gBAAgB,aAAa,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACtF,gBAAgB,YAAY,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACrF,gBAAgB,sBAAsB,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;4CAC/F,gBAAgB,eAAe,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;4CACxF,gBAAgB,eAAe,KAAK,uBAAS,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;;uCANlF,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;0BAc7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,SAAS,KAAK,SAAS,KAAK,KAAK,kBAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;4BAI3C,gBAAgB,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBAC9D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,YAAY;;;;;;;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,YAAY,KAAK,uBAChC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;gDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;gCAGlD,gBAAgB,aAAa,KAAK,uBACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;gDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;gCAG7C,gBAAgB,eAAe,KAAK,uBACnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASvD,gBAAgB,UAAU,KAAK,SAAS,KAAK,aAAa,kBACzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAiB,KAAK,aAAa;;;;;;;;;;;YAKnD,gBAAgB,aAAa,KAAK,sBACjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 5194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/RealDataTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/RealDataTemplate.js\n'use client';\n\nimport React from 'react';\n\nexport default function RealDataTemplate({ invoice, preview = false, displaySettings = {} }) {\n  // استخدام البيانات الحقيقية فقط - لا توجد بيانات افتراضية\n  const data = invoice || {};\n  \n  // استخدام إعدادات العرض من البيانات إذا لم تُمرر كخاصية منفصلة\n  const settings = displaySettings || data.displaySettings || {};\n\n  // إذا لم تكن هناك بيانات، عرض رسالة\n  if (!data.companyName && !preview) {\n    return (\n      <div className=\"bg-white p-8 text-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"flex flex-col items-center justify-center h-full\">\n          <svg className=\"w-16 h-16 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">لا توجد بيانات للعرض</h3>\n          <p className=\"text-gray-500 text-center max-w-md\">\n            يرجى إدخال معلومات الشركة في الإعدادات وإنشاء فاتورة جديدة لعرض البيانات الحقيقية.\n          </p>\n          <div className=\"mt-4\">\n            <a href=\"/dashboard/settings\" className=\"text-blue-500 hover:text-blue-700 underline\">\n              انتقل إلى الإعدادات\n            </a>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-8 shadow-lg\" style={{ minHeight: '297mm', width: '210mm' }}>\n      {/* Header */}\n      <div className=\"relative mb-8\">\n        <div className=\"bg-teal-600 h-16 w-full absolute top-0 left-0\"></div>\n        <div className=\"bg-teal-600 h-8 w-3/4 absolute top-16 left-0\"></div>\n\n        <div className=\"relative z-10 pt-6 pb-4\">\n          <div className=\"flex justify-between items-start\">\n            <div className=\"text-white\">\n              {settings.showCompanyName !== false && data.companyName && (\n                <h1 className=\"text-2xl font-bold mb-2\">{data.companyName}</h1>\n              )}\n              {settings.showCompanyAddress !== false && data.companyAddress && (\n                <p className=\"text-teal-100 text-sm\">{data.companyAddress}</p>\n              )}\n              {settings.showCompanyPhone !== false && data.companyPhone && (\n                <p className=\"text-teal-100 text-sm\">📞 {data.companyPhone}</p>\n              )}\n              {settings.showCompanyEmail !== false && data.companyEmail && (\n                <p className=\"text-teal-100 text-sm\">📧 {data.companyEmail}</p>\n              )}\n              {settings.showCompanyWebsite !== false && data.companyWebsite && (\n                <p className=\"text-teal-100 text-sm\">🌐 {data.companyWebsite}</p>\n              )}\n              {settings.showTaxId !== false && data.taxId && (\n                <p className=\"text-teal-100 text-sm\">الرقم الضريبي: {data.taxId}</p>\n              )}\n            </div>\n\n            {/* Logo Area */}\n            {settings.showCompanyLogo !== false && (\n              <div className=\"bg-white p-4 rounded-lg shadow-md\">\n                {data.logoUrl ? (\n                  <img\n                    src={data.logoUrl}\n                    alt=\"شعار الشركة\"\n                    className=\"w-16 h-16 object-contain rounded-lg\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-white font-bold text-sm\">شعار</div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Invoice Title and Info */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-teal-700 mb-4\">فاتورة مبيعات</h2>\n\n        <div className=\"grid grid-cols-3 gap-4 text-sm\">\n          {settings.showInvoiceNumber !== false && data.invoiceNumber && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">رقم الفاتورة: {data.invoiceNumber}</span>\n            </div>\n          )}\n          {settings.showCustomerName !== false && data.customerName && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">اسم العميل: {data.customerName}</span>\n            </div>\n          )}\n          {settings.showInvoiceDate !== false && data.date && (\n            <div className=\"text-right\">\n              <span className=\"font-semibold\">التاريخ: {data.date}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Customer Information */}\n      {(settings.showCustomerName !== false || settings.showCustomerAddress !== false || \n        settings.showCustomerPhone !== false || settings.showCustomerEmail !== false) && (\n        <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n          <h3 className=\"font-semibold text-gray-800 mb-3\">معلومات العميل:</h3>\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            {settings.showCustomerName !== false && data.customerName && (\n              <div><span className=\"font-semibold\">الاسم:</span> {data.customerName}</div>\n            )}\n            {settings.showCustomerAddress !== false && data.customerAddress && (\n              <div><span className=\"font-semibold\">العنوان:</span> {data.customerAddress}</div>\n            )}\n            {settings.showCustomerPhone !== false && data.customerPhone && (\n              <div><span className=\"font-semibold\">الهاتف:</span> {data.customerPhone}</div>\n            )}\n            {settings.showCustomerEmail !== false && data.customerEmail && (\n              <div><span className=\"font-semibold\">البريد:</span> {data.customerEmail}</div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Items Table */}\n      {data.items && data.items.length > 0 && (\n        <div className=\"mb-6\">\n          <table className=\"w-full border-collapse border border-gray-300\">\n            <thead className=\"bg-teal-600 text-white\">\n              <tr>\n                {settings.showItemNumbers !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">م</th>\n                )}\n                {settings.showProductCode !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكود</th>\n                )}\n                {settings.showProductDescription !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الوصف</th>\n                )}\n                {settings.showQuantity !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الكمية</th>\n                )}\n                {settings.showUnitPrice !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">السعر</th>\n                )}\n                {settings.showTotalPrice !== false && (\n                  <th className=\"border border-gray-300 p-3 text-right font-semibold\">الإجمالي</th>\n                )}\n              </tr>\n            </thead>\n            <tbody>\n              {data.items.map((item, index) => (\n                <tr key={item.id || index}>\n                  {settings.showItemNumbers !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{index + 1}</td>\n                  )}\n                  {settings.showProductCode !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.code || ''}</td>\n                  )}\n                  {settings.showProductDescription !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.description || ''}</td>\n                  )}\n                  {settings.showQuantity !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{item.quantity || 0}</td>\n                  )}\n                  {settings.showUnitPrice !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.price) || 0).toFixed(2)}</td>\n                  )}\n                  {settings.showTotalPrice !== false && (\n                    <td className=\"border border-gray-300 p-3 text-right\">{(Number(item.total) || 0).toFixed(2)}</td>\n                  )}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n\n      {/* Summary and Notes */}\n      <div className=\"flex justify-between items-start mb-6\">\n        <div className=\"w-1/2 pr-4\">\n          {settings.showNotes !== false && data.notes && (\n            <div className=\"mb-4\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">ملاحظات:</h4>\n              <p className=\"text-gray-700\">{data.notes}</p>\n            </div>\n          )}\n          \n          {settings.showPaymentTerms !== false && data.paymentTerms && (\n            <div>\n              <h4 className=\"font-semibold text-gray-800 mb-2\">شروط الدفع:</h4>\n              <p className=\"text-gray-700 text-sm\">{data.paymentTerms}</p>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"w-64\">\n          <div className=\"bg-yellow-100 p-4 rounded-lg\">\n            {settings.showSubtotal !== false && data.subtotal !== undefined && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">المجموع الفرعي:</span>\n                <span>{(Number(data.subtotal) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n              </div>\n            )}\n            {settings.showTaxAmount !== false && data.tax !== undefined && (\n              <div className=\"flex justify-between mb-2\">\n                <span className=\"font-semibold\">الضريبة:</span>\n                <span>{(Number(data.tax) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n              </div>\n            )}\n            {settings.showTotalAmount !== false && data.total !== undefined && (\n              <div className=\"border-t border-gray-300 pt-2\">\n                <div className=\"flex justify-between font-bold text-lg\">\n                  <span>الإجمالي:</span>\n                  <span>{(Number(data.total) || 0).toFixed(2)} {data.currency || 'ر.س'}</span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      {settings.showFooter !== false && data.invoiceFooter && (\n        <div className=\"text-center border-t border-gray-300 pt-4\">\n          <p className=\"text-gray-600\">{data.invoiceFooter}</p>\n        </div>\n      )}\n\n      {/* Signature Area */}\n      {settings.showSignature === true && (\n        <div className=\"mt-8 flex justify-end\">\n          <div className=\"text-center\">\n            <div className=\"w-32 h-16 border-b border-gray-400 mb-2\"></div>\n            <p className=\"text-sm text-gray-600\">التوقيع</p>\n          </div>\n        </div>\n      )}\n\n      {/* QR Code Area */}\n      {settings.showQRCode === true && (\n        <div className=\"mt-4 flex justify-center\">\n          <div className=\"w-24 h-24 border border-gray-300 rounded-lg flex items-center justify-center\">\n            <span className=\"text-xs text-gray-500\">QR Code</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;AAG5D;AAFA;;;AAIe,SAAS,iBAAiB,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,kBAAkB,CAAC,CAAC,EAAE;IACzF,0DAA0D;IAC1D,MAAM,OAAO,WAAW,CAAC;IAEzB,+DAA+D;IAC/D,MAAM,WAAW,mBAAmB,KAAK,eAAe,IAAI,CAAC;IAE7D,oCAAoC;IACpC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,SAAS;QACjC,qBACE,8OAAC;YAAI,WAAU;YAA2B,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACpF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,MAAK;4BAAsB,WAAU;sCAA8C;;;;;;;;;;;;;;;;;;;;;;IAOhG;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,WAAW;YAAS,OAAO;QAAQ;;0BAElF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,eAAe,KAAK,SAAS,KAAK,WAAW,kBACrD,8OAAC;4CAAG,WAAU;sDAA2B,KAAK,WAAW;;;;;;wCAE1D,SAAS,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAC3D,8OAAC;4CAAE,WAAU;sDAAyB,KAAK,cAAc;;;;;;wCAE1D,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,YAAY;;;;;;;wCAE3D,SAAS,kBAAkB,KAAK,SAAS,KAAK,cAAc,kBAC3D,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,KAAK,cAAc;;;;;;;wCAE7D,SAAS,SAAS,KAAK,SAAS,KAAK,KAAK,kBACzC,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,KAAK,KAAK;;;;;;;;;;;;;gCAKlE,SAAS,eAAe,KAAK,uBAC5B,8OAAC;oCAAI,WAAU;8CACZ,KAAK,OAAO,iBACX,8OAAC;wCACC,KAAK,KAAK,OAAO;wCACjB,KAAI;wCACJ,WAAU;;;;;6DAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAe,KAAK,aAAa;;;;;;;;;;;;4BAGpE,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAa,KAAK,YAAY;;;;;;;;;;;;4BAGjE,SAAS,eAAe,KAAK,SAAS,KAAK,IAAI,kBAC9C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAgB;wCAAU,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAO1D,CAAC,SAAS,gBAAgB,KAAK,SAAS,SAAS,mBAAmB,KAAK,SACxE,SAAS,iBAAiB,KAAK,SAAS,SAAS,iBAAiB,KAAK,KAAK,mBAC5E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;oCAAE,KAAK,YAAY;;;;;;;4BAEtE,SAAS,mBAAmB,KAAK,SAAS,KAAK,eAAe,kBAC7D,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAe;oCAAE,KAAK,eAAe;;;;;;;4BAE3E,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;4BAExE,SAAS,iBAAiB,KAAK,SAAS,KAAK,aAAa,kBACzD,8OAAC;;kDAAI,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;oCAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;;;YAO9E,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;oCACE,SAAS,eAAe,KAAK,uBAC5B,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,eAAe,KAAK,uBAC5B,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,sBAAsB,KAAK,uBACnC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,YAAY,KAAK,uBACzB,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,aAAa,KAAK,uBAC1B,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAErE,SAAS,cAAc,KAAK,uBAC3B,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAI1E,8OAAC;sCACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;;wCACE,SAAS,eAAe,KAAK,uBAC5B,8OAAC;4CAAG,WAAU;sDAAyC,QAAQ;;;;;;wCAEhE,SAAS,eAAe,KAAK,uBAC5B,8OAAC;4CAAG,WAAU;sDAAyC,KAAK,IAAI,IAAI;;;;;;wCAErE,SAAS,sBAAsB,KAAK,uBACnC,8OAAC;4CAAG,WAAU;sDAAyC,KAAK,WAAW,IAAI;;;;;;wCAE5E,SAAS,YAAY,KAAK,uBACzB,8OAAC;4CAAG,WAAU;sDAAyC,KAAK,QAAQ,IAAI;;;;;;wCAEzE,SAAS,aAAa,KAAK,uBAC1B,8OAAC;4CAAG,WAAU;sDAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;wCAE1F,SAAS,cAAc,KAAK,uBAC3B,8OAAC;4CAAG,WAAU;sDAAyC,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;mCAjBpF,KAAK,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;0BA2B9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,SAAS,KAAK,SAAS,KAAK,KAAK,kBACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;4BAI3C,SAAS,gBAAgB,KAAK,SAAS,KAAK,YAAY,kBACvD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,YAAY;;;;;;;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,YAAY,KAAK,SAAS,KAAK,QAAQ,KAAK,2BACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;gDAAM,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;gDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;gCAGrE,SAAS,aAAa,KAAK,SAAS,KAAK,GAAG,KAAK,2BAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;gDAAM,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;gDAAG;gDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;gCAGhE,SAAS,eAAe,KAAK,SAAS,KAAK,KAAK,KAAK,2BACpD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;oDAAG;oDAAE,KAAK,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1E,SAAS,UAAU,KAAK,SAAS,KAAK,aAAa,kBAClD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAiB,KAAK,aAAa;;;;;;;;;;;YAKnD,SAAS,aAAa,KAAK,sBAC1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM1C,SAAS,UAAU,KAAK,sBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 6001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/settingsService.js"], "sourcesContent": ["// frontend/lib/settingsService.js\n\n// خدمة إدارة الإعدادات\nclass SettingsService {\n  constructor() {\n    this.storageKey = 'invoiceAppSettings';\n    this.defaultSettings = this.getDefaultSettings();\n  }\n\n  // الحصول على الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      company: {\n        companyName: '',\n        companyAddress: '',\n        companyPhone: '',\n        companyEmail: '',\n        companyWebsite: '',\n        taxId: '',\n        currency: 'SAR',\n        logoUrl: null\n      },\n      invoice: {\n        invoicePrefix: 'INV',\n        invoiceNumberLength: 6,\n        defaultTaxRate: 15,\n        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n        autoGenerateInvoiceNumber: true,\n        showImagesInInvoice: true,\n        allowPartialPayments: true,\n        requireCustomerInfo: true,\n        defaultDueDays: 30,\n        invoiceFooter: '',\n        invoiceNotes: '',\n        // إعدادات العرض\n        display: {\n          // عناصر الرأس\n          showCompanyLogo: true,\n          showCompanyName: true,\n          showCompanyAddress: true,\n          showCompanyPhone: true,\n          showCompanyEmail: true,\n          showCompanyWebsite: true,\n          showTaxId: true,\n\n          // معلومات الفاتورة\n          showInvoiceNumber: true,\n          showInvoiceDate: true,\n          showDueDate: true,\n          showPaymentTerms: true,\n\n          // معلومات العميل\n          showCustomerName: true,\n          showCustomerAddress: true,\n          showCustomerPhone: true,\n          showCustomerEmail: true,\n\n          // عناصر الجدول\n          showProductImages: true,\n          showProductCode: true,\n          showProductDescription: true,\n          showQuantity: true,\n          showUnitPrice: true,\n          showDiscount: false,\n          showTotalPrice: true,\n          showItemNumbers: true,\n\n          // المجاميع\n          showSubtotal: true,\n          showTaxAmount: true,\n          showDiscountAmount: false,\n          showTotalAmount: true,\n\n          // التذييل\n          showNotes: true,\n          showFooter: true,\n          showSignature: false,\n          showQRCode: false,\n          showBankDetails: false,\n          showPaymentInstructions: true\n        }\n      }\n    };\n  }\n\n  // تحميل الإعدادات\n  async loadSettings() {\n    try {\n      // محاولة تحميل من localStorage أولاً\n      const localSettings = localStorage.getItem(this.storageKey);\n      if (localSettings) {\n        const parsed = JSON.parse(localSettings);\n        return this.mergeWithDefaults(parsed);\n      }\n\n      // في المستقبل: تحميل من API\n      // const response = await fetch('/api/settings');\n      // const settings = await response.json();\n      // return this.mergeWithDefaults(settings);\n\n      return this.defaultSettings;\n    } catch (error) {\n      console.error('Error loading settings:', error);\n      return this.defaultSettings;\n    }\n  }\n\n  // حفظ الإعدادات\n  async saveSettings(settings) {\n    try {\n      const mergedSettings = this.mergeWithDefaults(settings);\n\n      // حفظ في localStorage\n      localStorage.setItem(this.storageKey, JSON.stringify(mergedSettings));\n\n      // في المستقبل: حفظ في API\n      // await fetch('/api/settings', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(mergedSettings)\n      // });\n\n      return mergedSettings;\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      throw error;\n    }\n  }\n\n  // دمج الإعدادات مع الافتراضية\n  mergeWithDefaults(settings) {\n    return {\n      company: { ...this.defaultSettings.company, ...settings.company },\n      invoice: {\n        ...this.defaultSettings.invoice,\n        ...settings.invoice,\n        display: {\n          ...this.defaultSettings.invoice.display,\n          ...settings.invoice?.display\n        }\n      }\n    };\n  }\n\n  // الحصول على إعدادات الشركة\n  async getCompanySettings() {\n    const settings = await this.loadSettings();\n    return settings.company;\n  }\n\n  // الحصول على إعدادات الفواتير\n  async getInvoiceSettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice;\n  }\n\n  // الحصول على إعدادات عرض الفواتير\n  async getInvoiceDisplaySettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice.display;\n  }\n\n  // حفظ إعدادات الشركة\n  async saveCompanySettings(companySettings) {\n    const currentSettings = await this.loadSettings();\n\n    // معالجة رفع الصورة إذا كانت موجودة\n    if (companySettings.logoFile) {\n      try {\n        const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);\n        companySettings.logoUrl = logoUrl;\n        delete companySettings.logoFile; // إزالة الملف من البيانات\n      } catch (error) {\n        console.error('Error uploading logo:', error);\n        throw new Error('فشل في رفع شعار الشركة');\n      }\n    }\n\n    return this.saveSettings({\n      ...currentSettings,\n      company: companySettings\n    });\n  }\n\n  // رفع شعار الشركة\n  async uploadCompanyLogo(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const logoUrl = e.target.result;\n        // حفظ في localStorage مؤقتاً\n        localStorage.setItem('companyLogo', logoUrl);\n        resolve(logoUrl);\n      };\n      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));\n      reader.readAsDataURL(file);\n    });\n  }\n\n  // الحصول على شعار الشركة\n  getCompanyLogo() {\n    return localStorage.getItem('companyLogo');\n  }\n\n  // حفظ إعدادات الفواتير\n  async saveInvoiceSettings(invoiceSettings) {\n    const currentSettings = await this.loadSettings();\n    return this.saveSettings({\n      ...currentSettings,\n      invoice: {\n        ...currentSettings.invoice,\n        ...invoiceSettings\n      }\n    });\n  }\n\n  // حفظ إعدادات عرض الفواتير\n  async saveInvoiceDisplaySettings(displaySettings) {\n    const currentSettings = await this.loadSettings();\n    return this.saveSettings({\n      ...currentSettings,\n      invoice: {\n        ...currentSettings.invoice,\n        display: displaySettings\n      }\n    });\n  }\n}\n\n// إنشاء مثيل واحد للخدمة\nconst settingsService = new SettingsService();\n\nexport default settingsService;\n"], "names": [], "mappings": "AAAA,kCAAkC;AAElC,uBAAuB;;;;AACvB,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB;IAChD;IAEA,kCAAkC;IAClC,qBAAqB;QACnB,OAAO;YACL,SAAS;gBACP,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;gBACV,SAAS;YACX;YACA,SAAS;gBACP,eAAe;gBACf,qBAAqB;gBACrB,gBAAgB;gBAChB,qBAAqB;gBACrB,2BAA2B;gBAC3B,qBAAqB;gBACrB,sBAAsB;gBACtB,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,SAAS;oBACP,cAAc;oBACd,iBAAiB;oBACjB,iBAAiB;oBACjB,oBAAoB;oBACpB,kBAAkB;oBAClB,kBAAkB;oBAClB,oBAAoB;oBACpB,WAAW;oBAEX,mBAAmB;oBACnB,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;oBACb,kBAAkB;oBAElB,iBAAiB;oBACjB,kBAAkB;oBAClB,qBAAqB;oBACrB,mBAAmB;oBACnB,mBAAmB;oBAEnB,eAAe;oBACf,mBAAmB;oBACnB,iBAAiB;oBACjB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,iBAAiB;oBAEjB,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,oBAAoB;oBACpB,iBAAiB;oBAEjB,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,YAAY;oBACZ,iBAAiB;oBACjB,yBAAyB;gBAC3B;YACF;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe;QACnB,IAAI;YACF,qCAAqC;YACrC,MAAM,gBAAgB,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;YAC1D,IAAI,eAAe;gBACjB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC;YAEA,4BAA4B;YAC5B,iDAAiD;YACjD,0CAA0C;YAC1C,2CAA2C;YAE3C,OAAO,IAAI,CAAC,eAAe;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,IAAI,CAAC,eAAe;QAC7B;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa,QAAQ,EAAE;QAC3B,IAAI;YACF,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC;YAE9C,sBAAsB;YACtB,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;YAErD,0BAA0B;YAC1B,iCAAiC;YACjC,oBAAoB;YACpB,qDAAqD;YACrD,yCAAyC;YACzC,MAAM;YAEN,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,kBAAkB,QAAQ,EAAE;QAC1B,OAAO;YACL,SAAS;gBAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO;gBAAE,GAAG,SAAS,OAAO;YAAC;YAChE,SAAS;gBACP,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO;gBAC/B,GAAG,SAAS,OAAO;gBACnB,SAAS;oBACP,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO;oBACvC,GAAG,SAAS,OAAO,EAAE,OAAO;gBAC9B;YACF;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO;IACzB;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO;IACzB;IAEA,kCAAkC;IAClC,MAAM,4BAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,CAAC,OAAO;IACjC;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,eAAe,EAAE;QACzC,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAE/C,oCAAoC;QACpC,IAAI,gBAAgB,QAAQ,EAAE;YAC5B,IAAI;gBACF,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,QAAQ;gBACrE,gBAAgB,OAAO,GAAG;gBAC1B,OAAO,gBAAgB,QAAQ,EAAE,0BAA0B;YAC7D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,CAAC,MAAM;gBAC/B,6BAA6B;gBAC7B,aAAa,OAAO,CAAC,eAAe;gBACpC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,yBAAyB;IACzB,iBAAiB;QACf,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,eAAe,EAAE;QACzC,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAC/C,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;gBACP,GAAG,gBAAgB,OAAO;gBAC1B,GAAG,eAAe;YACpB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,2BAA2B,eAAe,EAAE;QAChD,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;QAC/C,OAAO,IAAI,CAAC,YAAY,CAAC;YACvB,GAAG,eAAe;YAClB,SAAS;gBACP,GAAG,gBAAgB,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;AACF;AAEA,yBAAyB;AACzB,MAAM,kBAAkB,IAAI;uCAEb", "debugId": null}}, {"offset": {"line": 6219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/hooks/useInvoiceSettings.js"], "sourcesContent": ["// frontend/components/invoice-templates/hooks/useInvoiceSettings.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport settingsService from '@/lib/settingsService';\n\nexport function useInvoiceSettings() {\n  const [companySettings, setCompanySettings] = useState(null);\n  const [invoiceSettings, setInvoiceSettings] = useState(null);\n  const [displaySettings, setDisplaySettings] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  const loadSettings = async () => {\n    try {\n      setLoading(true);\n      const [company, invoice, display] = await Promise.all([\n        settingsService.getCompanySettings(),\n        settingsService.getInvoiceSettings(),\n        settingsService.getInvoiceDisplaySettings()\n      ]);\n\n      setCompanySettings(company);\n      setInvoiceSettings(invoice);\n      setDisplaySettings(display);\n    } catch (error) {\n      console.error('Error loading settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    companySettings,\n    invoiceSettings,\n    displaySettings,\n    loading,\n    refreshSettings: loadSettings\n  };\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;AAGpE;AACA;AAHA;;;AAKO,SAAS;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,SAAS,SAAS,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,sHAAA,CAAA,UAAe,CAAC,kBAAkB;gBAClC,sHAAA,CAAA,UAAe,CAAC,kBAAkB;gBAClC,sHAAA,CAAA,UAAe,CAAC,yBAAyB;aAC1C;YAED,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 6267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/SmartTemplate.js"], "sourcesContent": ["// frontend/components/invoice-templates/SmartTemplate.js\n'use client';\n\nimport React from 'react';\nimport { useInvoiceSettings } from './hooks/useInvoiceSettings';\nimport settingsService from '@/lib/settingsService';\n\nexport default function SmartTemplate({ invoice, templateComponent: TemplateComponent, preview = false }) {\n  const { companySettings, invoiceSettings, displaySettings, loading } = useInvoiceSettings();\n\n  if (loading) {\n    return (\n      <div className=\"bg-white p-8 flex items-center justify-center\" style={{ minHeight: '297mm', width: '210mm' }}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري تحميل الإعدادات...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // دمج بيانات الفاتورة مع إعدادات الشركة\n  const enhancedInvoice = {\n    ...invoice,\n    // معلومات الشركة من الإعدادات (بدون قيم افتراضية)\n    companyName: companySettings?.companyName || '',\n    companyDetails: companySettings?.companyAddress || '',\n    companyAddress: companySettings?.companyAddress || '',\n    companyPhone: companySettings?.companyPhone || '',\n    companyEmail: companySettings?.companyEmail || '',\n    companyWebsite: companySettings?.companyWebsite || '',\n    taxId: companySettings?.taxId || '',\n    logoUrl: companySettings?.logoUrl || settingsService.getCompanyLogo() || null,\n\n    // إعدادات الفاتورة\n    currency: companySettings?.currency || 'SAR',\n    taxRate: invoiceSettings?.defaultTaxRate || 15,\n    paymentTerms: invoiceSettings?.defaultPaymentTerms || '',\n    invoiceFooter: invoiceSettings?.invoiceFooter || '',\n    invoiceNotes: invoice?.notes || invoiceSettings?.invoiceNotes || '',\n\n    // إعدادات العرض\n    displaySettings: displaySettings || {}\n  };\n\n  return (\n    <TemplateComponent\n      invoice={enhancedInvoice}\n      preview={preview}\n      displaySettings={displaySettings}\n    />\n  );\n}\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;AAGzD;AACA;AACA;AAJA;;;;;AAMe,SAAS,cAAc,EAAE,OAAO,EAAE,mBAAmB,iBAAiB,EAAE,UAAU,KAAK,EAAE;IACtG,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD;IAExF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;YAAgD,OAAO;gBAAE,WAAW;gBAAS,OAAO;YAAQ;sBACzG,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,GAAG,OAAO;QACV,kDAAkD;QAClD,aAAa,iBAAiB,eAAe;QAC7C,gBAAgB,iBAAiB,kBAAkB;QACnD,gBAAgB,iBAAiB,kBAAkB;QACnD,cAAc,iBAAiB,gBAAgB;QAC/C,cAAc,iBAAiB,gBAAgB;QAC/C,gBAAgB,iBAAiB,kBAAkB;QACnD,OAAO,iBAAiB,SAAS;QACjC,SAAS,iBAAiB,WAAW,sHAAA,CAAA,UAAe,CAAC,cAAc,MAAM;QAEzE,mBAAmB;QACnB,UAAU,iBAAiB,YAAY;QACvC,SAAS,iBAAiB,kBAAkB;QAC5C,cAAc,iBAAiB,uBAAuB;QACtD,eAAe,iBAAiB,iBAAiB;QACjD,cAAc,SAAS,SAAS,iBAAiB,gBAAgB;QAEjE,gBAAgB;QAChB,iBAAiB,mBAAmB,CAAC;IACvC;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,SAAS;QACT,iBAAiB;;;;;;AAGvB", "debugId": null}}, {"offset": {"line": 6356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/invoice-templates/InvoiceRenderer.js"], "sourcesContent": ["// frontend/components/invoice-templates/InvoiceRenderer.js\n'use client';\n\nimport React from 'react';\nimport ClassicTemplate from './ClassicTemplate';\nimport EnhancedClassicTemplate from './EnhancedClassicTemplate';\nimport RealDataTemplate from './RealDataTemplate';\nimport ModernTemplate from './ModernTemplate';\nimport ElegantTemplate from './ElegantTemplate';\nimport MinimalTemplate from './MinimalTemplate';\nimport SmartTemplate from './SmartTemplate';\n\nconst templateComponents = {\n  classic: RealDataTemplate, // استخدام القالب الذي يعتمد على البيانات الحقيقية\n  modern: ModernTemplate,\n  elegant: ElegantTemplate,\n  minimal: MinimalTemplate\n};\n\nexport default function InvoiceRenderer({ invoice, templateId, preview = false }) {\n  // Get template from props or localStorage\n  const selectedTemplate = templateId || localStorage.getItem('selectedInvoiceTemplate') || 'classic';\n\n  // Get the template component\n  const TemplateComponent = templateComponents[selectedTemplate] || RealDataTemplate;\n\n  if (!TemplateComponent) {\n    return (\n      <div className=\"bg-white p-8 text-center\">\n        <p className=\"text-red-600\">خطأ: القالب المحدد غير موجود</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"invoice-container\">\n      <SmartTemplate\n        invoice={invoice}\n        templateComponent={TemplateComponent}\n        preview={preview}\n      />\n    </div>\n  );\n}\n\n// Export template components for direct use\nexport {\n  ClassicTemplate,\n  ModernTemplate,\n  ElegantTemplate,\n  MinimalTemplate,\n  templateComponents\n};\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;AAG3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,qBAAqB;IACzB,SAAS,sJAAA,CAAA,UAAgB;IACzB,QAAQ,oJAAA,CAAA,UAAc;IACtB,SAAS,qJAAA,CAAA,UAAe;IACxB,SAAS,qJAAA,CAAA,UAAe;AAC1B;AAEe,SAAS,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,KAAK,EAAE;IAC9E,0CAA0C;IAC1C,MAAM,mBAAmB,cAAc,aAAa,OAAO,CAAC,8BAA8B;IAE1F,6BAA6B;IAC7B,MAAM,oBAAoB,kBAAkB,CAAC,iBAAiB,IAAI,sJAAA,CAAA,UAAgB;IAElF,IAAI,CAAC,mBAAmB;QACtB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAe;;;;;;;;;;;IAGlC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mJAAA,CAAA,UAAa;YACZ,SAAS;YACT,mBAAmB;YACnB,SAAS;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 6449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  return apiRequest(`${API_BASE_URL}/customers`, {\n    method: 'POST',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function getCustomerById(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, { method: 'GET' }, true);\n}\n\nexport async function updateCustomer(id, customerData) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function deleteCustomer(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'DELETE',\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QAAE,QAAQ;IAAM,GAAG;AAC1E;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 6872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoices/create/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\nimport TemplateSelector from '@/components/invoice-templates/TemplateSelector';\nimport InvoiceRenderer from '@/components/invoice-templates/InvoiceRenderer';\nimport { templates } from '@/components/invoice-templates/TemplatePreview';\nimport { getCustomers, getProducts } from '@/lib/api';\n\nfunction CreateInvoicePage() {\n  const [step, setStep] = useState(1); // 1: Details, 2: Preview\n  const [selectedTemplate, setSelectedTemplate] = useState('classic');\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [invoiceData, setInvoiceData] = useState({\n    customerId: '',\n    customerName: '',\n    customerDetails: '',\n    items: [],\n    notes: '',\n    dueDate: '',\n    subtotal: 0,\n    tax: 0,\n    total: 0\n  });\n\n  const router = useRouter();\n\n  useEffect(() => {\n    loadData();\n    // Load saved template from localStorage\n    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');\n    if (savedTemplate) {\n      setSelectedTemplate(savedTemplate);\n    }\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [customersData, productsData] = await Promise.all([\n        getCustomers(),\n        getProducts()\n      ]);\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (err) {\n      setError('فشل في تحميل البيانات');\n      console.error('Error loading data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCustomerChange = (customerId) => {\n    const customer = customers.find(c => c.id === parseInt(customerId));\n    setInvoiceData(prev => ({\n      ...prev,\n      customerId,\n      customerName: customer?.name || '',\n      customerDetails: customer?.companyName || customer?.email || ''\n    }));\n  };\n\n  const addItem = () => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        id: Date.now(),\n        productId: '',\n        description: '',\n        quantity: 1,\n        price: 0,\n        total: 0\n      }]\n    }));\n  };\n\n  const updateItem = (itemId, field, value) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.map(item => {\n        if (item.id === itemId) {\n          const updatedItem = { ...item, [field]: value };\n\n          // Auto-fill from product if productId is selected\n          if (field === 'productId' && value) {\n            const product = products.find(p => p.id === parseInt(value));\n            if (product) {\n              updatedItem.description = product.name;\n              updatedItem.price = Number(product.price) || 0;\n            }\n          }\n\n          // Ensure numeric values\n          if (field === 'quantity') {\n            updatedItem.quantity = Number(value) || 0;\n          }\n          if (field === 'price') {\n            updatedItem.price = Number(value) || 0;\n          }\n\n          // Calculate total\n          updatedItem.total = Number(updatedItem.quantity || 0) * Number(updatedItem.price || 0);\n\n          return updatedItem;\n        }\n        return item;\n      })\n    }));\n  };\n\n  const removeItem = (itemId) => {\n    setInvoiceData(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n  };\n\n  const calculateTotals = () => {\n    const subtotal = invoiceData.items.reduce((sum, item) => sum + (Number(item.total) || 0), 0);\n    const tax = subtotal * 0.15; // 15% VAT\n    const total = subtotal + tax;\n\n    setInvoiceData(prev => ({\n      ...prev,\n      subtotal: Number(subtotal) || 0,\n      tax: Number(tax) || 0,\n      total: Number(total) || 0\n    }));\n  };\n\n  useEffect(() => {\n    calculateTotals();\n  }, [invoiceData.items]);\n\n  const nextStep = () => {\n    if (step < 2) {\n      setStep(step + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (step > 1) {\n      setStep(step - 1);\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      // Here you would call the API to create the invoice\n      // await createInvoice({ ...invoiceData, templateId: selectedTemplate });\n\n      // For now, just simulate success\n      setTimeout(() => {\n        router.push('/dashboard/invoices');\n      }, 1000);\n    } catch (err) {\n      setError('فشل في إنشاء الفاتورة');\n      console.error('Error creating invoice:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">تفاصيل الفاتورة</h2>\n              <p className=\"text-gray-600\">أدخل بيانات الفاتورة والعناصر</p>\n            </div>\n\n            {/* Customer Selection */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات العميل</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    اختر العميل\n                  </label>\n                  <select\n                    value={invoiceData.customerId}\n                    onChange={(e) => handleCustomerChange(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required\n                  >\n                    <option value=\"\">اختر عميل...</option>\n                    {customers.map(customer => (\n                      <option key={customer.id} value={customer.id}>\n                        {customer.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    تاريخ الاستحقاق\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={invoiceData.dueDate}\n                    onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Items */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">عناصر الفاتورة</h3>\n                <button\n                  onClick={addItem}\n                  className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\"\n                >\n                  إضافة عنصر\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {invoiceData.items.map((item, index) => (\n                  <div key={item.id} className=\"grid grid-cols-12 gap-4 items-end\">\n                    <div className=\"col-span-4\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        المنتج\n                      </label>\n                      <select\n                        value={item.productId}\n                        onChange={(e) => updateItem(item.id, 'productId', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"\">اختر منتج...</option>\n                        {products.map(product => (\n                          <option key={product.id} value={product.id}>\n                            {product.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        الكمية\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={item.quantity}\n                        onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        min=\"1\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        السعر\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={item.price}\n                        onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        step=\"0.01\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        الإجمالي\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={(Number(item.total) || 0).toFixed(2)}\n                        readOnly\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50\"\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <button\n                        onClick={() => removeItem(item.id)}\n                        className=\"w-full bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors\"\n                      >\n                        حذف\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {invoiceData.items.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  لا توجد عناصر. اضغط \"إضافة عنصر\" لبدء إضافة المنتجات.\n                </div>\n              )}\n            </div>\n\n            {/* Notes */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملاحظات</h3>\n              <textarea\n                value={invoiceData.notes}\n                onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"ملاحظات إضافية...\"\n              />\n            </div>\n\n            {/* Totals Summary */}\n            {invoiceData.items.length > 0 && (\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملخص المبالغ</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{(Number(invoiceData.subtotal) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>ضريبة القيمة المضافة (15%):</span>\n                    <span>{(Number(invoiceData.tax) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                  <div className=\"flex justify-between font-bold text-lg border-t pt-2\">\n                    <span>الإجمالي:</span>\n                    <span>{(Number(invoiceData.total) || 0).toFixed(2)} ر.س</span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">معاينة الفاتورة</h2>\n              <p className=\"text-gray-600\">راجع الفاتورة قبل الحفظ</p>\n            </div>\n\n            <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n              <div className=\"transform scale-75 origin-top\">\n                <InvoiceRenderer\n                  invoice={{\n                    invoiceNumber: 'INV-' + Date.now(),\n                    date: new Date().toLocaleDateString('ar-SA'),\n                    customerName: invoiceData.customerName,\n                    customerDetails: invoiceData.customerDetails,\n                    items: invoiceData.items,\n                    subtotal: invoiceData.subtotal,\n                    tax: invoiceData.tax,\n                    total: invoiceData.total,\n                    notes: invoiceData.notes || 'شكراً لكم...',\n                    companyName: 'شركتك',\n                    companyDetails: 'تفاصيل شركتك'\n                  }}\n                  templateId={selectedTemplate}\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <nav className=\"flex\" aria-label=\"Breadcrumb\">\n                  <ol className=\"flex items-center space-x-4\">\n                    <li>\n                      <Link href=\"/dashboard\" className=\"text-gray-400 hover:text-gray-500\">\n                        <svg className=\"flex-shrink-0 h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n                        </svg>\n                      </Link>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <Link href=\"/dashboard/invoices\" className=\"ml-4 text-sm font-medium text-gray-500 hover:text-gray-700\">\n                          الفواتير\n                        </Link>\n                      </div>\n                    </li>\n                    <li>\n                      <div className=\"flex items-center\">\n                        <svg className=\"flex-shrink-0 h-5 w-5 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        <span className=\"ml-4 text-sm font-medium text-gray-900\">إنشاء فاتورة جديدة</span>\n                      </div>\n                    </li>\n                  </ol>\n                </nav>\n                <h1 className=\"mt-2 text-3xl font-bold text-gray-900\">إنشاء فاتورة جديدة</h1>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Current Template Info */}\n          <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n                </svg>\n                <span className=\"text-blue-800 font-medium\">\n                  القالب المحدد: <span className=\"font-bold\">{templates[selectedTemplate]?.name || 'القالب الكلاسيكي'}</span>\n                </span>\n              </div>\n              <Link\n                href=\"/dashboard/invoice-templates\"\n                className=\"text-blue-600 hover:text-blue-800 text-sm font-medium underline\"\n              >\n                تغيير القالب\n              </Link>\n            </div>\n          </div>\n\n          {/* Progress Steps */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-center\">\n              {[1, 2].map((stepNumber) => (\n                <div key={stepNumber} className=\"flex items-center\">\n                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${\n                    step >= stepNumber\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {stepNumber}\n                  </div>\n                  <div className={`ml-4 ${step >= stepNumber ? 'text-blue-600' : 'text-gray-500'}`}>\n                    {stepNumber === 1 && 'تفاصيل الفاتورة'}\n                    {stepNumber === 2 && 'المعاينة'}\n                  </div>\n                  {stepNumber < 2 && (\n                    <div className={`w-16 h-1 mx-4 ${\n                      step > stepNumber ? 'bg-blue-500' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\n              <p className=\"text-red-800\">{error}</p>\n            </div>\n          )}\n\n          {/* Step Content */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\">\n            {renderStepContent()}\n          </div>\n\n          {/* Navigation Buttons */}\n          <div className=\"flex justify-between\">\n            <div>\n              {step > 1 && (\n                <button\n                  onClick={prevStep}\n                  className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  السابق\n                </button>\n              )}\n            </div>\n\n            <div className=\"flex gap-4\">\n              <Link\n                href=\"/dashboard/invoices\"\n                className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                إلغاء\n              </Link>\n\n              {step < 2 ? (\n                <button\n                  onClick={nextStep}\n                  disabled={step === 1 && (invoiceData.items.length === 0 || !invoiceData.customerId)}\n                  className=\"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                >\n                  التالي\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={loading}\n                  className=\"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                >\n                  {loading ? 'جاري الحفظ...' : 'حفظ الفاتورة'}\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(CreateInvoicePage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,SAAS;IACP,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,yBAAyB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,OAAO,EAAE;QACT,OAAO;QACP,SAAS;QACT,UAAU;QACV,KAAK;QACL,OAAO;IACT;IAEA,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,wCAAwC;QACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,MAAM,CAAC,eAAe,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,CAAA,GAAA,0GAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,0GAAA,CAAA,cAAW,AAAD;aACX;YACD,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;QACvD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;gBACA,cAAc,UAAU,QAAQ;gBAChC,iBAAiB,UAAU,eAAe,UAAU,SAAS;YAC/D,CAAC;IACH;IAEA,MAAM,UAAU;QACd,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO;uBAAI,KAAK,KAAK;oBAAE;wBACrB,IAAI,KAAK,GAAG;wBACZ,WAAW;wBACX,aAAa;wBACb,UAAU;wBACV,OAAO;wBACP,OAAO;oBACT;iBAAE;YACJ,CAAC;IACH;IAEA,MAAM,aAAa,CAAC,QAAQ,OAAO;QACjC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA;oBACpB,IAAI,KAAK,EAAE,KAAK,QAAQ;wBACtB,MAAM,cAAc;4BAAE,GAAG,IAAI;4BAAE,CAAC,MAAM,EAAE;wBAAM;wBAE9C,kDAAkD;wBAClD,IAAI,UAAU,eAAe,OAAO;4BAClC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BACrD,IAAI,SAAS;gCACX,YAAY,WAAW,GAAG,QAAQ,IAAI;gCACtC,YAAY,KAAK,GAAG,OAAO,QAAQ,KAAK,KAAK;4BAC/C;wBACF;wBAEA,wBAAwB;wBACxB,IAAI,UAAU,YAAY;4BACxB,YAAY,QAAQ,GAAG,OAAO,UAAU;wBAC1C;wBACA,IAAI,UAAU,SAAS;4BACrB,YAAY,KAAK,GAAG,OAAO,UAAU;wBACvC;wBAEA,kBAAkB;wBAClB,YAAY,KAAK,GAAG,OAAO,YAAY,QAAQ,IAAI,KAAK,OAAO,YAAY,KAAK,IAAI;wBAEpF,OAAO;oBACT;oBACA,OAAO;gBACT;YACF,CAAC;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC/C,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW,YAAY,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG;QAC1F,MAAM,MAAM,WAAW,MAAM,UAAU;QACvC,MAAM,QAAQ,WAAW;QAEzB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,UAAU,OAAO,aAAa;gBAC9B,KAAK,OAAO,QAAQ;gBACpB,OAAO,OAAO,UAAU;YAC1B,CAAC;IACH;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,WAAW;QACf,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,WAAW;QACf,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,oDAAoD;YACpD,yEAAyE;YAEzE,iCAAiC;YACjC,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAI/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO,YAAY,UAAU;oDAC7B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;gEAAyB,OAAO,SAAS,EAAE;0EACzC,SAAS,IAAI;+DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;sDAO9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,OAAO;oDAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO,KAAK,SAAS;4DACrB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAChE,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;wEAAwB,OAAO,QAAQ,EAAE;kFACvC,QAAQ,IAAI;uEADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;8DAO7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,KAAK,QAAQ;4DACpB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC7E,WAAU;4DACV,KAAI;;;;;;;;;;;;8DAIR,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,KAAK,KAAK;4DACjB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC5E,WAAU;4DACV,MAAK;;;;;;;;;;;;8DAIT,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;4DACzC,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,WAAW,KAAK,EAAE;wDACjC,WAAU;kEACX;;;;;;;;;;;;2CA7DK,KAAK,EAAE;;;;;;;;;;gCAqEpB,YAAY,KAAK,CAAC,MAAM,KAAK,mBAC5B,8OAAC;oCAAI,WAAU;8CAAiC;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCACC,OAAO,YAAY,KAAK;oCACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC3E,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,YAAY,KAAK,CAAC,MAAM,GAAG,mBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,CAAC,OAAO,YAAY,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,CAAC,OAAO,YAAY,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,CAAC,OAAO,YAAY,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQjE,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qKAAA,CAAA,UAAe;oCACd,SAAS;wCACP,eAAe,SAAS,KAAK,GAAG;wCAChC,MAAM,IAAI,OAAO,kBAAkB,CAAC;wCACpC,cAAc,YAAY,YAAY;wCACtC,iBAAiB,YAAY,eAAe;wCAC5C,OAAO,YAAY,KAAK;wCACxB,UAAU,YAAY,QAAQ;wCAC9B,KAAK,YAAY,GAAG;wCACpB,OAAO,YAAY,KAAK;wCACxB,OAAO,YAAY,KAAK,IAAI;wCAC5B,aAAa;wCACb,gBAAgB;oCAClB;oCACA,YAAY;;;;;;;;;;;;;;;;;;;;;;YAOxB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;wCAAO,cAAW;kDAC/B,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAChC,cAAA,8OAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8DAId,8OAAC;8DACC,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAsB,WAAU;0EAA6D;;;;;;;;;;;;;;;;;8DAK5G,8OAAC;8DACC,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAAsC,MAAK;gEAAe,SAAQ;0EAC/E,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;0EAE3J,8OAAC;gEAAK,WAAU;0EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjE,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAK,WAAU;;oDAA4B;kEAC3B,8OAAC;wDAAK,WAAU;kEAAa,qJAAA,CAAA,YAAS,CAAC,iBAAiB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;kDAGrF,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,2BACX,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAW,CAAC,sEAAsE,EACrF,QAAQ,aACJ,2BACA,6BACJ;0DACC;;;;;;0DAEH,8OAAC;gDAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,aAAa,kBAAkB,iBAAiB;;oDAC7E,eAAe,KAAK;oDACpB,eAAe,KAAK;;;;;;;4CAEtB,aAAa,mBACZ,8OAAC;gDAAI,WAAW,CAAC,cAAc,EAC7B,OAAO,aAAa,gBAAgB,eACpC;;;;;;;uCAfI;;;;;;;;;;;;;;;wBAuBf,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAKjC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CACE,OAAO,mBACN,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;wCAIA,OAAO,kBACN,8OAAC;4CACC,SAAS;4CACT,UAAU,SAAS,KAAK,CAAC,YAAY,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,UAAU;4CAClF,WAAU;sDACX;;;;;iEAID,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}