// frontend/components/invoice-templates/SmartTemplate.js
'use client';

import React from 'react';
import { useInvoiceSettings } from './hooks/useInvoiceSettings';
import settingsService from '@/lib/settingsService';

export default function SmartTemplate({ invoice, templateComponent: TemplateComponent, preview = false }) {
  const { companySettings, invoiceSettings, displaySettings, loading } = useInvoiceSettings();

  if (loading) {
    return (
      <div className="bg-white p-8 flex items-center justify-center" style={{ minHeight: '297mm', width: '210mm' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  // دمج بيانات الفاتورة مع إعدادات الشركة
  const enhancedInvoice = {
    ...invoice,
    // معلومات الشركة من الإعدادات (بدون قيم افتراضية)
    companyName: companySettings?.companyName || '',
    companyDetails: companySettings?.companyAddress || '',
    companyAddress: companySettings?.companyAddress || '',
    companyPhone: companySettings?.companyPhone || '',
    companyEmail: companySettings?.companyEmail || '',
    companyWebsite: companySettings?.companyWebsite || '',
    taxId: companySettings?.taxId || '',
    logoUrl: companySettings?.logoUrl || settingsService.getCompanyLogo() || null,

    // إعدادات الفاتورة
    currency: companySettings?.currency || 'SAR',
    taxRate: invoiceSettings?.defaultTaxRate || 15,
    paymentTerms: invoiceSettings?.defaultPaymentTerms || '',
    invoiceFooter: invoiceSettings?.invoiceFooter || '',
    invoiceNotes: invoice?.notes || invoiceSettings?.invoiceNotes || '',

    // إعدادات العرض
    displaySettings: displaySettings || {}
  };

  return (
    <TemplateComponent
      invoice={enhancedInvoice}
      preview={preview}
      displaySettings={displaySettings}
    />
  );
}
