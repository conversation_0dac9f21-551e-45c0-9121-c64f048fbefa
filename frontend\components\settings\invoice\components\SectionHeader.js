// frontend/components/settings/invoice/components/SectionHeader.js
'use client';

export default function SectionHeader({ title, icon, section, onSelectAll, onDeselectAll, children }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {icon}
            <h3 className="text-lg font-semibold text-white mr-3">{title}</h3>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => onSelectAll(section)}
              className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors"
            >
              تحديد الكل
            </button>
            <button
              type="button"
              onClick={() => onDeselectAll(section)}
              className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors"
            >
              إلغاء الكل
            </button>
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {children}
        </div>
      </div>
    </div>
  );
}
