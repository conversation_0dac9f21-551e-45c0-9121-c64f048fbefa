// frontend/components/settings/invoice/hooks/useInvoiceDisplaySettings.js
'use client';

import { useState } from 'react';
import { defaultInvoiceDisplaySettings, sectionFields } from '../data/defaultSettings';

export function useInvoiceDisplaySettings(initialSettings) {
  const [formData, setFormData] = useState({
    ...defaultInvoiceDisplaySettings,
    ...initialSettings
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSelectAll = (section) => {
    const fields = sectionFields[section] || [];
    const newData = { ...formData };
    fields.forEach(field => {
      newData[field] = true;
    });
    setFormData(newData);
  };

  const handleDeselectAll = (section) => {
    const fields = sectionFields[section] || [];
    const newData = { ...formData };
    fields.forEach(field => {
      newData[field] = false;
    });
    setFormData(newData);
  };

  const handleReset = () => {
    setFormData({
      ...defaultInvoiceDisplaySettings,
      ...initialSettings
    });
  };

  return {
    formData,
    handleInputChange,
    handleSelectAll,
    handleDeselectAll,
    handleReset
  };
}
