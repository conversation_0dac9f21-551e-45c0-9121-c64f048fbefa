{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user'));\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  const response = await fetch(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(credentials),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to login');\n  }\n  return response.json();\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching products:', error);\n    throw error;\n  }\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customers:', error);\n    throw error;\n  }\n}\n\nexport async function createCustomer(customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating customer:', error);\n    throw error;\n  }\n}\n\nexport async function getCustomerById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching customer by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateCustomer(id, customerData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(customerData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating customer:', error);\n    throw error;\n  }\n}\n\nexport async function deleteCustomer(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/customers/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting customer:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;AACtB,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;IAC7C,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAMO,eAAe,MAAM,WAAW;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;QACzD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,YAAY;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;YAC9D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/ImageUpload.js"], "sourcesContent": ["// frontend/components/ImageUpload.js\n'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { uploadProductImage, deleteProductImage, setMainImage } from '@/lib/api';\n\nexport default function ImageUpload({ productId, images = [], onImagesChange }) {\n  const [uploading, setUploading] = useState(false);\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef(null);\n\n  const handleFileSelect = async (files) => {\n    if (!files || files.length === 0) return;\n\n    setUploading(true);\n    try {\n      const uploadPromises = Array.from(files).map(file => \n        uploadProductImage(productId, file)\n      );\n      \n      const results = await Promise.all(uploadPromises);\n      \n      // تحديث قائمة الصور\n      if (onImagesChange) {\n        const newImages = results.map(result => result.image);\n        onImagesChange([...images, ...newImages]);\n      }\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      alert('فشل في رفع الصور: ' + error.message);\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n    const files = e.dataTransfer.files;\n    handleFileSelect(files);\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const handleFileInputChange = (e) => {\n    handleFileSelect(e.target.files);\n  };\n\n  const handleDeleteImage = async (imageId) => {\n    if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) return;\n\n    try {\n      await deleteProductImage(imageId);\n      \n      // تحديث قائمة الصور\n      if (onImagesChange) {\n        const updatedImages = images.filter(img => img.id !== imageId);\n        onImagesChange(updatedImages);\n      }\n    } catch (error) {\n      console.error('Error deleting image:', error);\n      alert('فشل في حذف الصورة: ' + error.message);\n    }\n  };\n\n  const handleSetMainImage = async (imageId) => {\n    try {\n      await setMainImage(imageId);\n      \n      // تحديث قائمة الصور\n      if (onImagesChange) {\n        const updatedImages = images.map(img => ({\n          ...img,\n          isMain: img.id === imageId\n        }));\n        onImagesChange(updatedImages);\n      }\n    } catch (error) {\n      console.error('Error setting main image:', error);\n      alert('فشل في تحديد الصورة الرئيسية: ' + error.message);\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* منطقة رفع الصور */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n          dragOver \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onClick={() => fileInputRef.current?.click()}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\"image/*\"\n          onChange={handleFileInputChange}\n          className=\"hidden\"\n        />\n        \n        {uploading ? (\n          <div className=\"flex items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <span className=\"ml-2\">جاري رفع الصور...</span>\n          </div>\n        ) : (\n          <div>\n            <svg className=\"mx-auto h-12 w-12 text-gray-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\n              <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n            <p className=\"mt-2 text-sm text-gray-600\">\n              <span className=\"font-medium text-blue-600 hover:text-blue-500 cursor-pointer\">\n                اضغط لرفع الصور\n              </span>\n              {' '}أو اسحب الصور هنا\n            </p>\n            <p className=\"text-xs text-gray-500\">PNG, JPG, GIF حتى 5MB</p>\n          </div>\n        )}\n      </div>\n\n      {/* عرض الصور المرفوعة */}\n      {images.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n          {images.map((image) => (\n            <div key={image.id} className=\"relative group\">\n              <div className=\"aspect-square relative overflow-hidden rounded-lg border\">\n                <Image\n                  src={`http://localhost:5000/${image.path}`}\n                  alt={image.originalName}\n                  fill\n                  className=\"object-cover\"\n                />\n                \n                {/* شارة الصورة الرئيسية */}\n                {image.isMain && (\n                  <div className=\"absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium\">\n                    رئيسية\n                  </div>\n                )}\n                \n                {/* أزرار التحكم */}\n                <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2\">\n                  {!image.isMain && (\n                    <button\n                      onClick={() => handleSetMainImage(image.id)}\n                      className=\"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm\"\n                    >\n                      جعل رئيسية\n                    </button>\n                  )}\n                  <button\n                    onClick={() => handleDeleteImage(image.id)}\n                    className=\"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\"\n                  >\n                    حذف\n                  </button>\n                </div>\n              </div>\n              \n              <p className=\"mt-1 text-xs text-gray-500 truncate\">\n                {image.originalName}\n              </p>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;AAGrC;AACA;AACA;AAJA;;;;;AAMe,SAAS,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,cAAc,EAAE;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA,OAC3C,CAAA,GAAA,0GAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YAGhC,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAElC,oBAAoB;YACpB,IAAI,gBAAgB;gBAClB,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK;gBACpD,eAAe;uBAAI;uBAAW;iBAAU;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,uBAAuB,MAAM,OAAO;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,YAAY;QACZ,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,iBAAiB,EAAE,MAAM,CAAC,KAAK;IACjC;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,oCAAoC;QAEjD,IAAI;YACF,MAAM,CAAA,GAAA,0GAAA,CAAA,qBAAkB,AAAD,EAAE;YAEzB,oBAAoB;YACpB,IAAI,gBAAgB;gBAClB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBACtD,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,wBAAwB,MAAM,OAAO;QAC7C;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,CAAA,GAAA,0GAAA,CAAA,eAAY,AAAD,EAAE;YAEnB,oBAAoB;YACpB,IAAI,gBAAgB;gBAClB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;wBACvC,GAAG,GAAG;wBACN,QAAQ,IAAI,EAAE,KAAK;oBACrB,CAAC;gBACD,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,mCAAmC,MAAM,OAAO;QACxD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,WACI,+BACA,yCACJ;gBACF,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,SAAS,IAAM,aAAa,OAAO,EAAE;;kCAErC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;oBAGX,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;6CAGzB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;gCAAkC,QAAO;gCAAe,MAAK;gCAAO,SAAQ;0CACzF,cAAA,8OAAC;oCAAK,GAAE;oCAAyL,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;0CAExP,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAA+D;;;;;;oCAG9E;oCAAI;;;;;;;0CAEP,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAM1C,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,CAAC,sBAAsB,EAAE,MAAM,IAAI,EAAE;wCAC1C,KAAK,MAAM,YAAY;wCACvB,IAAI;wCACJ,WAAU;;;;;;oCAIX,MAAM,MAAM,kBACX,8OAAC;wCAAI,WAAU;kDAAsF;;;;;;kDAMvG,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,MAAM,MAAM,kBACZ,8OAAC;gDACC,SAAS,IAAM,mBAAmB,MAAM,EAAE;gDAC1C,WAAU;0DACX;;;;;;0DAIH,8OAAC;gDACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;gDACzC,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAE,WAAU;0CACV,MAAM,YAAY;;;;;;;uBApCb,MAAM,EAAE;;;;;;;;;;;;;;;;AA4C9B", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/products/edit/%5Bid%5D/page.js"], "sourcesContent": ["// frontend/app/dashboard/products/edit/[id]/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport { getProductById, updateProduct } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport ImageUpload from '@/components/ImageUpload';\n\nfunction EditProductPage() {\n  const [productData, setProductData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    minStock: '',\n    category: '',\n    supplier: '',\n    barcode: '',\n    isActive: true,\n  });\n  const [images, setImages] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const router = useRouter();\n  const params = useParams();\n  const productId = params.id;\n\n  useEffect(() => {\n    loadProduct();\n  }, [productId]);\n\n  const loadProduct = async () => {\n    try {\n      const product = await getProductById(productId);\n      setProductData({\n        name: product.name || '',\n        description: product.description || '',\n        price: product.price || '',\n        stock: product.stock || '',\n        minStock: product.minStock || '',\n        category: product.category || '',\n        supplier: product.supplier || '',\n        barcode: product.barcode || '',\n        isActive: product.isActive !== false,\n      });\n      setImages(product.images || []);\n    } catch (err) {\n      setError('فشل في تحميل بيانات المنتج');\n      console.error('Error loading product:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setProductData((prevData) => ({\n      ...prevData,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(null);\n\n    try {\n      await updateProduct(productId, productData);\n      router.push('/dashboard/products');\n    } catch (err) {\n      setError(err.message || 'فشل في تحديث المنتج. يرجى المحاولة مرة أخرى.');\n      console.error('Product update error:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-xl\">جاري تحميل بيانات المنتج...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h1 className=\"text-3xl font-bold text-gray-800\">تعديل المنتج</h1>\n          <button\n            onClick={() => router.back()}\n            className=\"text-gray-600 hover:text-gray-800\"\n          >\n            ← العودة\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* معلومات المنتج */}\n          <div>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* معلومات المنتج الأساسية */}\n              <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">المعلومات الأساسية</h2>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      اسم المنتج *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={productData.name}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"barcode\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      الباركود\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"barcode\"\n                      name=\"barcode\"\n                      value={productData.barcode}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      الوصف\n                    </label>\n                    <textarea\n                      id=\"description\"\n                      name=\"description\"\n                      value={productData.description}\n                      onChange={handleChange}\n                      rows=\"3\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div>\n                      <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        الفئة\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"category\"\n                        name=\"category\"\n                        value={productData.category}\n                        onChange={handleChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      />\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"supplier\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        المورد\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"supplier\"\n                        name=\"supplier\"\n                        value={productData.supplier}\n                        onChange={handleChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* معلومات السعر والمخزون */}\n              <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">السعر والمخزون</h2>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      السعر *\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={productData.price}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"stock\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      الكمية المتوفرة *\n                    </label>\n                    <input\n                      type=\"number\"\n                      id=\"stock\"\n                      name=\"stock\"\n                      value={productData.stock}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"minStock\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      الحد الأدنى للمخزون\n                    </label>\n                    <input\n                      type=\"number\"\n                      id=\"minStock\"\n                      name=\"minStock\"\n                      value={productData.minStock}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* إعدادات إضافية */}\n              <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">إعدادات إضافية</h2>\n                \n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"isActive\"\n                    name=\"isActive\"\n                    checked={productData.isActive}\n                    onChange={handleChange}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-900\">\n                    المنتج نشط\n                  </label>\n                </div>\n              </div>\n\n              {/* أزرار الحفظ */}\n              <div className=\"flex justify-end space-x-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => router.back()}\n                  className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  إلغاء\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={saving}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\"\n                >\n                  {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* إدارة الصور */}\n          <div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">صور المنتج</h2>\n              <ImageUpload\n                productId={productId}\n                images={images}\n                onImagesChange={setImages}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(EditProductPage);\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AAGpD;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,0GAAA,CAAA,iBAAc,AAAD,EAAE;YACrC,eAAe;gBACb,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;gBACxB,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,KAAK;YACjC;YACA,UAAU,QAAQ,MAAM,IAAI,EAAE;QAChC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,eAAe,CAAC,WAAa,CAAC;gBAC5B,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,UAAU;QACV,SAAS;QAET,IAAI;YACF,MAAM,CAAA,GAAA,0GAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YAC/B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;YACxB,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCACX;;;;;;;;;;;;gBAKF,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;sCACC,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA+C;;;;;;0EAG/E,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,IAAI;gEACvB,UAAU;gEACV,WAAU;gEACV,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,OAAO;gEAC1B,UAAU;gEACV,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAc,WAAU;0EAA+C;;;;;;0EAGtF,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,WAAW;gEAC9B,UAAU;gEACV,MAAK;gEACL,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,YAAY,QAAQ;wEAC3B,UAAU;wEACV,WAAU;;;;;;;;;;;;0EAId,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,YAAY,QAAQ;wEAC3B,UAAU;wEACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,KAAK;gEACxB,UAAU;gEACV,WAAU;gEACV,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,KAAK;gEACxB,UAAU;gEACV,WAAU;gEACV,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA+C;;;;;;0EAGnF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,QAAQ;gEAC3B,UAAU;gEACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,YAAY,QAAQ;wDAC7B,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;kDAO3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI;gDAC1B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;sCACC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC,yHAAA,CAAA,UAAW;wCACV,WAAW;wCACX,QAAQ;wCACR,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}