// backend/test-server.js
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

app.use(cors());
app.use(express.json());

// بيانات تجريبية للإعدادات
let settings = {
  company: {
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    companyWebsite: '',
    taxId: '',
    currency: 'SAR',
    logoUrl: null
  },
  invoice: {
    invoicePrefix: 'INV',
    invoiceNumberLength: 6,
    defaultTaxRate: 15,
    defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    autoGenerateInvoiceNumber: true,
    showImagesInInvoice: true,
    allowPartialPayments: true,
    requireCustomerInfo: true,
    defaultDueDays: 30,
    invoiceFooter: '',
    invoiceNotes: '',
    display: {
      // رأس الفاتورة
      showCompanyLogo: true,
      showCompanyName: true,
      showCompanyAddress: true,
      showCompanyPhone: true,
      showCompanyEmail: true,
      showCompanyWebsite: true,
      showTaxId: true,
      
      // معلومات الفاتورة
      showInvoiceNumber: true,
      showInvoiceDate: true,
      showDueDate: true,
      showPaymentTerms: true,
      
      // معلومات العميل
      showCustomerName: true,
      showCustomerAddress: true,
      showCustomerPhone: true,
      showCustomerEmail: true,
      
      // عناصر الجدول
      showProductImages: true,
      showProductCode: true,
      showProductDescription: true,
      showQuantity: true,
      showUnitPrice: true,
      showDiscount: false,
      showTotalPrice: true,
      showItemNumbers: true,
      
      // المجاميع
      showSubtotal: true,
      showTaxAmount: true,
      showDiscountAmount: false,
      showTotalAmount: true,
      
      // التذييل
      showNotes: true,
      showFooter: true,
      showSignature: false,
      showQRCode: false,
      showBankDetails: false,
      showPaymentInstructions: true
    }
  }
};

// الحصول على جميع الإعدادات
app.get('/api/settings', (req, res) => {
  console.log('📥 GET /api/settings - تحميل الإعدادات');
  res.json(settings);
});

// حفظ إعدادات الشركة
app.post('/api/settings/company', (req, res) => {
  console.log('📤 POST /api/settings/company - حفظ إعدادات الشركة:', req.body);
  
  settings.company = {
    ...settings.company,
    ...req.body
  };
  
  res.json({ 
    message: 'تم حفظ إعدادات الشركة بنجاح', 
    settings: settings.company 
  });
});

// حفظ إعدادات الفواتير العامة
app.post('/api/settings/invoice', (req, res) => {
  console.log('📤 POST /api/settings/invoice - حفظ إعدادات الفواتير:', req.body);
  
  settings.invoice = {
    ...settings.invoice,
    ...req.body,
    display: settings.invoice.display // الحفاظ على إعدادات العرض
  };
  
  res.json({ 
    message: 'تم حفظ إعدادات الفواتير بنجاح', 
    settings: settings.invoice 
  });
});

// حفظ إعدادات عرض الفاتورة
app.post('/api/settings/invoice/display', (req, res) => {
  console.log('📤 POST /api/settings/invoice/display - حفظ إعدادات العرض:', req.body);
  
  settings.invoice.display = {
    ...settings.invoice.display,
    ...req.body
  };
  
  res.json({ 
    message: 'تم حفظ إعدادات عرض الفاتورة بنجاح', 
    settings: settings.invoice.display 
  });
});

// مسار اختبار
app.get('/api', (req, res) => {
  res.json({ message: 'Invoice Settings API is running!' });
});

app.listen(PORT, () => {
  console.log(`🚀 خادم الإعدادات يعمل على المنفذ ${PORT}`);
  console.log(`📡 API متاح على: http://localhost:${PORT}/api`);
  console.log(`⚙️ إعدادات الفواتير: http://localhost:${PORT}/api/settings`);
});
