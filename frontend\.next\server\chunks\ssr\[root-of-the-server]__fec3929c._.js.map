{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;QACF,GAAG;YAAC;SAAO;QAEX,qBAAO,8OAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/CompanySettings.js"], "sourcesContent": ["// frontend/components/settings/CompanySettings.js\n'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\n\nexport default function CompanySettings({ settings, onSave, loading }) {\n  const [formData, setFormData] = useState({\n    companyName: settings?.companyName || '',\n    companyAddress: settings?.companyAddress || '',\n    companyPhone: settings?.companyPhone || '',\n    companyEmail: settings?.companyEmail || '',\n    companyWebsite: settings?.companyWebsite || '',\n    taxId: settings?.taxId || '',\n    currency: settings?.currency || 'USD',\n    logo: null\n  });\n\n  const [logoPreview, setLogoPreview] = useState(settings?.logoUrl || null);\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleLogoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prev => ({ ...prev, logo: file }));\n      const reader = new FileReader();\n      reader.onload = (e) => setLogoPreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // إضافة الملف إلى البيانات إذا كان موجوداً\n    const dataToSave = { ...formData };\n    if (formData.logo) {\n      dataToSave.logoFile = formData.logo;\n    }\n    onSave(dataToSave);\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className=\"bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4\">\n        <div className=\"flex items-center\">\n          <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10\" />\n          </svg>\n          <h2 className=\"text-lg font-semibold text-white\">معلومات الشركة</h2>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* شعار الشركة */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-semibold text-gray-700 mb-3\">شعار الشركة</label>\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"flex-shrink-0\">\n                {logoPreview ? (\n                  <Image\n                    src={logoPreview}\n                    alt=\"Company Logo\"\n                    width={80}\n                    height={80}\n                    className=\"w-20 h-20 rounded-lg object-cover border-2 border-gray-200\"\n                  />\n                ) : (\n                  <div className=\"w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300\">\n                    <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                )}\n              </div>\n              <div className=\"flex-1\">\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleLogoChange}\n                  className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">PNG, JPG, GIF حتى 2MB</p>\n              </div>\n            </div>\n          </div>\n\n          {/* اسم الشركة */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">اسم الشركة *</label>\n            <input\n              type=\"text\"\n              value={formData.companyName}\n              onChange={(e) => handleInputChange('companyName', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n              placeholder=\"اسم شركتك...\"\n              required\n            />\n          </div>\n\n          {/* الرقم الضريبي */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">الرقم الضريبي</label>\n            <input\n              type=\"text\"\n              value={formData.taxId}\n              onChange={(e) => handleInputChange('taxId', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n              placeholder=\"*********...\"\n            />\n          </div>\n\n          {/* عنوان الشركة */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">عنوان الشركة</label>\n            <textarea\n              value={formData.companyAddress}\n              onChange={(e) => handleInputChange('companyAddress', e.target.value)}\n              rows=\"3\"\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none\"\n              placeholder=\"العنوان الكامل للشركة...\"\n            />\n          </div>\n\n          {/* رقم الهاتف */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">رقم الهاتف</label>\n            <input\n              type=\"tel\"\n              value={formData.companyPhone}\n              onChange={(e) => handleInputChange('companyPhone', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n              placeholder=\"+966 50 123 4567\"\n            />\n          </div>\n\n          {/* البريد الإلكتروني */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">البريد الإلكتروني</label>\n            <input\n              type=\"email\"\n              value={formData.companyEmail}\n              onChange={(e) => handleInputChange('companyEmail', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n\n          {/* الموقع الإلكتروني */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n              الموقع الإلكتروني\n              <span className=\"text-gray-500 font-normal text-xs\">(اختياري)</span>\n            </label>\n            <input\n              type=\"url\"\n              value={formData.companyWebsite}\n              onChange={(e) => handleInputChange('companyWebsite', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n              placeholder=\"https://www.company.com (اختياري)\"\n            />\n          </div>\n\n          {/* العملة */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">العملة الافتراضية</label>\n            <select\n              value={formData.currency}\n              onChange={(e) => handleInputChange('currency', e.target.value)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n            >\n              <option value=\"USD\">دولار أمريكي (USD)</option>\n              <option value=\"SAR\">ريال سعودي (SAR)</option>\n              <option value=\"EUR\">يورو (EUR)</option>\n              <option value=\"GBP\">جنيه إسترليني (GBP)</option>\n              <option value=\"AED\">درهم إماراتي (AED)</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end mt-8\">\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            {loading ? (\n              <span className=\"flex items-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                </svg>\n                جاري الحفظ...\n              </span>\n            ) : (\n              <span className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                حفظ الإعدادات\n              </span>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;AAGlD;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa,UAAU,eAAe;QACtC,gBAAgB,UAAU,kBAAkB;QAC5C,cAAc,UAAU,gBAAgB;QACxC,cAAc,UAAU,gBAAgB;QACxC,gBAAgB,UAAU,kBAAkB;QAC5C,OAAO,UAAU,SAAS;QAC1B,UAAU,UAAU,YAAY;QAChC,MAAM;IACR;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,WAAW;IAEpE,MAAM,oBAAoB,CAAC,OAAO;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAK,CAAC;YAC5C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,MAAM;YACrD,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,2CAA2C;QAC3C,MAAM,aAAa;YAAE,GAAG,QAAQ;QAAC;QACjC,IAAI,SAAS,IAAI,EAAE;YACjB,WAAW,QAAQ,GAAG,SAAS,IAAI;QACrC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAA0B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjF,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,4BACC,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAK7E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMhD,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wCAChE,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAKZ,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,OAAO,SAAS,cAAc;wCAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACnE,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCACjE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCACjE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAAiD;0DAEhE,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,cAAc;wCAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACnE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;qDAIR,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/data/defaultSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/data/defaultSettings.js\n\nexport const defaultInvoiceDisplaySettings = {\n  // عناصر الرأس\n  showCompanyLogo: true,\n  showCompanyName: true,\n  showCompanyAddress: true,\n  showCompanyPhone: true,\n  showCompanyEmail: true,\n  showCompanyWebsite: true, // اختياري - يظهر فقط إذا كان متوفراً\n  showTaxId: true,\n\n  // معلومات الفاتورة\n  showInvoiceNumber: true,\n  showInvoiceDate: true,\n  showDueDate: true,\n  showPaymentTerms: true,\n\n  // معلومات العميل\n  showCustomerName: true,\n  showCustomerAddress: true,\n  showCustomerPhone: true,\n  showCustomerEmail: true,\n\n  // عناصر الجدول\n  showProductImages: true,\n  showProductCode: true,\n  showProductDescription: true,\n  showQuantity: true,\n  showUnitPrice: true,\n  showDiscount: false,\n  showTotalPrice: true,\n\n  // المجاميع\n  showSubtotal: true,\n  showTaxAmount: true,\n  showDiscountAmount: false,\n  showTotalAmount: true,\n\n  // التذييل\n  showNotes: true,\n  showFooter: true,\n  showSignature: false,\n  showQRCode: false,\n\n  // إعدادات إضافية\n  showItemNumbers: true,\n  showBankDetails: false,\n  showPaymentInstructions: true\n};\n\nexport const sectionFields = {\n  header: ['showCompanyLogo', 'showCompanyName', 'showCompanyAddress', 'showCompanyPhone', 'showCompanyEmail', 'showCompanyWebsite', 'showTaxId'],\n  invoice: ['showInvoiceNumber', 'showInvoiceDate', 'showDueDate', 'showPaymentTerms'],\n  customer: ['showCustomerName', 'showCustomerAddress', 'showCustomerPhone', 'showCustomerEmail'],\n  items: ['showProductImages', 'showProductCode', 'showProductDescription', 'showQuantity', 'showUnitPrice', 'showDiscount', 'showTotalPrice', 'showItemNumbers'],\n  totals: ['showSubtotal', 'showTaxAmount', 'showDiscountAmount', 'showTotalAmount'],\n  footer: ['showNotes', 'showFooter', 'showSignature', 'showQRCode', 'showBankDetails', 'showPaymentInstructions']\n};\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;AAExD,MAAM,gCAAgC;IAC3C,cAAc;IACd,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,kBAAkB;IAClB,kBAAkB;IAClB,oBAAoB;IACpB,WAAW;IAEX,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IACjB,aAAa;IACb,kBAAkB;IAElB,iBAAiB;IACjB,kBAAkB;IAClB,qBAAqB;IACrB,mBAAmB;IACnB,mBAAmB;IAEnB,eAAe;IACf,mBAAmB;IACnB,iBAAiB;IACjB,wBAAwB;IACxB,cAAc;IACd,eAAe;IACf,cAAc;IACd,gBAAgB;IAEhB,WAAW;IACX,cAAc;IACd,eAAe;IACf,oBAAoB;IACpB,iBAAiB;IAEjB,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,YAAY;IAEZ,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,yBAAyB;AAC3B;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;QAAC;QAAmB;QAAmB;QAAsB;QAAoB;QAAoB;QAAsB;KAAY;IAC/I,SAAS;QAAC;QAAqB;QAAmB;QAAe;KAAmB;IACpF,UAAU;QAAC;QAAoB;QAAuB;QAAqB;KAAoB;IAC/F,OAAO;QAAC;QAAqB;QAAmB;QAA0B;QAAgB;QAAiB;QAAgB;QAAkB;KAAkB;IAC/J,QAAQ;QAAC;QAAgB;QAAiB;QAAsB;KAAkB;IAClF,QAAQ;QAAC;QAAa;QAAc;QAAiB;QAAc;QAAmB;KAA0B;AAClH", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/hooks/useInvoiceDisplaySettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/hooks/useInvoiceDisplaySettings.js\n'use client';\n\nimport { useState } from 'react';\nimport { defaultInvoiceDisplaySettings, sectionFields } from '../data/defaultSettings';\n\nexport function useInvoiceDisplaySettings(initialSettings) {\n  const [formData, setFormData] = useState({\n    ...defaultInvoiceDisplaySettings,\n    ...initialSettings\n  });\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSelectAll = (section) => {\n    const fields = sectionFields[section] || [];\n    const newData = { ...formData };\n    fields.forEach(field => {\n      newData[field] = true;\n    });\n    setFormData(newData);\n  };\n\n  const handleDeselectAll = (section) => {\n    const fields = sectionFields[section] || [];\n    const newData = { ...formData };\n    fields.forEach(field => {\n      newData[field] = false;\n    });\n    setFormData(newData);\n  };\n\n  const handleReset = () => {\n    setFormData({\n      ...defaultInvoiceDisplaySettings,\n      ...initialSettings\n    });\n  };\n\n  return {\n    formData,\n    handleInputChange,\n    handleSelectAll,\n    handleDeselectAll,\n    handleReset\n  };\n}\n"], "names": [], "mappings": "AAAA,0EAA0E;;;;AAG1E;AACA;AAHA;;;AAKO,SAAS,0BAA0B,eAAe;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,GAAG,4JAAA,CAAA,gCAA6B;QAChC,GAAG,eAAe;IACpB;IAEA,MAAM,oBAAoB,CAAC,OAAO;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,4JAAA,CAAA,gBAAa,CAAC,QAAQ,IAAI,EAAE;QAC3C,MAAM,UAAU;YAAE,GAAG,QAAQ;QAAC;QAC9B,OAAO,OAAO,CAAC,CAAA;YACb,OAAO,CAAC,MAAM,GAAG;QACnB;QACA,YAAY;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,4JAAA,CAAA,gBAAa,CAAC,QAAQ,IAAI,EAAE;QAC3C,MAAM,UAAU;YAAE,GAAG,QAAQ;QAAC;QAC9B,OAAO,OAAO,CAAC,CAAA;YACb,OAAO,CAAC,MAAM,GAAG;QACnB;QACA,YAAY;IACd;IAEA,MAAM,cAAc;QAClB,YAAY;YACV,GAAG,4JAAA,CAAA,gCAA6B;YAChC,GAAG,eAAe;QACpB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/components/SectionHeader.js"], "sourcesContent": ["// frontend/components/settings/invoice/components/SectionHeader.js\n'use client';\n\nexport default function SectionHeader({ title, icon, section, onSelectAll, onDeselectAll, children }) {\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6\">\n      <div className=\"bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {icon}\n            <h3 className=\"text-lg font-semibold text-white mr-3\">{title}</h3>\n          </div>\n          <div className=\"flex gap-2\">\n            <button\n              type=\"button\"\n              onClick={() => onSelectAll(section)}\n              className=\"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors\"\n            >\n              تحديد الكل\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => onDeselectAll(section)}\n              className=\"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors\"\n            >\n              إلغاء الكل\n            </button>\n          </div>\n        </div>\n      </div>\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AACnE;;AAEe,SAAS,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE;IAClG,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ;8CACD,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;;;;;;;sCAEzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/components/CheckboxField.js"], "sourcesContent": ["// frontend/components/settings/invoice/components/CheckboxField.js\n'use client';\n\nexport default function CheckboxField({ field, label, description, checked, onChange }) {\n  return (\n    <div className=\"flex items-start space-x-3\">\n      <div className=\"flex items-center h-5\">\n        <input\n          type=\"checkbox\"\n          checked={checked}\n          onChange={(e) => onChange(field, e.target.checked)}\n          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2\"\n        />\n      </div>\n      <div className=\"text-sm\">\n        <label className=\"font-medium text-gray-900 cursor-pointer\">\n          {label}\n        </label>\n        {description && (\n          <p className=\"text-gray-500 text-xs mt-1\">{description}</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AACnE;;AAEe,SAAS,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE;IACpF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU,CAAC,IAAM,SAAS,OAAO,EAAE,MAAM,CAAC,OAAO;oBACjD,WAAU;;;;;;;;;;;0BAGd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCACd;;;;;;oBAEF,6BACC,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/HeaderSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/HeaderSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function HeaderSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const headerFields = [\n    { field: 'showCompanyLogo', label: 'شعار الشركة', description: 'عرض شعار الشركة في الرأس' },\n    { field: 'showCompanyName', label: 'اسم الشركة', description: 'عرض اسم الشركة' },\n    { field: 'showCompanyAddress', label: 'عنوان الشركة', description: 'عرض عنوان الشركة' },\n    { field: 'showCompanyPhone', label: 'هاتف الشركة', description: 'عرض رقم هاتف الشركة' },\n    { field: 'showCompanyEmail', label: 'بريد الشركة', description: 'عرض البريد الإلكتروني' },\n    { field: 'showCompanyWebsite', label: 'موقع الشركة (اختياري)', description: 'عرض موقع الشركة الإلكتروني إذا كان متوفراً' },\n    { field: 'showTaxId', label: 'الرقم الضريبي', description: 'عرض الرقم الضريبي للشركة' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"رأس الفاتورة\"\n      section=\"header\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10\" />\n        </svg>\n      }\n    >\n      {headerFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAGlE;AACA;AAHA;;;;AAKe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IAC5F,MAAM,eAAe;QACnB;YAAE,OAAO;YAAmB,OAAO;YAAe,aAAa;QAA2B;QAC1F;YAAE,OAAO;YAAmB,OAAO;YAAc,aAAa;QAAiB;QAC/E;YAAE,OAAO;YAAsB,OAAO;YAAgB,aAAa;QAAmB;QACtF;YAAE,OAAO;YAAoB,OAAO;YAAe,aAAa;QAAsB;QACtF;YAAE,OAAO;YAAoB,OAAO;YAAe,aAAa;QAAwB;QACxF;YAAE,OAAO;YAAsB,OAAO;YAAyB,aAAa;QAA6C;QACzH;YAAE,OAAO;YAAa,OAAO;YAAiB,aAAa;QAA2B;KACvF;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,aAAa,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAC9C,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/InvoiceInfoSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/InvoiceInfoSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function InvoiceInfoSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const invoiceFields = [\n    { field: 'showInvoiceNumber', label: 'رقم الفاتورة', description: 'عرض رقم الفاتورة' },\n    { field: 'showInvoiceDate', label: 'تاريخ الفاتورة', description: 'عرض تاريخ إصدار الفاتورة' },\n    { field: 'showDueDate', label: 'تاريخ الاستحقاق', description: 'عرض تاريخ استحقاق الدفع' },\n    { field: 'showPaymentTerms', label: 'شروط الدفع', description: 'عرض شروط وأحكام الدفع' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"معلومات الفاتورة\"\n      section=\"invoice\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      }\n    >\n      {invoiceFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AAGvE;AACA;AAHA;;;;AAKe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IACjG,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAqB,OAAO;YAAgB,aAAa;QAAmB;QACrF;YAAE,OAAO;YAAmB,OAAO;YAAkB,aAAa;QAA2B;QAC7F;YAAE,OAAO;YAAe,OAAO;YAAmB,aAAa;QAA0B;QACzF;YAAE,OAAO;YAAoB,OAAO;YAAc,aAAa;QAAwB;KACxF;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,cAAc,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAC/C,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/CustomerSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/CustomerSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function CustomerSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const customerFields = [\n    { field: 'showCustomerName', label: 'اسم العميل', description: 'عرض اسم العميل' },\n    { field: 'showCustomerAddress', label: 'عنوان العميل', description: 'عرض عنوان العميل' },\n    { field: 'showCustomerPhone', label: 'هاتف العميل', description: 'عرض رقم هاتف العميل' },\n    { field: 'showCustomerEmail', label: 'بريد العميل', description: 'عرض البريد الإلكتروني للعميل' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"معلومات العميل\"\n      section=\"customer\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      }\n    >\n      {customerFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;AAGpE;AACA;AAHA;;;;AAKe,SAAS,iBAAiB,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IAC9F,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAoB,OAAO;YAAc,aAAa;QAAiB;QAChF;YAAE,OAAO;YAAuB,OAAO;YAAgB,aAAa;QAAmB;QACvF;YAAE,OAAO;YAAqB,OAAO;YAAe,aAAa;QAAsB;QACvF;YAAE,OAAO;YAAqB,OAAO;YAAe,aAAa;QAA+B;KACjG;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,eAAe,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAChD,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/ItemsSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/ItemsSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function ItemsSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const itemsFields = [\n    { field: 'showItemNumbers', label: 'ترقيم العناصر', description: 'عرض أرقام تسلسلية للعناصر' },\n    { field: 'showProductImages', label: 'صور المنتجات', description: 'عرض صور المنتجات' },\n    { field: 'showProductCode', label: 'كود المنتج', description: 'عرض كود أو رقم المنتج' },\n    { field: 'showProductDescription', label: 'وصف المنتج', description: 'عرض وصف المنتج' },\n    { field: 'showQuantity', label: 'الكمية', description: 'عرض كمية المنتج' },\n    { field: 'showUnitPrice', label: 'سعر الوحدة', description: 'عرض سعر الوحدة الواحدة' },\n    { field: 'showDiscount', label: 'الخصم', description: 'عرض قيمة الخصم' },\n    { field: 'showTotalPrice', label: 'الإجمالي', description: 'عرض إجمالي سعر العنصر' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"عناصر الفاتورة\"\n      section=\"items\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n        </svg>\n      }\n    >\n      {itemsFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;;AAGjE;AACA;AAHA;;;;AAKe,SAAS,cAAc,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IAC3F,MAAM,cAAc;QAClB;YAAE,OAAO;YAAmB,OAAO;YAAiB,aAAa;QAA4B;QAC7F;YAAE,OAAO;YAAqB,OAAO;YAAgB,aAAa;QAAmB;QACrF;YAAE,OAAO;YAAmB,OAAO;YAAc,aAAa;QAAwB;QACtF;YAAE,OAAO;YAA0B,OAAO;YAAc,aAAa;QAAiB;QACtF;YAAE,OAAO;YAAgB,OAAO;YAAU,aAAa;QAAkB;QACzE;YAAE,OAAO;YAAiB,OAAO;YAAc,aAAa;QAAyB;QACrF;YAAE,OAAO;YAAgB,OAAO;YAAS,aAAa;QAAiB;QACvE;YAAE,OAAO;YAAkB,OAAO;YAAY,aAAa;QAAwB;KACpF;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,YAAY,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAC7C,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/TotalsSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/TotalsSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function TotalsSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const totalsFields = [\n    { field: 'showSubtotal', label: 'المجموع الفرعي', description: 'عرض المجموع قبل الضريبة' },\n    { field: 'showTaxAmount', label: 'قيمة الضريبة', description: 'عرض مبلغ الضريبة' },\n    { field: 'showDiscountAmount', label: 'قيمة الخصم', description: 'عرض إجمالي الخصم' },\n    { field: 'showTotalAmount', label: 'المبلغ الإجمالي', description: 'عرض المبلغ النهائي' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"المجاميع والحسابات\"\n      section=\"totals\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n        </svg>\n      }\n    >\n      {totalsFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAGlE;AACA;AAHA;;;;AAKe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IAC5F,MAAM,eAAe;QACnB;YAAE,OAAO;YAAgB,OAAO;YAAkB,aAAa;QAA0B;QACzF;YAAE,OAAO;YAAiB,OAAO;YAAgB,aAAa;QAAmB;QACjF;YAAE,OAAO;YAAsB,OAAO;YAAc,aAAa;QAAmB;QACpF;YAAE,OAAO;YAAmB,OAAO;YAAmB,aAAa;QAAqB;KACzF;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,aAAa,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAC9C,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/sections/FooterSettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/sections/FooterSettings.js\n'use client';\n\nimport SectionHeader from '../components/SectionHeader';\nimport CheckboxField from '../components/CheckboxField';\n\nexport default function FooterSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {\n  const footerFields = [\n    { field: 'showNotes', label: 'الملاحظات', description: 'عرض ملاحظات الفاتورة' },\n    { field: 'showFooter', label: 'تذييل مخصص', description: 'عرض نص التذييل المخصص' },\n    { field: 'showSignature', label: 'مكان التوقيع', description: 'عرض مساحة للتوقيع' },\n    { field: 'showQRCode', label: 'رمز QR', description: 'عرض رمز QR للفاتورة' },\n    { field: 'showBankDetails', label: 'تفاصيل البنك', description: 'عرض معلومات الحساب البنكي' },\n    { field: 'showPaymentInstructions', label: 'تعليمات الدفع', description: 'عرض تعليمات الدفع' }\n  ];\n\n  return (\n    <SectionHeader\n      title=\"تذييل الفاتورة\"\n      section=\"footer\"\n      onSelectAll={onSelectAll}\n      onDeselectAll={onDeselectAll}\n      icon={\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\" />\n        </svg>\n      }\n    >\n      {footerFields.map(({ field, label, description }) => (\n        <CheckboxField\n          key={field}\n          field={field}\n          label={label}\n          description={description}\n          checked={formData[field]}\n          onChange={onInputChange}\n        />\n      ))}\n    </SectionHeader>\n  );\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAGlE;AACA;AAHA;;;;AAKe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE;IAC5F,MAAM,eAAe;QACnB;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAuB;QAC9E;YAAE,OAAO;YAAc,OAAO;YAAc,aAAa;QAAwB;QACjF;YAAE,OAAO;YAAiB,OAAO;YAAgB,aAAa;QAAoB;QAClF;YAAE,OAAO;YAAc,OAAO;YAAU,aAAa;QAAsB;QAC3E;YAAE,OAAO;YAAmB,OAAO;YAAgB,aAAa;QAA4B;QAC5F;YAAE,OAAO;YAA2B,OAAO;YAAiB,aAAa;QAAoB;KAC9F;IAED,qBACE,8OAAC,gKAAA,CAAA,UAAa;QACZ,OAAM;QACN,SAAQ;QACR,aAAa;QACb,eAAe;QACf,oBACE,8OAAC;YAAI,WAAU;YAAqB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC5E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;kBAIxE,aAAa,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAC9C,8OAAC,gKAAA,CAAA,UAAa;gBAEZ,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,SAAS,QAAQ,CAAC,MAAM;gBACxB,UAAU;eALL;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/components/SaveButtons.js"], "sourcesContent": ["// frontend/components/settings/invoice/components/SaveButtons.js\n'use client';\n\nexport default function SaveButtons({ loading, onReset }) {\n  return (\n    <div className=\"flex justify-end space-x-4 pt-6 border-t border-gray-200\">\n      <button\n        type=\"button\"\n        onClick={onReset}\n        className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n      >\n        إعادة تعيين\n      </button>\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed\"\n      >\n        {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;;AACjE;;AAEe,SAAS,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;0BACX;;;;;;0BAGD,8OAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,UAAU,kBAAkB;;;;;;;;;;;;AAIrC", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/invoice/InvoiceDisplaySettings.js"], "sourcesContent": ["// frontend/components/settings/invoice/InvoiceDisplaySettings.js\n'use client';\n\nimport { useInvoiceDisplaySettings } from './hooks/useInvoiceDisplaySettings';\nimport HeaderSettings from './sections/HeaderSettings';\nimport InvoiceInfoSettings from './sections/InvoiceInfoSettings';\nimport CustomerSettings from './sections/CustomerSettings';\nimport ItemsSettings from './sections/ItemsSettings';\nimport TotalsSettings from './sections/TotalsSettings';\nimport FooterSettings from './sections/FooterSettings';\nimport SaveButtons from './components/SaveButtons';\n\nexport default function InvoiceDisplaySettings({ settings, onSave, loading }) {\n  const {\n    formData,\n    handleInputChange,\n    handleSelectAll,\n    handleDeselectAll,\n    handleReset\n  } = useInvoiceDisplaySettings(settings);\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSave(formData);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <HeaderSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <InvoiceInfoSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <CustomerSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <ItemsSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <TotalsSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <FooterSettings\n        formData={formData}\n        onInputChange={handleInputChange}\n        onSelectAll={handleSelectAll}\n        onDeselectAll={handleDeselectAll}\n      />\n\n      <SaveButtons\n        loading={loading}\n        onReset={handleReset}\n      />\n    </form>\n  );\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;;AAGjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS,uBAAuB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;IAC1E,MAAM,EACJ,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,iBAAiB,EACjB,WAAW,EACZ,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD,EAAE;IAE9B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC,+JAAA,CAAA,UAAc;gBACb,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,oKAAA,CAAA,UAAmB;gBAClB,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,iKAAA,CAAA,UAAgB;gBACf,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,8JAAA,CAAA,UAAa;gBACZ,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,+JAAA,CAAA,UAAc;gBACb,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,+JAAA,CAAA,UAAc;gBACb,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,eAAe;;;;;;0BAGjB,8OAAC,8JAAA,CAAA,UAAW;gBACV,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/InvoiceSettings.js"], "sourcesContent": ["// frontend/components/settings/InvoiceSettings.js\n'use client';\n\nimport { useState } from 'react';\nimport InvoiceDisplaySettings from './invoice/InvoiceDisplaySettings';\n\nexport default function InvoiceSettings({ settings, onSave, loading, onRefresh }) {\n  const [activeTab, setActiveTab] = useState('general');\n\n  const [formData, setFormData] = useState({\n    invoicePrefix: settings?.invoicePrefix || 'INV',\n    invoiceNumberLength: settings?.invoiceNumberLength || 6,\n    defaultTaxRate: settings?.defaultTaxRate || 0,\n    defaultPaymentTerms: settings?.defaultPaymentTerms || '',\n    autoGenerateInvoiceNumber: settings?.autoGenerateInvoiceNumber ?? true,\n    showImagesInInvoice: settings?.showImagesInInvoice ?? true,\n    allowPartialPayments: settings?.allowPartialPayments ?? true,\n    requireCustomerInfo: settings?.requireCustomerInfo ?? true,\n    defaultDueDays: settings?.defaultDueDays || 30,\n    invoiceFooter: settings?.invoiceFooter || '',\n    invoiceNotes: settings?.invoiceNotes || ''\n  });\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSave(formData);\n  };\n\n  const tabs = [\n    { id: 'general', name: 'الإعدادات العامة', icon: '⚙️' },\n    { id: 'display', name: 'إعدادات العرض', icon: '👁️' }\n  ];\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n      <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n        <div className=\"flex items-center\">\n          <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h2 className=\"text-lg font-semibold text-white\">إعدادات الفواتير</h2>\n        </div>\n      </div>\n\n      {/* التبويبات */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === tab.id\n                  ? 'border-green-500 text-green-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.name}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      <div className=\"p-6\">\n        {activeTab === 'general' && (\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* ترقيم الفواتير */}\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <svg className=\"w-5 h-5 mr-2 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\" />\n              </svg>\n              ترقيم الفواتير\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">بادئة الفاتورة</label>\n                <input\n                  type=\"text\"\n                  value={formData.invoicePrefix}\n                  onChange={(e) => handleInputChange('invoicePrefix', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                  placeholder=\"INV\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">طول الرقم</label>\n                <input\n                  type=\"number\"\n                  min=\"4\"\n                  max=\"10\"\n                  value={formData.invoiceNumberLength}\n                  onChange={(e) => handleInputChange('invoiceNumberLength', parseInt(e.target.value))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                />\n              </div>\n              <div className=\"flex items-center\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.autoGenerateInvoiceNumber}\n                    onChange={(e) => handleInputChange('autoGenerateInvoiceNumber', e.target.checked)}\n                    className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">ترقيم تلقائي</span>\n                </label>\n              </div>\n            </div>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              مثال: {formData.invoicePrefix}-{String(1).padStart(formData.invoiceNumberLength, '0')}\n            </p>\n          </div>\n\n          {/* الضرائب والدفع */}\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">معدل الضريبة الافتراضي (%)</label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              max=\"100\"\n              value={formData.defaultTaxRate}\n              onChange={(e) => handleInputChange('defaultTaxRate', parseFloat(e.target.value) || 0)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n              placeholder=\"15.00\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">مدة الاستحقاق الافتراضية (أيام)</label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              value={formData.defaultDueDays}\n              onChange={(e) => handleInputChange('defaultDueDays', parseInt(e.target.value) || 0)}\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200\"\n              placeholder=\"30\"\n            />\n          </div>\n\n          {/* شروط الدفع الافتراضية */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">شروط الدفع الافتراضية</label>\n            <textarea\n              value={formData.defaultPaymentTerms}\n              onChange={(e) => handleInputChange('defaultPaymentTerms', e.target.value)}\n              rows=\"3\"\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none\"\n              placeholder=\"الدفع خلال 30 يوم من تاريخ الفاتورة...\"\n            />\n          </div>\n\n          {/* الخيارات المتقدمة */}\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <svg className=\"w-5 h-5 mr-2 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n              </svg>\n              خيارات متقدمة\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.showImagesInInvoice}\n                  onChange={(e) => handleInputChange('showImagesInInvoice', e.target.checked)}\n                  className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">إظهار صور المنتجات في الفواتير</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.allowPartialPayments}\n                  onChange={(e) => handleInputChange('allowPartialPayments', e.target.checked)}\n                  className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">السماح بالدفعات الجزئية</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.requireCustomerInfo}\n                  onChange={(e) => handleInputChange('requireCustomerInfo', e.target.checked)}\n                  className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">إجبارية معلومات العميل</span>\n              </label>\n            </div>\n          </div>\n\n          {/* ملاحظات الفاتورة */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">الملاحظات الافتراضية</label>\n            <textarea\n              value={formData.invoiceNotes}\n              onChange={(e) => handleInputChange('invoiceNotes', e.target.value)}\n              rows=\"3\"\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none\"\n              placeholder=\"شكراً لتعاملكم معنا...\"\n            />\n          </div>\n\n          {/* تذييل الفاتورة */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-semibold text-gray-700 mb-2\">تذييل الفاتورة</label>\n            <textarea\n              value={formData.invoiceFooter}\n              onChange={(e) => handleInputChange('invoiceFooter', e.target.value)}\n              rows=\"2\"\n              className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 resize-none\"\n              placeholder=\"معلومات إضافية تظهر في أسفل الفاتورة...\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex justify-end mt-8\">\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            {loading ? (\n              <span className=\"flex items-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                </svg>\n                جاري الحفظ...\n              </span>\n            ) : (\n              <span className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                حفظ الإعدادات\n              </span>\n            )}\n          </button>\n        </div>\n          </form>\n        )}\n\n        {activeTab === 'display' && (\n          <InvoiceDisplaySettings\n            settings={settings?.invoice?.display || {}}\n            onSave={async (data) => {\n              try {\n                const settingsService = (await import('@/lib/settingsService')).default;\n                await settingsService.saveInvoiceDisplaySettings(data);\n\n                // إعادة تحميل الإعدادات إذا كانت الدالة متوفرة\n                if (onRefresh) {\n                  await onRefresh();\n                }\n\n                // إشعار بالنجاح\n                alert('✅ تم حفظ إعدادات العرض بنجاح!');\n              } catch (error) {\n                console.error('Error saving display settings:', error);\n                alert('❌ حدث خطأ أثناء حفظ الإعدادات');\n              }\n            }}\n            loading={loading}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;AAGlD;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe,UAAU,iBAAiB;QAC1C,qBAAqB,UAAU,uBAAuB;QACtD,gBAAgB,UAAU,kBAAkB;QAC5C,qBAAqB,UAAU,uBAAuB;QACtD,2BAA2B,UAAU,6BAA6B;QAClE,qBAAqB,UAAU,uBAAuB;QACtD,sBAAsB,UAAU,wBAAwB;QACxD,qBAAqB,UAAU,uBAAuB;QACtD,gBAAgB,UAAU,kBAAkB;QAC5C,eAAe,UAAU,iBAAiB;QAC1C,cAAc,UAAU,gBAAgB;IAC1C;IAEA,MAAM,oBAAoB,CAAC,OAAO;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;IACT;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM;YAAoB,MAAM;QAAK;QACtD;YAAE,IAAI;YAAW,MAAM;YAAiB,MAAM;QAAM;KACrD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAA0B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjF,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;;;;;;0BAKrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,oCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAQ,IAAI,IAAI;;;;;;gCAC/B,IAAI,IAAI;;2BATJ,IAAI,EAAE;;;;;;;;;;;;;;;0BAenB,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,2BACb,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAC1C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACrF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO,SAAS,aAAa;gEAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAClE,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,SAAS,mBAAmB;gEACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;gEACjF,WAAU;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,SAAS,yBAAyB;oEAC3C,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,OAAO;oEAChF,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;0DAInD,8OAAC;gDAAE,WAAU;;oDAA6B;oDACjC,SAAS,aAAa;oDAAC;oDAAE,OAAO,GAAG,QAAQ,CAAC,SAAS,mBAAmB,EAAE;;;;;;;;;;;;;kDAKrF,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDACnF,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACjF,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACxE,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EACrF,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,mBAAmB;gEACrC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,OAAO;gEAC1E,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;kEAG/C,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,oBAAoB;gEACtC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,OAAO;gEAC3E,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;kEAG/C,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,mBAAmB;gEACrC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,OAAO;gEAC1E,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;;;;;;;;;;;;;kDAMnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;6DAIR,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;oBASb,cAAc,2BACb,8OAAC,2JAAA,CAAA,UAAsB;wBACrB,UAAU,UAAU,SAAS,WAAW,CAAC;wBACzC,QAAQ,OAAO;4BACb,IAAI;gCACF,MAAM,kBAAkB,CAAC,+HAAqC,EAAE,OAAO;gCACvE,MAAM,gBAAgB,0BAA0B,CAAC;gCAEjD,+CAA+C;gCAC/C,IAAI,WAAW;oCACb,MAAM;gCACR;gCAEA,gBAAgB;gCAChB,MAAM;4BACR,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,kCAAkC;gCAChD,MAAM;4BACR;wBACF;wBACA,SAAS;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/BackupSettings.js"], "sourcesContent": ["// frontend/components/settings/BackupSettings.js\n'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function BackupSettings({ onBackup, onRestore, loading }) {\n  const [backupHistory, setBackupHistory] = useState([]);\n  const [autoBackup, setAutoBackup] = useState(true);\n  const [backupFrequency, setBackupFrequency] = useState('daily');\n  const [backupTime, setBackupTime] = useState('02:00');\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  useEffect(() => {\n    // محاكاة تحميل تاريخ النسخ الاحتياطية\n    const mockBackups = [\n      {\n        id: 1,\n        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n        size: '2.5 MB',\n        type: 'auto',\n        status: 'success'\n      },\n      {\n        id: 2,\n        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),\n        size: '2.3 MB',\n        type: 'manual',\n        status: 'success'\n      },\n      {\n        id: 3,\n        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\n        size: '2.1 MB',\n        type: 'auto',\n        status: 'success'\n      }\n    ];\n    setBackupHistory(mockBackups);\n  }, []);\n\n  const handleCreateBackup = () => {\n    onBackup({\n      type: 'manual',\n      includeImages: true,\n      includeSettings: true\n    });\n  };\n\n  const handleRestoreBackup = (backupId) => {\n    if (window.confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {\n      onRestore(backupId);\n    }\n  };\n\n  const handleFileRestore = () => {\n    if (!selectedFile) {\n      alert('يرجى اختيار ملف النسخة الاحتياطية');\n      return;\n    }\n    \n    if (window.confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {\n      onRestore(selectedFile);\n    }\n  };\n\n  const formatDate = (date) => {\n    return new Date(date).toLocaleDateString('ar-SA', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getBackupIcon = (type) => {\n    return type === 'auto' ? (\n      <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n      </svg>\n    ) : (\n      <svg className=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n      </svg>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* إعدادات النسخ الاحتياطي التلقائي */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-purple-500 to-pink-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">النسخ الاحتياطي التلقائي</h2>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={autoBackup}\n                  onChange={(e) => setAutoBackup(e.target.checked)}\n                  className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm font-medium text-gray-700\">تفعيل النسخ الاحتياطي التلقائي</span>\n              </label>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">التكرار</label>\n              <select\n                value={backupFrequency}\n                onChange={(e) => setBackupFrequency(e.target.value)}\n                disabled={!autoBackup}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 disabled:bg-gray-100\"\n              >\n                <option value=\"daily\">يومياً</option>\n                <option value=\"weekly\">أسبوعياً</option>\n                <option value=\"monthly\">شهرياً</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">الوقت</label>\n              <input\n                type=\"time\"\n                value={backupTime}\n                onChange={(e) => setBackupTime(e.target.value)}\n                disabled={!autoBackup}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 disabled:bg-gray-100\"\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-start\">\n              <svg className=\"w-5 h-5 text-blue-600 mt-0.5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <div>\n                <h4 className=\"text-sm font-medium text-blue-800\">معلومات مهمة</h4>\n                <p className=\"text-sm text-blue-700 mt-1\">\n                  سيتم إنشاء نسخة احتياطية تلقائياً {backupFrequency === 'daily' ? 'يومياً' : backupFrequency === 'weekly' ? 'أسبوعياً' : 'شهرياً'} \n                  في الساعة {backupTime}. يتم الاحتفاظ بآخر 30 نسخة احتياطية.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* إنشاء نسخة احتياطية يدوية */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">إنشاء نسخة احتياطية</h2>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 mb-6\">إنشاء نسخة احتياطية فورية من جميع البيانات</p>\n            <button\n              onClick={handleCreateBackup}\n              disabled={loading}\n              className=\"px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n            >\n              {loading ? (\n                <span className=\"flex items-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                  </svg>\n                  جاري إنشاء النسخة...\n                </span>\n              ) : (\n                <span className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                  إنشاء نسخة احتياطية الآن\n                </span>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* استعادة من ملف */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">استعادة من ملف</h2>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">اختر ملف النسخة الاحتياطية</label>\n              <input\n                type=\"file\"\n                accept=\".json,.zip\"\n                onChange={(e) => setSelectedFile(e.target.files[0])}\n                className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100\"\n              />\n            </div>\n            \n            <div className=\"flex justify-end\">\n              <button\n                onClick={handleFileRestore}\n                disabled={!selectedFile || loading}\n                className=\"px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white font-medium rounded-lg hover:from-orange-700 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n              >\n                <span className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                  </svg>\n                  استعادة البيانات\n                </span>\n              </button>\n            </div>\n          </div>\n\n          <div className=\"mt-6 p-4 bg-red-50 rounded-lg\">\n            <div className=\"flex items-start\">\n              <svg className=\"w-5 h-5 text-red-600 mt-0.5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n              <div>\n                <h4 className=\"text-sm font-medium text-red-800\">تحذير</h4>\n                <p className=\"text-sm text-red-700 mt-1\">\n                  استعادة النسخة الاحتياطية ستستبدل جميع البيانات الحالية. تأكد من إنشاء نسخة احتياطية قبل المتابعة.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* تاريخ النسخ الاحتياطية */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">تاريخ النسخ الاحتياطية</h2>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {backupHistory.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2z\" />\n              </svg>\n              <p className=\"text-gray-500\">لا توجد نسخ احتياطية</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {backupHistory.map((backup) => (\n                <div key={backup.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex-shrink-0\">\n                      {getBackupIcon(backup.type)}\n                    </div>\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-sm font-medium text-gray-900\">\n                          {formatDate(backup.date)}\n                        </span>\n                        <span className={`text-xs px-2 py-1 rounded-full ${\n                          backup.type === 'auto' \n                            ? 'bg-blue-100 text-blue-800' \n                            : 'bg-green-100 text-green-800'\n                        }`}>\n                          {backup.type === 'auto' ? 'تلقائي' : 'يدوي'}\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-500\">الحجم: {backup.size}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => handleRestoreBackup(backup.id)}\n                      className=\"px-3 py-1 text-sm text-indigo-600 hover:text-indigo-800 font-medium\"\n                    >\n                      استعادة\n                    </button>\n                    <button className=\"px-3 py-1 text-sm text-gray-600 hover:text-gray-800 font-medium\">\n                      تحميل\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;AAGjD;AAFA;;;AAIe,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,MAAM,cAAc;YAClB;gBACE,IAAI;gBACJ,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;gBAC/C,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;gBAC/C,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;gBAC/C,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;SACD;QACD,iBAAiB;IACnB,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,SAAS;YACP,MAAM;YACN,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,OAAO,OAAO,CAAC,kFAAkF;YACnG,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;YACjB,MAAM;YACN;QACF;QAEA,IAAI,OAAO,OAAO,CAAC,kFAAkF;YACnG,UAAU;QACZ;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,SAAS,uBACd,8OAAC;YAAI,WAAU;YAAwB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAC/E,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;iCAGvE,8OAAC;YAAI,WAAU;YAAyB,MAAK;YAAO,QAAO;YAAe,SAAQ;sBAChF,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;gBAAI,GAAE;;;;;;;;;;;IAG3E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDACC,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;oDAC/C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAyC;;;;;;;;;;;;;;;;;kDAI7D,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,UAAU,CAAC;gDACX,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAI5B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,UAAU,CAAC;gDACX,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAoC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC3F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;;wDAA6B;wDACL,oBAAoB,UAAU,WAAW,oBAAoB,WAAW,aAAa;wDAAS;wDACtH;wDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,wBACC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;6DAIR,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gDAClD,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,gBAAgB;4CAC3B,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;oDACjE;;;;;;;;;;;;;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAmC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC1F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAuC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9F,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAG/B,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,cAAc,OAAO,IAAI;;;;;;8DAE5B,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,WAAW,OAAO,IAAI;;;;;;8EAEzB,8OAAC;oEAAK,WAAW,CAAC,+BAA+B,EAC/C,OAAO,IAAI,KAAK,SACZ,8BACA,+BACJ;8EACC,OAAO,IAAI,KAAK,SAAS,WAAW;;;;;;;;;;;;sEAGzC,8OAAC;4DAAE,WAAU;;gEAAwB;gEAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;sDAG5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,oBAAoB,OAAO,EAAE;oDAC5C,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAO,WAAU;8DAAkE;;;;;;;;;;;;;mCA5B9E,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCnC", "debugId": null}}, {"offset": {"line": 3309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/NotificationSettings.js"], "sourcesContent": ["// frontend/components/settings/NotificationSettings.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function NotificationSettings({ settings, onSave, loading }) {\n  const [formData, setFormData] = useState({\n    emailNotifications: settings?.emailNotifications ?? true,\n    smsNotifications: settings?.smsNotifications ?? false,\n    pushNotifications: settings?.pushNotifications ?? true,\n    \n    // إشعارات الفواتير\n    invoiceCreated: settings?.invoiceCreated ?? true,\n    invoicePaid: settings?.invoicePaid ?? true,\n    invoiceOverdue: settings?.invoiceOverdue ?? true,\n    paymentReceived: settings?.paymentReceived ?? true,\n    \n    // إشعارات المخزون\n    lowStockAlert: settings?.lowStockAlert ?? true,\n    outOfStockAlert: settings?.outOfStockAlert ?? true,\n    stockThreshold: settings?.stockThreshold || 5,\n    \n    // إشعارات النظام\n    systemUpdates: settings?.systemUpdates ?? true,\n    securityAlerts: settings?.securityAlerts ?? true,\n    backupStatus: settings?.backupStatus ?? true,\n    \n    // إعدادات البريد الإلكتروني\n    emailServer: settings?.emailServer || '',\n    emailPort: settings?.emailPort || 587,\n    emailUsername: settings?.emailUsername || '',\n    emailPassword: settings?.emailPassword || '',\n    emailSecurity: settings?.emailSecurity || 'tls',\n    \n    // إعدادات الرسائل النصية\n    smsProvider: settings?.smsProvider || '',\n    smsApiKey: settings?.smsApiKey || '',\n    smsFromNumber: settings?.smsFromNumber || ''\n  });\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSave(formData);\n  };\n\n  const testEmailSettings = async () => {\n    try {\n      // محاكاة اختبار إعدادات البريد الإلكتروني\n      alert('تم إرسال رسالة اختبار بنجاح!');\n    } catch (error) {\n      alert('فشل في إرسال رسالة الاختبار. يرجى التحقق من الإعدادات.');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* إعدادات الإشعارات العامة */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-yellow-500 to-orange-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">طرق الإشعارات</h2>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <label className=\"flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={formData.emailNotifications}\n                onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}\n                className=\"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded\"\n              />\n              <div className=\"ml-3\">\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-yellow-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                  <span className=\"text-sm font-medium text-gray-900\">البريد الإلكتروني</span>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">إشعارات عبر البريد الإلكتروني</p>\n              </div>\n            </label>\n\n            <label className=\"flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={formData.smsNotifications}\n                onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}\n                className=\"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded\"\n              />\n              <div className=\"ml-3\">\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-yellow-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                  </svg>\n                  <span className=\"text-sm font-medium text-gray-900\">الرسائل النصية</span>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">إشعارات عبر SMS</p>\n              </div>\n            </label>\n\n            <label className=\"flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={formData.pushNotifications}\n                onChange={(e) => handleInputChange('pushNotifications', e.target.checked)}\n                className=\"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded\"\n              />\n              <div className=\"ml-3\">\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-yellow-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM4 19h6v-6H4v6z\" />\n                  </svg>\n                  <span className=\"text-sm font-medium text-gray-900\">إشعارات فورية</span>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">إشعارات داخل النظام</p>\n              </div>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* إشعارات الفواتير */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <h2 className=\"text-lg font-semibold text-white\">إشعارات الفواتير</h2>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.invoiceCreated}\n                  onChange={(e) => handleInputChange('invoiceCreated', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">إنشاء فاتورة جديدة</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.invoicePaid}\n                  onChange={(e) => handleInputChange('invoicePaid', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">دفع فاتورة</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.invoiceOverdue}\n                  onChange={(e) => handleInputChange('invoiceOverdue', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">فواتير متأخرة السداد</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.paymentReceived}\n                  onChange={(e) => handleInputChange('paymentReceived', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">استلام دفعة</span>\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* إشعارات المخزون */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n              </svg>\n              <h2 className=\"text-lg font-semibold text-white\">إشعارات المخزون</h2>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.lowStockAlert}\n                    onChange={(e) => handleInputChange('lowStockAlert', e.target.checked)}\n                    className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">تنبيه المخزون المنخفض</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.outOfStockAlert}\n                    onChange={(e) => handleInputChange('outOfStockAlert', e.target.checked)}\n                    className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700\">تنبيه نفاد المخزون</span>\n                </label>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">حد التنبيه للمخزون</label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  value={formData.stockThreshold}\n                  onChange={(e) => handleInputChange('stockThreshold', parseInt(e.target.value) || 5)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500\"\n                  placeholder=\"5\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">سيتم إرسال تنبيه عندما يصل المخزون لهذا العدد أو أقل</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* إشعارات النظام */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n              </svg>\n              <h2 className=\"text-lg font-semibold text-white\">إشعارات النظام</h2>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.systemUpdates}\n                  onChange={(e) => handleInputChange('systemUpdates', e.target.checked)}\n                  className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">تحديثات النظام</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.securityAlerts}\n                  onChange={(e) => handleInputChange('securityAlerts', e.target.checked)}\n                  className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">تنبيهات الأمان</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.backupStatus}\n                  onChange={(e) => handleInputChange('backupStatus', e.target.checked)}\n                  className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">حالة النسخ الاحتياطي</span>\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* إعدادات البريد الإلكتروني */}\n        {formData.emailNotifications && (\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n            <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                  <h2 className=\"text-lg font-semibold text-white\">إعدادات البريد الإلكتروني</h2>\n                </div>\n                <button\n                  type=\"button\"\n                  onClick={testEmailSettings}\n                  className=\"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors\"\n                >\n                  اختبار الإعدادات\n                </button>\n              </div>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">خادم البريد الإلكتروني</label>\n                  <input\n                    type=\"text\"\n                    value={formData.emailServer}\n                    onChange={(e) => handleInputChange('emailServer', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                    placeholder=\"smtp.gmail.com\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">المنفذ</label>\n                  <input\n                    type=\"number\"\n                    value={formData.emailPort}\n                    onChange={(e) => handleInputChange('emailPort', parseInt(e.target.value) || 587)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                    placeholder=\"587\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">اسم المستخدم</label>\n                  <input\n                    type=\"email\"\n                    value={formData.emailUsername}\n                    onChange={(e) => handleInputChange('emailUsername', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">كلمة المرور</label>\n                  <input\n                    type=\"password\"\n                    value={formData.emailPassword}\n                    onChange={(e) => handleInputChange('emailPassword', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                    placeholder=\"••••••••\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">نوع الأمان</label>\n                  <select\n                    value={formData.emailSecurity}\n                    onChange={(e) => handleInputChange('emailSecurity', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500\"\n                  >\n                    <option value=\"tls\">TLS</option>\n                    <option value=\"ssl\">SSL</option>\n                    <option value=\"none\">بدون تشفير</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            {loading ? (\n              <span className=\"flex items-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z\"></path>\n                </svg>\n                جاري الحفظ...\n              </span>\n            ) : (\n              <span className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                حفظ إعدادات الإشعارات\n              </span>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAGvD;AAFA;;;AAIe,SAAS,qBAAqB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,oBAAoB,UAAU,sBAAsB;QACpD,kBAAkB,UAAU,oBAAoB;QAChD,mBAAmB,UAAU,qBAAqB;QAElD,mBAAmB;QACnB,gBAAgB,UAAU,kBAAkB;QAC5C,aAAa,UAAU,eAAe;QACtC,gBAAgB,UAAU,kBAAkB;QAC5C,iBAAiB,UAAU,mBAAmB;QAE9C,kBAAkB;QAClB,eAAe,UAAU,iBAAiB;QAC1C,iBAAiB,UAAU,mBAAmB;QAC9C,gBAAgB,UAAU,kBAAkB;QAE5C,iBAAiB;QACjB,eAAe,UAAU,iBAAiB;QAC1C,gBAAgB,UAAU,kBAAkB;QAC5C,cAAc,UAAU,gBAAgB;QAExC,4BAA4B;QAC5B,aAAa,UAAU,eAAe;QACtC,WAAW,UAAU,aAAa;QAClC,eAAe,UAAU,iBAAiB;QAC1C,eAAe,UAAU,iBAAiB;QAC1C,eAAe,UAAU,iBAAiB;QAE1C,yBAAyB;QACzB,aAAa,UAAU,eAAe;QACtC,WAAW,UAAU,aAAa;QAClC,eAAe,UAAU,iBAAiB;IAC5C;IAEA,MAAM,oBAAoB,CAAC,OAAO;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,0CAA0C;YAC1C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,kBAAkB;4CACpC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;4CACzE,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA+B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;sEAEvE,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,gBAAgB;4CAClC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,OAAO;4CACvE,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA+B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;sEAEvE,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,iBAAiB;4CACnC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,OAAO;4CACxE,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA+B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;sEAEvE,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;;;;;;0CAIrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,cAAc;oDAChC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,WAAW;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,OAAO;oDAClE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,cAAc;oDAChC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,eAAe;oDACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,OAAO;oDACtE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;;;;;;0CAIrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,aAAa;4DAC/B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,OAAO;4DACpE,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAG/C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,eAAe;4DACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,OAAO;4DACtE,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAIjD,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDACjF,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;;8DACjF,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;8DACrE,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;sDAEvE,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;;;;;;0CAIrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,aAAa;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDACpE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,cAAc;oDAChC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,YAAY;oDAC9B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,OAAO;oDACnE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOpD,SAAS,kBAAkB,kBAC1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAC5E,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;qDAIR,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 4521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/settings/SecuritySettings.js"], "sourcesContent": ["// frontend/components/settings/SecuritySettings.js\n'use client';\n\nimport { useState } from 'react';\n\nexport default function SecuritySettings({ onSave, loading }) {\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorAuth: false,\n    sessionTimeout: 30,\n    passwordExpiry: 90,\n    loginAttempts: 5,\n    ipWhitelist: '',\n    auditLog: true,\n    encryptBackups: true,\n    requireStrongPassword: true\n  });\n\n  const [sessions, setSessions] = useState([\n    {\n      id: 1,\n      device: 'Chrome on Windows',\n      location: 'الرياض, السعودية',\n      lastActive: new Date(Date.now() - 5 * 60 * 1000),\n      current: true\n    },\n    {\n      id: 2,\n      device: 'Safari on iPhone',\n      location: 'جدة, السعودية',\n      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      current: false\n    }\n  ]);\n\n  const handlePasswordChange = (field, value) => {\n    setPasswordData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSecurityChange = (field, value) => {\n    setSecuritySettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handlePasswordSubmit = (e) => {\n    e.preventDefault();\n    \n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('كلمات المرور الجديدة غير متطابقة');\n      return;\n    }\n\n    if (passwordData.newPassword.length < 8) {\n      alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');\n      return;\n    }\n\n    onSave({ type: 'password', data: passwordData });\n    setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n  };\n\n  const handleSecuritySubmit = (e) => {\n    e.preventDefault();\n    onSave({ type: 'security', data: securitySettings });\n  };\n\n  const terminateSession = (sessionId) => {\n    if (window.confirm('هل أنت متأكد من إنهاء هذه الجلسة؟')) {\n      setSessions(prev => prev.filter(session => session.id !== sessionId));\n    }\n  };\n\n  const terminateAllSessions = () => {\n    if (window.confirm('هل أنت متأكد من إنهاء جميع الجلسات الأخرى؟')) {\n      setSessions(prev => prev.filter(session => session.current));\n    }\n  };\n\n  const formatLastActive = (date) => {\n    const now = new Date();\n    const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'نشط الآن';\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `منذ ${diffInDays} يوم`;\n  };\n\n  const getPasswordStrength = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    \n    return strength;\n  };\n\n  const getStrengthColor = (strength) => {\n    if (strength <= 2) return 'bg-red-500';\n    if (strength <= 3) return 'bg-yellow-500';\n    if (strength <= 4) return 'bg-blue-500';\n    return 'bg-green-500';\n  };\n\n  const getStrengthText = (strength) => {\n    if (strength <= 2) return 'ضعيفة';\n    if (strength <= 3) return 'متوسطة';\n    if (strength <= 4) return 'قوية';\n    return 'قوية جداً';\n  };\n\n  const passwordStrength = getPasswordStrength(passwordData.newPassword);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* تغيير كلمة المرور */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">تغيير كلمة المرور</h2>\n          </div>\n        </div>\n\n        <form onSubmit={handlePasswordSubmit} className=\"p-6\">\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">كلمة المرور الحالية</label>\n              <input\n                type=\"password\"\n                value={passwordData.currentPassword}\n                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}\n                className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-200 transition-all duration-200\"\n                placeholder=\"أدخل كلمة المرور الحالية\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">كلمة المرور الجديدة</label>\n              <input\n                type=\"password\"\n                value={passwordData.newPassword}\n                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}\n                className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-200 transition-all duration-200\"\n                placeholder=\"أدخل كلمة المرور الجديدة\"\n                required\n              />\n              \n              {passwordData.newPassword && (\n                <div className=\"mt-2\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">قوة كلمة المرور:</span>\n                    <span className={`font-medium ${\n                      passwordStrength <= 2 ? 'text-red-600' :\n                      passwordStrength <= 3 ? 'text-yellow-600' :\n                      passwordStrength <= 4 ? 'text-blue-600' : 'text-green-600'\n                    }`}>\n                      {getStrengthText(passwordStrength)}\n                    </span>\n                  </div>\n                  <div className=\"mt-1 w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(passwordStrength)}`}\n                      style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                    ></div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">تأكيد كلمة المرور الجديدة</label>\n              <input\n                type=\"password\"\n                value={passwordData.confirmPassword}\n                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}\n                className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-200 transition-all duration-200\"\n                placeholder=\"أعد إدخال كلمة المرور الجديدة\"\n                required\n              />\n              \n              {passwordData.confirmPassword && passwordData.newPassword !== passwordData.confirmPassword && (\n                <p className=\"mt-1 text-sm text-red-600\">كلمات المرور غير متطابقة</p>\n              )}\n            </div>\n\n            <div className=\"flex justify-end\">\n              <button\n                type=\"submit\"\n                disabled={loading || passwordData.newPassword !== passwordData.confirmPassword}\n                className=\"px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white font-medium rounded-lg hover:from-red-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n              >\n                تغيير كلمة المرور\n              </button>\n            </div>\n          </div>\n        </form>\n      </div>\n\n      {/* إعدادات الأمان */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n            </svg>\n            <h2 className=\"text-lg font-semibold text-white\">إعدادات الأمان</h2>\n          </div>\n        </div>\n\n        <form onSubmit={handleSecuritySubmit} className=\"p-6\">\n          <div className=\"space-y-6\">\n            {/* المصادقة الثنائية */}\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">المصادقة الثنائية (2FA)</h3>\n                <p className=\"text-sm text-gray-500\">طبقة حماية إضافية لحسابك</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={securitySettings.twoFactorAuth}\n                  onChange={(e) => handleSecurityChange('twoFactorAuth', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600\"></div>\n              </label>\n            </div>\n\n            {/* إعدادات الجلسة */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">انتهاء الجلسة (دقيقة)</label>\n                <input\n                  type=\"number\"\n                  min=\"5\"\n                  max=\"480\"\n                  value={securitySettings.sessionTimeout}\n                  onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">انتهاء كلمة المرور (يوم)</label>\n                <input\n                  type=\"number\"\n                  min=\"30\"\n                  max=\"365\"\n                  value={securitySettings.passwordExpiry}\n                  onChange={(e) => handleSecurityChange('passwordExpiry', parseInt(e.target.value))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">محاولات تسجيل الدخول</label>\n                <input\n                  type=\"number\"\n                  min=\"3\"\n                  max=\"10\"\n                  value={securitySettings.loginAttempts}\n                  onChange={(e) => handleSecurityChange('loginAttempts', parseInt(e.target.value))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">عناوين IP المسموحة</label>\n                <input\n                  type=\"text\"\n                  value={securitySettings.ipWhitelist}\n                  onChange={(e) => handleSecurityChange('ipWhitelist', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500\"\n                  placeholder=\"***********, ********\"\n                />\n              </div>\n            </div>\n\n            {/* خيارات إضافية */}\n            <div className=\"space-y-4\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={securitySettings.auditLog}\n                  onChange={(e) => handleSecurityChange('auditLog', e.target.checked)}\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">تسجيل جميع العمليات (Audit Log)</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={securitySettings.encryptBackups}\n                  onChange={(e) => handleSecurityChange('encryptBackups', e.target.checked)}\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">تشفير النسخ الاحتياطية</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={securitySettings.requireStrongPassword}\n                  onChange={(e) => handleSecurityChange('requireStrongPassword', e.target.checked)}\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">إجبار كلمات مرور قوية</span>\n              </label>\n            </div>\n\n            <div className=\"flex justify-end\">\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n              >\n                حفظ إعدادات الأمان\n              </button>\n            </div>\n          </div>\n        </form>\n      </div>\n\n      {/* الجلسات النشطة */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-6 h-6 text-white mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n              </svg>\n              <h2 className=\"text-lg font-semibold text-white\">الجلسات النشطة</h2>\n            </div>\n            <button\n              onClick={terminateAllSessions}\n              className=\"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors\"\n            >\n              إنهاء جميع الجلسات\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {sessions.map((session) => (\n              <div key={session.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-medium text-gray-900\">{session.device}</span>\n                      {session.current && (\n                        <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">الجلسة الحالية</span>\n                      )}\n                    </div>\n                    <p className=\"text-sm text-gray-500\">{session.location}</p>\n                    <p className=\"text-xs text-gray-400\">{formatLastActive(session.lastActive)}</p>\n                  </div>\n                </div>\n                {!session.current && (\n                  <button\n                    onClick={() => terminateSession(session.id)}\n                    className=\"px-3 py-1 text-sm text-red-600 hover:text-red-800 font-medium\"\n                  >\n                    إنهاء الجلسة\n                  </button>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AAGnD;AAFA;;;AAIe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,uBAAuB;IACzB;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK;YAC3C,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK;YAChD,SAAS;QACX;KACD;IAED,MAAM,uBAAuB,CAAC,OAAO;QACnC,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,OAAO;QACnC,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAEhB,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,EAAE;YAC7D,MAAM;YACN;QACF;QAEA,IAAI,aAAa,WAAW,CAAC,MAAM,GAAG,GAAG;YACvC,MAAM;YACN;QACF;QAEA,OAAO;YAAE,MAAM;YAAY,MAAM;QAAa;QAC9C,gBAAgB;YAAE,iBAAiB;YAAI,aAAa;YAAI,iBAAiB;QAAG;IAC9E;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,OAAO;YAAE,MAAM;YAAY,MAAM;QAAiB;IACpD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,OAAO,CAAC,sCAAsC;YACvD,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC5D;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,OAAO,OAAO,CAAC,+CAA+C;YAChE,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO;QAC5D;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;QAE1D,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC;QAE3D,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC;QAEtD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC;IAChC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,WAAW;QACf,IAAI,SAAS,MAAM,IAAI,GAAG;QAC1B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;QAEnC,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY,GAAG,OAAO;QAC1B,IAAI,YAAY,GAAG,OAAO;QAC1B,IAAI,YAAY,GAAG,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY,GAAG,OAAO;QAC1B,IAAI,YAAY,GAAG,OAAO;QAC1B,IAAI,YAAY,GAAG,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,mBAAmB,oBAAoB,aAAa,WAAW;IAErE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAK,UAAU;wBAAsB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,aAAa,eAAe;4CACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CACvE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,aAAa,WAAW;4CAC/B,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;4CACnE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;wCAGT,aAAa,WAAW,kBACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAW,CAAC,YAAY,EAC5B,oBAAoB,IAAI,iBACxB,oBAAoB,IAAI,oBACxB,oBAAoB,IAAI,kBAAkB,kBAC1C;sEACC,gBAAgB;;;;;;;;;;;;8DAGrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,iBAAiB,mBAAmB;wDAC/F,OAAO;4DAAE,OAAO,GAAG,AAAC,mBAAmB,IAAK,IAAI,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,aAAa,eAAe;4CACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CACvE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;wCAGT,aAAa,eAAe,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,kBACxF,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU,WAAW,aAAa,WAAW,KAAK,aAAa,eAAe;wCAC9E,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAK,UAAU;wBAAsB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,iBAAiB,aAAa;oDACvC,UAAU,CAAC,IAAM,qBAAqB,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDACvE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,iBAAiB,cAAc;oDACtC,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC/E,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,iBAAiB,cAAc;oDACtC,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC/E,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,iBAAiB,aAAa;oDACrC,UAAU,CAAC,IAAM,qBAAqB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC9E,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,iBAAiB,WAAW;oDACnC,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;oDACnE,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,iBAAiB,QAAQ;oDAClC,UAAU,CAAC,IAAM,qBAAqB,YAAY,EAAE,MAAM,CAAC,OAAO;oDAClE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,iBAAiB,cAAc;oDACxC,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACxE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,iBAAiB,qBAAqB;oDAC/C,UAAU,CAAC,IAAM,qBAAqB,yBAAyB,EAAE,MAAM,CAAC,OAAO;oDAC/E,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAIjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;sDAEvE,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAEnD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAqC,QAAQ,MAAM;;;;;;gEAClE,QAAQ,OAAO,kBACd,8OAAC;oEAAK,WAAU;8EAA6D;;;;;;;;;;;;sEAGjF,8OAAC;4DAAE,WAAU;sEAAyB,QAAQ,QAAQ;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAyB,iBAAiB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;wCAG5E,CAAC,QAAQ,OAAO,kBACf,8OAAC;4CACC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4CAC1C,WAAU;sDACX;;;;;;;mCAtBK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiClC", "debugId": null}}, {"offset": {"line": 5433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/settings/page.js"], "sourcesContent": ["// frontend/app/dashboard/settings/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport withAuth from '@/components/withAuth';\nimport CompanySettings from '@/components/settings/CompanySettings';\nimport InvoiceSettings from '@/components/settings/InvoiceSettings';\nimport BackupSettings from '@/components/settings/BackupSettings';\nimport NotificationSettings from '@/components/settings/NotificationSettings';\nimport SecuritySettings from '@/components/settings/SecuritySettings';\n\nfunction SettingsPage() {\n  const [activeTab, setActiveTab] = useState('company');\n  const [settings, setSettings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  const loadSettings = async () => {\n    try {\n      // تحميل الإعدادات من الخدمة\n      const settingsService = (await import('@/lib/settingsService')).default;\n      const loadedSettings = await settingsService.loadSettings();\n      setSettings(loadedSettings);\n    } catch (loadError) {\n      console.error('Error loading settings:', loadError);\n      // في حالة الخطأ، استخدم الإعدادات الافتراضية\n      const mockSettings = {\n        company: {\n          companyName: '',\n          companyAddress: 'الرياض، المملكة العربية السعودية',\n          companyPhone: '+966 11 123 4567',\n          companyEmail: '<EMAIL>',\n          companyWebsite: 'https://www.techcompany.com',\n          taxId: '*********',\n          currency: 'SAR',\n          logoUrl: null\n        },\n        invoice: {\n          invoicePrefix: 'INV',\n          invoiceNumberLength: 6,\n          defaultTaxRate: 15,\n          defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n          autoGenerateInvoiceNumber: true,\n          showImagesInInvoice: true,\n          allowPartialPayments: true,\n          requireCustomerInfo: true,\n          defaultDueDays: 30,\n          invoiceFooter: 'شكراً لتعاملكم معنا',\n          invoiceNotes: 'يرجى الدفع في الموعد المحدد'\n        },\n        notifications: {\n          emailNotifications: true,\n          smsNotifications: false,\n          pushNotifications: true,\n          invoiceCreated: true,\n          invoicePaid: true,\n          invoiceOverdue: true,\n          paymentReceived: true,\n          lowStockAlert: true,\n          outOfStockAlert: true,\n          stockThreshold: 5,\n          systemUpdates: true,\n          securityAlerts: true,\n          backupStatus: true,\n          emailServer: 'smtp.gmail.com',\n          emailPort: 587,\n          emailUsername: '',\n          emailPassword: '',\n          emailSecurity: 'tls'\n        }\n      };\n      setSettings(mockSettings);\n    }\n  };\n\n  const handleSave = async (tabType, data) => {\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const settingsService = (await import('@/lib/settingsService')).default;\n\n      // حفظ الإعدادات حسب النوع\n      if (tabType === 'company') {\n        await settingsService.saveCompanySettings(data);\n      } else if (tabType === 'invoice') {\n        await settingsService.saveInvoiceSettings(data);\n      } else {\n        // للأنواع الأخرى، حفظ عام\n        const currentSettings = await settingsService.loadSettings();\n        await settingsService.saveSettings({\n          ...currentSettings,\n          [tabType]: { ...currentSettings[tabType], ...data }\n        });\n      }\n\n      // تحديث الحالة المحلية\n      setSettings(prev => ({\n        ...prev,\n        [tabType]: { ...prev[tabType], ...data }\n      }));\n\n      setSuccess('تم حفظ الإعدادات بنجاح');\n\n      // إخفاء رسالة النجاح بعد 3 ثوان\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBackup = async (options) => {\n    setLoading(true);\n    try {\n      // محاكاة إنشاء نسخة احتياطية\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // إنشاء ملف JSON للتحميل\n      const backupData = {\n        timestamp: new Date().toISOString(),\n        version: '1.0',\n        data: {\n          settings,\n          // يمكن إضافة بيانات أخرى هنا\n        }\n      };\n\n      const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;\n      link.click();\n      URL.revokeObjectURL(url);\n\n      setSuccess('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('فشل في إنشاء النسخة الاحتياطية');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRestore = async (backupData) => {\n    setLoading(true);\n    try {\n      // محاكاة استعادة النسخة الاحتياطية\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      if (backupData instanceof File) {\n        const text = await backupData.text();\n        const data = JSON.parse(text);\n        setSettings(data.data.settings || {});\n      }\n\n      setSuccess('تم استعادة النسخة الاحتياطية بنجاح');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('فشل في استعادة النسخة الاحتياطية');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSecuritySave = async (securityData) => {\n    setLoading(true);\n    try {\n      // محاكاة حفظ إعدادات الأمان\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      if (securityData.type === 'password') {\n        setSuccess('تم تغيير كلمة المرور بنجاح');\n      } else {\n        setSuccess('تم حفظ إعدادات الأمان بنجاح');\n      }\n\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('فشل في حفظ إعدادات الأمان');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    {\n      id: 'company',\n      name: 'معلومات الشركة',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10\" />\n        </svg>\n      )\n    },\n    {\n      id: 'invoice',\n      name: 'إعدادات الفواتير',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      id: 'notifications',\n      name: 'الإشعارات',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM4 19h6v-6H4v6z\" />\n        </svg>\n      )\n    },\n    {\n      id: 'security',\n      name: 'الأمان',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n        </svg>\n      )\n    },\n    {\n      id: 'backup',\n      name: 'النسخ الاحتياطي',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.back()}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                العودة\n              </button>\n              <div className=\"h-6 w-px bg-gray-300\"></div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">إعدادات النظام</h1>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* رسائل النجاح والخطأ */}\n        {success && (\n          <div className=\"mb-6 bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-green-700 font-medium\">{success}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* التبويبات الجانبية */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8\">\n              <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n                <h2 className=\"text-lg font-semibold text-white\">أقسام الإعدادات</h2>\n              </div>\n              <nav className=\"p-2\">\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 mb-1 ${\n                      activeTab === tab.id\n                        ? 'bg-indigo-50 text-indigo-700 border-r-4 border-indigo-500'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${activeTab === tab.id ? 'text-indigo-500' : 'text-gray-400'}`}>\n                      {tab.icon}\n                    </span>\n                    {tab.name}\n                  </button>\n                ))}\n              </nav>\n            </div>\n          </div>\n\n          {/* المحتوى الرئيسي */}\n          <div className=\"lg:col-span-3\">\n            {activeTab === 'company' && (\n              <CompanySettings\n                settings={settings.company}\n                onSave={(data) => handleSave('company', data)}\n                loading={loading}\n              />\n            )}\n\n            {activeTab === 'invoice' && (\n              <InvoiceSettings\n                settings={settings.invoice}\n                onSave={(data) => handleSave('invoice', data)}\n                onRefresh={loadSettings}\n                loading={loading}\n              />\n            )}\n\n            {activeTab === 'notifications' && (\n              <NotificationSettings\n                settings={settings.notifications}\n                onSave={(data) => handleSave('notifications', data)}\n                loading={loading}\n              />\n            )}\n\n            {activeTab === 'security' && (\n              <SecuritySettings\n                onSave={handleSecuritySave}\n                loading={loading}\n              />\n            )}\n\n            {activeTab === 'backup' && (\n              <BackupSettings\n                onBackup={handleBackup}\n                onRestore={handleRestore}\n                loading={loading}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default withAuth(SettingsPage);\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AAG1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,SAAS;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,4BAA4B;YAC5B,MAAM,kBAAkB,CAAC,+HAAqC,EAAE,OAAO;YACvE,MAAM,iBAAiB,MAAM,gBAAgB,YAAY;YACzD,YAAY;QACd,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,6CAA6C;YAC7C,MAAM,eAAe;gBACnB,SAAS;oBACP,aAAa;oBACb,gBAAgB;oBAChB,cAAc;oBACd,cAAc;oBACd,gBAAgB;oBAChB,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;gBACA,SAAS;oBACP,eAAe;oBACf,qBAAqB;oBACrB,gBAAgB;oBAChB,qBAAqB;oBACrB,2BAA2B;oBAC3B,qBAAqB;oBACrB,sBAAsB;oBACtB,qBAAqB;oBACrB,gBAAgB;oBAChB,eAAe;oBACf,cAAc;gBAChB;gBACA,eAAe;oBACb,oBAAoB;oBACpB,kBAAkB;oBAClB,mBAAmB;oBACnB,gBAAgB;oBAChB,aAAa;oBACb,gBAAgB;oBAChB,iBAAiB;oBACjB,eAAe;oBACf,iBAAiB;oBACjB,gBAAgB;oBAChB,eAAe;oBACf,gBAAgB;oBAChB,cAAc;oBACd,aAAa;oBACb,WAAW;oBACX,eAAe;oBACf,eAAe;oBACf,eAAe;gBACjB;YACF;YACA,YAAY;QACd;IACF;IAEA,MAAM,aAAa,OAAO,SAAS;QACjC,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,kBAAkB,CAAC,+HAAqC,EAAE,OAAO;YAEvE,0BAA0B;YAC1B,IAAI,YAAY,WAAW;gBACzB,MAAM,gBAAgB,mBAAmB,CAAC;YAC5C,OAAO,IAAI,YAAY,WAAW;gBAChC,MAAM,gBAAgB,mBAAmB,CAAC;YAC5C,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,kBAAkB,MAAM,gBAAgB,YAAY;gBAC1D,MAAM,gBAAgB,YAAY,CAAC;oBACjC,GAAG,eAAe;oBAClB,CAAC,QAAQ,EAAE;wBAAE,GAAG,eAAe,CAAC,QAAQ;wBAAE,GAAG,IAAI;oBAAC;gBACpD;YACF;YAEA,uBAAuB;YACvB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,QAAQ,EAAE;wBAAE,GAAG,IAAI,CAAC,QAAQ;wBAAE,GAAG,IAAI;oBAAC;gBACzC,CAAC;YAED,WAAW;YAEX,gCAAgC;YAChC,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,6BAA6B;YAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yBAAyB;YACzB,MAAM,aAAa;gBACjB,WAAW,IAAI,OAAO,WAAW;gBACjC,SAAS;gBACT,MAAM;oBACJ;gBAEF;YACF;YAEA,MAAM,OAAO,IAAI,KAAK;gBAAC,KAAK,SAAS,CAAC,YAAY,MAAM;aAAG,EAAE;gBAAE,MAAM;YAAmB;YACxF,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,OAAO,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACvE,KAAK,KAAK;YACV,IAAI,eAAe,CAAC;YAEpB,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,IAAI;YACF,mCAAmC;YACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,sBAAsB,MAAM;gBAC9B,MAAM,OAAO,MAAM,WAAW,IAAI;gBAClC,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;YACrC;YAEA,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,IAAI;YACF,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,aAAa,IAAI,KAAK,YAAY;gBACpC,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YAEA,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO,IAAI;oCAC1B,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;oBAEZ,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAyB,SAAQ;wCAAY,MAAK;kDAC/D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwI,UAAS;;;;;;;;;;;;;;;;8CAGhL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;oBAM1D,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oDAClC,WAAW,CAAC,mGAAmG,EAC7G,cAAc,IAAI,EAAE,GAChB,8DACA,sDACJ;;sEAEF,8OAAC;4DAAK,WAAW,CAAC,KAAK,EAAE,cAAc,IAAI,EAAE,GAAG,oBAAoB,iBAAiB;sEAClF,IAAI,IAAI;;;;;;wDAEV,IAAI,IAAI;;mDAXJ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;0CAmBrB,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,2BACb,8OAAC,yIAAA,CAAA,UAAe;wCACd,UAAU,SAAS,OAAO;wCAC1B,QAAQ,CAAC,OAAS,WAAW,WAAW;wCACxC,SAAS;;;;;;oCAIZ,cAAc,2BACb,8OAAC,yIAAA,CAAA,UAAe;wCACd,UAAU,SAAS,OAAO;wCAC1B,QAAQ,CAAC,OAAS,WAAW,WAAW;wCACxC,WAAW;wCACX,SAAS;;;;;;oCAIZ,cAAc,iCACb,8OAAC,8IAAA,CAAA,UAAoB;wCACnB,UAAU,SAAS,aAAa;wCAChC,QAAQ,CAAC,OAAS,WAAW,iBAAiB;wCAC9C,SAAS;;;;;;oCAIZ,cAAc,4BACb,8OAAC,0IAAA,CAAA,UAAgB;wCACf,QAAQ;wCACR,SAAS;;;;;;oCAIZ,cAAc,0BACb,8OAAC,wIAAA,CAAA,UAAc;wCACb,UAAU;wCACV,WAAW;wCACX,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;uCAEe,CAAA,GAAA,sHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}