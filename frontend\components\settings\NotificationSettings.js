// frontend/components/settings/NotificationSettings.js
'use client';

import { useState } from 'react';

export default function NotificationSettings({ settings, onSave, loading }) {
  const [formData, setFormData] = useState({
    emailNotifications: settings?.emailNotifications ?? true,
    smsNotifications: settings?.smsNotifications ?? false,
    pushNotifications: settings?.pushNotifications ?? true,
    
    // إشعارات الفواتير
    invoiceCreated: settings?.invoiceCreated ?? true,
    invoicePaid: settings?.invoicePaid ?? true,
    invoiceOverdue: settings?.invoiceOverdue ?? true,
    paymentReceived: settings?.paymentReceived ?? true,
    
    // إشعارات المخزون
    lowStockAlert: settings?.lowStockAlert ?? true,
    outOfStockAlert: settings?.outOfStockAlert ?? true,
    stockThreshold: settings?.stockThreshold || 5,
    
    // إشعارات النظام
    systemUpdates: settings?.systemUpdates ?? true,
    securityAlerts: settings?.securityAlerts ?? true,
    backupStatus: settings?.backupStatus ?? true,
    
    // إعدادات البريد الإلكتروني
    emailServer: settings?.emailServer || '',
    emailPort: settings?.emailPort || 587,
    emailUsername: settings?.emailUsername || '',
    emailPassword: settings?.emailPassword || '',
    emailSecurity: settings?.emailSecurity || 'tls',
    
    // إعدادات الرسائل النصية
    smsProvider: settings?.smsProvider || '',
    smsApiKey: settings?.smsApiKey || '',
    smsFromNumber: settings?.smsFromNumber || ''
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const testEmailSettings = async () => {
    try {
      // محاكاة اختبار إعدادات البريد الإلكتروني
      alert('تم إرسال رسالة اختبار بنجاح!');
    } catch (error) {
      alert('فشل في إرسال رسالة الاختبار. يرجى التحقق من الإعدادات.');
    }
  };

  return (
    <div className="space-y-6">
      {/* إعدادات الإشعارات العامة */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-yellow-500 to-orange-600 px-6 py-4">
          <div className="flex items-center">
            <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z" />
            </svg>
            <h2 className="text-lg font-semibold text-white">طرق الإشعارات</h2>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <label className="flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.emailNotifications}
                onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
              />
              <div className="ml-3">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-900">البريد الإلكتروني</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">إشعارات عبر البريد الإلكتروني</p>
              </div>
            </label>

            <label className="flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.smsNotifications}
                onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
              />
              <div className="ml-3">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-900">الرسائل النصية</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">إشعارات عبر SMS</p>
              </div>
            </label>

            <label className="flex items-center p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.pushNotifications}
                onChange={(e) => handleInputChange('pushNotifications', e.target.checked)}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
              />
              <div className="ml-3">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-900">إشعارات فورية</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">إشعارات داخل النظام</p>
              </div>
            </label>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* إشعارات الفواتير */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h2 className="text-lg font-semibold text-white">إشعارات الفواتير</h2>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.invoiceCreated}
                  onChange={(e) => handleInputChange('invoiceCreated', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">إنشاء فاتورة جديدة</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.invoicePaid}
                  onChange={(e) => handleInputChange('invoicePaid', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">دفع فاتورة</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.invoiceOverdue}
                  onChange={(e) => handleInputChange('invoiceOverdue', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">فواتير متأخرة السداد</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.paymentReceived}
                  onChange={(e) => handleInputChange('paymentReceived', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">استلام دفعة</span>
              </label>
            </div>
          </div>
        </div>

        {/* إشعارات المخزون */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <h2 className="text-lg font-semibold text-white">إشعارات المخزون</h2>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.lowStockAlert}
                    onChange={(e) => handleInputChange('lowStockAlert', e.target.checked)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">تنبيه المخزون المنخفض</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.outOfStockAlert}
                    onChange={(e) => handleInputChange('outOfStockAlert', e.target.checked)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">تنبيه نفاد المخزون</span>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">حد التنبيه للمخزون</label>
                <input
                  type="number"
                  min="1"
                  value={formData.stockThreshold}
                  onChange={(e) => handleInputChange('stockThreshold', parseInt(e.target.value) || 5)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500"
                  placeholder="5"
                />
                <p className="text-xs text-gray-500 mt-1">سيتم إرسال تنبيه عندما يصل المخزون لهذا العدد أو أقل</p>
              </div>
            </div>
          </div>
        </div>

        {/* إشعارات النظام */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <h2 className="text-lg font-semibold text-white">إشعارات النظام</h2>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.systemUpdates}
                  onChange={(e) => handleInputChange('systemUpdates', e.target.checked)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">تحديثات النظام</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.securityAlerts}
                  onChange={(e) => handleInputChange('securityAlerts', e.target.checked)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">تنبيهات الأمان</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.backupStatus}
                  onChange={(e) => handleInputChange('backupStatus', e.target.checked)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">حالة النسخ الاحتياطي</span>
              </label>
            </div>
          </div>
        </div>

        {/* إعدادات البريد الإلكتروني */}
        {formData.emailNotifications && (
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <h2 className="text-lg font-semibold text-white">إعدادات البريد الإلكتروني</h2>
                </div>
                <button
                  type="button"
                  onClick={testEmailSettings}
                  className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-lg hover:bg-opacity-30 transition-colors"
                >
                  اختبار الإعدادات
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">خادم البريد الإلكتروني</label>
                  <input
                    type="text"
                    value={formData.emailServer}
                    onChange={(e) => handleInputChange('emailServer', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                    placeholder="smtp.gmail.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المنفذ</label>
                  <input
                    type="number"
                    value={formData.emailPort}
                    onChange={(e) => handleInputChange('emailPort', parseInt(e.target.value) || 587)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                    placeholder="587"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                  <input
                    type="email"
                    value={formData.emailUsername}
                    onChange={(e) => handleInputChange('emailUsername', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                  <input
                    type="password"
                    value={formData.emailPassword}
                    onChange={(e) => handleInputChange('emailPassword', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                    placeholder="••••••••"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الأمان</label>
                  <select
                    value={formData.emailSecurity}
                    onChange={(e) => handleInputChange('emailSecurity', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500"
                  >
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                    <option value="none">بدون تشفير</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            {loading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l-3-2.647z"></path>
                </svg>
                جاري الحفظ...
              </span>
            ) : (
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                حفظ إعدادات الإشعارات
              </span>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
