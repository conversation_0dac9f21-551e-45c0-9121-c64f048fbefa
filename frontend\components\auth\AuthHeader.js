// frontend/components/auth/AuthHeader.js
'use client';

export default function AuthHeader({ title, subtitle, showLogo = true }) {
  return (
    <div className="sm:mx-auto sm:w-full sm:max-w-md relative z-10">
      {showLogo && (
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
      )}
      
      <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
        {title}
      </h2>
      
      {subtitle && (
        <p className="mt-2 text-center text-sm text-gray-600">
          {subtitle}
        </p>
      )}
    </div>
  );
}
