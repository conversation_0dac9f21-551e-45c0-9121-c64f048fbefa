{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  return apiRequest(`${API_BASE_URL}/customers`, {\n    method: 'POST',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function getCustomerById(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, { method: 'GET' }, true);\n}\n\nexport async function updateCustomer(id, customerData) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function deleteCustomer(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'DELETE',\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QAAE,QAAQ;IAAM,GAAG;AAC1E;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/invoices/%5Bid%5D/page.js"], "sourcesContent": ["// frontend/app/dashboard/invoices/[id]/page.js\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport Image from 'next/image';\nimport { getInvoiceById, addPayment } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\n\nfunction InvoiceDetailPage() {\n  const [invoice, setInvoice] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentData, setPaymentData] = useState({\n    amount: '',\n    method: 'cash',\n    reference: '',\n    notes: ''\n  });\n  const [paymentLoading, setPaymentLoading] = useState(false);\n\n  const router = useRouter();\n  const params = useParams();\n  const invoiceId = params.id;\n\n  useEffect(() => {\n    loadInvoice();\n  }, [invoiceId]);\n\n  const loadInvoice = async () => {\n    try {\n      const data = await getInvoiceById(invoiceId);\n      setInvoice(data);\n    } catch (err) {\n      setError('Failed to load invoice. Please try again.');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddPayment = async (e) => {\n    e.preventDefault();\n    setPaymentLoading(true);\n\n    try {\n      await addPayment(invoiceId, paymentData);\n      setShowPaymentModal(false);\n      setPaymentData({ amount: '', method: 'cash', reference: '', notes: '' });\n      loadInvoice(); // إعادة تحميل الفاتورة\n    } catch (err) {\n      setError(err.message || 'Failed to add payment');\n    } finally {\n      setPaymentLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const formatDate = (date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      draft: 'bg-gray-100 text-gray-800',\n      sent: 'bg-blue-100 text-blue-800',\n      paid: 'bg-green-100 text-green-800',\n      overdue: 'bg-red-100 text-red-800',\n      cancelled: 'bg-red-100 text-red-800'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  const getPaymentStatusColor = (status) => {\n    const colors = {\n      unpaid: 'bg-red-100 text-red-800',\n      partial: 'bg-yellow-100 text-yellow-800',\n      paid: 'bg-green-100 text-green-800'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  const remainingAmount = invoice ? parseFloat(invoice.totalAmount) - parseFloat(invoice.paidAmount) : 0;\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-xl\">Loading invoice...</div>\n      </div>\n    );\n  }\n\n  if (error && !invoice) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-xl mb-4\">{error}</div>\n          <button\n            onClick={() => router.back()}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n          >\n            العودة\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => router.back()}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                العودة\n              </button>\n              <div className=\"h-6 w-px bg-gray-300\"></div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">فاتورة {invoice?.invoiceNumber}</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {invoice?.paymentStatus !== 'paid' && (\n                <button\n                  onClick={() => setShowPaymentModal(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                  إضافة دفعة\n                </button>\n              )}\n              <button\n                onClick={() => window.print()}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n                </svg>\n                طباعة\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* معلومات الفاتورة الرئيسية */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* معلومات الفاتورة والعميل */}\n            <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n              <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-lg font-semibold text-white\">معلومات الفاتورة</h2>\n                  <div className=\"flex space-x-2\">\n                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice?.status)}`}>\n                      {invoice?.status}\n                    </span>\n                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(invoice?.paymentStatus)}`}>\n                      {invoice?.paymentStatus}\n                    </span>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"p-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل الفاتورة</h3>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                        <span className=\"font-medium\">{invoice?.invoiceNumber}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                        <span className=\"font-medium\">{formatDate(invoice?.issueDate)}</span>\n                      </div>\n                      {invoice?.dueDate && (\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">تاريخ الاستحقاق:</span>\n                          <span className=\"font-medium\">{formatDate(invoice?.dueDate)}</span>\n                        </div>\n                      )}\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">العملة:</span>\n                        <span className=\"font-medium\">{invoice?.currency}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات العميل</h3>\n                    <div className=\"space-y-2\">\n                      <div>\n                        <span className=\"text-gray-600\">الاسم:</span>\n                        <div className=\"font-medium\">{invoice?.customer?.name}</div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">البريد الإلكتروني:</span>\n                        <div className=\"font-medium\">{invoice?.customer?.email}</div>\n                      </div>\n                      {invoice?.customer?.phone && (\n                        <div>\n                          <span className=\"text-gray-600\">الهاتف:</span>\n                          <div className=\"font-medium\">{invoice?.customer?.phone}</div>\n                        </div>\n                      )}\n                      {invoice?.customer?.address && (\n                        <div>\n                          <span className=\"text-gray-600\">العنوان:</span>\n                          <div className=\"font-medium\">{invoice?.customer?.address}</div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* عناصر الفاتورة */}\n            <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n              <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4\">\n                <h2 className=\"text-lg font-semibold text-white\">عناصر الفاتورة</h2>\n              </div>\n              \n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">المنتج</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">الكمية</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">السعر</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">خصم</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">المجموع</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {invoice?.items?.map((item, index) => (\n                      <tr key={index} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            {invoice.showImages && item.product.images && item.product.images.length > 0 && (\n                              <div className=\"flex-shrink-0 h-12 w-12 mr-4\">\n                                <Image\n                                  src={`http://localhost:5000/${item.product.images[0].path}`}\n                                  alt={item.product.name}\n                                  width={48}\n                                  height={48}\n                                  className=\"h-12 w-12 rounded-lg object-cover border border-gray-200\"\n                                />\n                              </div>\n                            )}\n                            <div>\n                              <div className=\"text-sm font-medium text-gray-900\">{item.product.name}</div>\n                              <div className=\"text-sm text-gray-500\">{item.product.description}</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {item.quantity}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {formatCurrency(item.unitPrice)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {item.discount}%\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                          {formatCurrency(item.total)}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            {/* الملاحظات والشروط */}\n            {(invoice?.notes || invoice?.terms) && (\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4\">\n                  <h2 className=\"text-lg font-semibold text-white\">ملاحظات وشروط</h2>\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    {invoice?.notes && (\n                      <div>\n                        <h3 className=\"text-sm font-semibold text-gray-700 mb-2\">ملاحظات</h3>\n                        <p className=\"text-gray-600 text-sm\">{invoice.notes}</p>\n                      </div>\n                    )}\n                    {invoice?.terms && (\n                      <div>\n                        <h3 className=\"text-sm font-semibold text-gray-700 mb-2\">شروط الدفع</h3>\n                        <p className=\"text-gray-600 text-sm\">{invoice.terms}</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* الشريط الجانبي - الملخص والمدفوعات */}\n          <div className=\"lg:col-span-1 space-y-6\">\n            {/* ملخص المبالغ */}\n            <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden sticky top-8\">\n              <div className=\"bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4\">\n                <h2 className=\"text-lg font-semibold text-white\">ملخص المبالغ</h2>\n              </div>\n              \n              <div className=\"p-6 space-y-4\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">المجموع الفرعي:</span>\n                  <span className=\"font-medium\">{formatCurrency(invoice?.subtotal)}</span>\n                </div>\n                \n                {invoice?.discountAmount > 0 && (\n                  <div className=\"flex justify-between text-green-600\">\n                    <span>خصم ({invoice?.discountRate}%):</span>\n                    <span>-{formatCurrency(invoice?.discountAmount)}</span>\n                  </div>\n                )}\n                \n                {invoice?.taxAmount > 0 && (\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">ضريبة ({invoice?.taxRate}%):</span>\n                    <span className=\"font-medium\">{formatCurrency(invoice?.taxAmount)}</span>\n                  </div>\n                )}\n                \n                <hr />\n                <div className=\"flex justify-between text-lg font-bold\">\n                  <span>المجموع الكلي:</span>\n                  <span className=\"text-blue-600\">{formatCurrency(invoice?.totalAmount)}</span>\n                </div>\n                \n                <div className=\"flex justify-between text-green-600\">\n                  <span>المدفوع:</span>\n                  <span className=\"font-medium\">{formatCurrency(invoice?.paidAmount)}</span>\n                </div>\n                \n                {remainingAmount > 0 && (\n                  <div className=\"flex justify-between text-red-600\">\n                    <span>المتبقي:</span>\n                    <span className=\"font-medium\">{formatCurrency(remainingAmount)}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* سجل المدفوعات */}\n            {invoice?.payments && invoice.payments.length > 0 && (\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\">\n                <div className=\"bg-gradient-to-r from-purple-500 to-pink-600 px-6 py-4\">\n                  <h2 className=\"text-lg font-semibold text-white\">سجل المدفوعات</h2>\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    {invoice.payments.map((payment, index) => (\n                      <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                        <div className=\"flex justify-between items-start mb-2\">\n                          <span className=\"font-medium\">{formatCurrency(payment.amount)}</span>\n                          <span className=\"text-sm text-gray-500\">{formatDate(payment.paymentDate)}</span>\n                        </div>\n                        <div className=\"text-sm text-gray-600\">\n                          <div>طريقة الدفع: {payment.method}</div>\n                          {payment.reference && <div>المرجع: {payment.reference}</div>}\n                          {payment.notes && <div>ملاحظات: {payment.notes}</div>}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">إضافة دفعة جديدة</h3>\n                <button\n                  onClick={() => setShowPaymentModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <form onSubmit={handleAddPayment} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">المبلغ *</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    max={remainingAmount}\n                    value={paymentData.amount}\n                    onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder={`الحد الأقصى: ${formatCurrency(remainingAmount)}`}\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">طريقة الدفع *</label>\n                  <select\n                    value={paymentData.method}\n                    onChange={(e) => setPaymentData(prev => ({ ...prev, method: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required\n                  >\n                    <option value=\"cash\">نقداً</option>\n                    <option value=\"card\">بطاقة ائتمان</option>\n                    <option value=\"bank_transfer\">تحويل بنكي</option>\n                    <option value=\"check\">شيك</option>\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">المرجع</label>\n                  <input\n                    type=\"text\"\n                    value={paymentData.reference}\n                    onChange={(e) => setPaymentData(prev => ({ ...prev, reference: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"رقم الشيك أو المرجع...\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">ملاحظات</label>\n                  <textarea\n                    value={paymentData.notes}\n                    onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"\n                    placeholder=\"ملاحظات إضافية...\"\n                  />\n                </div>\n                \n                <div className=\"flex justify-end space-x-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPaymentModal(false)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                  >\n                    إلغاء\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={paymentLoading}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n                  >\n                    {paymentLoading ? 'جاري الحفظ...' : 'إضافة الدفعة'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default withAuth(InvoiceDetailPage);\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;;AAG/C;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,SAAS;;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,OAAO;IACT;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAU;IAEd,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE;YAClC,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,kBAAkB;QAElB,IAAI;YACF,MAAM,CAAA,GAAA,6GAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YAC5B,oBAAoB;YACpB,eAAe;gBAAE,QAAQ;gBAAI,QAAQ;gBAAQ,WAAW;gBAAI,OAAO;YAAG;YACtE,eAAe,uBAAuB;QACxC,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,OAAO;YACP,MAAM;YACN,MAAM;YACN,SAAS;YACT,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,SAAS;YACb,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,kBAAkB,UAAU,WAAW,QAAQ,WAAW,IAAI,WAAW,QAAQ,UAAU,IAAI;IAErG,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;;4CAAmC;4CAAQ,SAAS;;;;;;;;;;;;;0CAEpE,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,kBAAkB,wBAC1B,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIV,6LAAC;wCACC,SAAS,IAAM,OAAO,KAAK;wCAC3B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,SAAQ;wCAAY,MAAK;kDAC7D,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,SAAS,SAAS;8EAC3G,SAAS;;;;;;8EAEZ,6LAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,sBAAsB,SAAS,gBAAgB;8EACzH,SAAS;;;;;;;;;;;;;;;;;;;;;;;0DAMlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA2C;;;;;;8EACzD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAe,SAAS;;;;;;;;;;;;sFAE1C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAe,WAAW,SAAS;;;;;;;;;;;;wEAEpD,SAAS,yBACR,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAe,WAAW,SAAS;;;;;;;;;;;;sFAGvD,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAe,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sEAK9C,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA2C;;;;;;8EACzD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAI,WAAU;8FAAe,SAAS,UAAU;;;;;;;;;;;;sFAEnD,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAI,WAAU;8FAAe,SAAS,UAAU;;;;;;;;;;;;wEAElD,SAAS,UAAU,uBAClB,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAI,WAAU;8FAAe,SAAS,UAAU;;;;;;;;;;;;wEAGpD,SAAS,UAAU,yBAClB,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAI,WAAU;8FAAe,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAM,WAAU;sEACf,cAAA,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAC/F,6LAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAC/F,6LAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAC/F,6LAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAC/F,6LAAC;wEAAG,WAAU;kFAAiF;;;;;;;;;;;;;;;;;sEAGnG,6LAAC;4DAAM,WAAU;sEACd,SAAS,OAAO,IAAI,CAAC,MAAM,sBAC1B,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAG,WAAU;sFACZ,cAAA,6LAAC;gFAAI,WAAU;;oFACZ,QAAQ,UAAU,IAAI,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,mBACzE,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4FACJ,KAAK,CAAC,sBAAsB,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE;4FAC3D,KAAK,KAAK,OAAO,CAAC,IAAI;4FACtB,OAAO;4FACP,QAAQ;4FACR,WAAU;;;;;;;;;;;kGAIhB,6LAAC;;0GACC,6LAAC;gGAAI,WAAU;0GAAqC,KAAK,OAAO,CAAC,IAAI;;;;;;0GACrE,6LAAC;gGAAI,WAAU;0GAAyB,KAAK,OAAO,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;sFAItE,6LAAC;4EAAG,WAAU;sFACX,KAAK,QAAQ;;;;;;sFAEhB,6LAAC;4EAAG,WAAU;sFACX,eAAe,KAAK,SAAS;;;;;;sFAEhC,6LAAC;4EAAG,WAAU;;gFACX,KAAK,QAAQ;gFAAC;;;;;;;sFAEjB,6LAAC;4EAAG,WAAU;sFACX,eAAe,KAAK,KAAK;;;;;;;mEA9BrB;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAwClB,CAAC,SAAS,SAAS,SAAS,KAAK,mBAChC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,SAAS,uBACR,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA2C;;;;;;8EACzD,6LAAC;oEAAE,WAAU;8EAAyB,QAAQ,KAAK;;;;;;;;;;;;wDAGtD,SAAS,uBACR,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA2C;;;;;;8EACzD,6LAAC;oEAAE,WAAU;8EAAyB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUjE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAe,eAAe,SAAS;;;;;;;;;;;;oDAGxD,SAAS,iBAAiB,mBACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAM,SAAS;oEAAa;;;;;;;0EAClC,6LAAC;;oEAAK;oEAAE,eAAe,SAAS;;;;;;;;;;;;;oDAInC,SAAS,YAAY,mBACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAgB;oEAAQ,SAAS;oEAAQ;;;;;;;0EACzD,6LAAC;gEAAK,WAAU;0EAAe,eAAe,SAAS;;;;;;;;;;;;kEAI3D,6LAAC;;;;;kEACD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAiB,eAAe,SAAS;;;;;;;;;;;;kEAG3D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAe,eAAe,SAAS;;;;;;;;;;;;oDAGxD,kBAAkB,mBACjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;oCAOrD,SAAS,YAAY,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAe,eAAe,QAAQ,MAAM;;;;;;sFAC5D,6LAAC;4EAAK,WAAU;sFAAyB,WAAW,QAAQ,WAAW;;;;;;;;;;;;8EAEzE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAI;gFAAc,QAAQ,MAAM;;;;;;;wEAChC,QAAQ,SAAS,kBAAI,6LAAC;;gFAAI;gFAAS,QAAQ,SAAS;;;;;;;wEACpD,QAAQ,KAAK,kBAAI,6LAAC;;gFAAI;gFAAU,QAAQ,KAAK;;;;;;;;;;;;;;2DARxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAqBzB,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAK,UAAU;gCAAkB,WAAU;;kDAC1C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,KAAK;gDACL,OAAO,YAAY,MAAM;gDACzB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC5E,WAAU;gDACV,aAAa,CAAC,aAAa,EAAE,eAAe,kBAAkB;gDAC9D,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,OAAO,YAAY,MAAM;gDACzB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC5E,WAAU;gDACV,QAAQ;;kEAER,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,SAAS;gDAC5B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC/E,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD;GApfS;;QAaQ,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAdjB;6CAsfM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}