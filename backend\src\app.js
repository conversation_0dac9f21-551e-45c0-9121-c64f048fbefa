// backend/src/app.js
require('dotenv').config(); // لتحميل متغيرات البيئة من .env
const express = require('express');
const cors = require('cors'); // للسماح بالاتصال من الواجهة الأمامية
const { PrismaClient } = require('@prisma/client'); // عميل Prisma للتفاعل مع قاعدة البيانات

const app = express();
const prisma = new PrismaClient(); // إنشاء مثيل Prisma
const PORT = process.env.PORT || 5000; // تحديد المنفذ من .env أو 5000 كافتراضي

app.use(cors()); // تفعيل CORS لجميع المسارات
app.use(express.json()); // لتمكين تحليل JSON في جسم الطلبات الواردة (req.body)

// مسار بسيط لاختبار API
app.get('/api', (req, res) => {
  res.send('Invoice App Backend API is running!');
});

// ######### هنا سيتم استيراد مسارات API الخاصة بك لاحقاً #########
// مثال:
// const productRoutes = require('./routes/productRoutes');
// app.use('/api/products', productRoutes);
// ##############################################################

// ربط قاعدة البيانات وتشغيل الخادم
async function main() {
  try {
    await prisma.$connect(); // محاولة الاتصال بقاعدة البيانات
    console.log('✅ Connected to the database');
    app.listen(PORT, () => { // تشغيل الخادم على المنفذ المحدد
      console.log(`🚀 Server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('❌ Database connection failed', error);
    process.exit(1); // إنهاء العملية في حالة فشل الاتصال بقاعدة البيانات
  }
}

main(); // استدعاء الدالة لبدء تشغيل الخادم
// backend/src/app.js
// ... (الكود السابق) ...

// استيراد مسارات المنتجات
const productRoutes = require('./routes/productRoutes');

// استخدام مسارات المنتجات تحت /api/products
app.use('/api/products', productRoutes);

// ... (الكود اللاحق) ...
// backend/src/app.js
// ... (الكود السابق) ...

// استيراد مسارات المصادقة
const authRoutes = require('./routes/authRoutes');
app.use('/api/auth', authRoutes);

// استيراد مسارات العملاء
const customerRoutes = require('./routes/ustomerRoutes.js');

// استيراد مسارات الصور
const imageRoutes = require('./routes/imageRoutes');

// استيراد مسارات الملفات الثابتة
const staticRoutes = require('./routes/staticRoutes');

// استيراد مسارات الفواتير
const invoiceRoutes = require('./routes/invoiceRoutes');

// استخدام مسارات العملاء تحت /api/customers
app.use('/api/customers', customerRoutes);

// استخدام مسارات الصور تحت /api
app.use('/api', imageRoutes);

// استخدام مسارات الفواتير تحت /api/invoices
app.use('/api/invoices', invoiceRoutes);

// استخدام مسارات الملفات الثابتة
app.use('/', staticRoutes);

// ... (الكود اللاحق) ...
