{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/api.js"], "sourcesContent": ["// frontend/lib/api.js\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Cache for API responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nconst getAuthHeader = () => {\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  if (user && user.token) {\n    return { Authorization: `Bearer ${user.token}` };\n  }\n  return {};\n};\n\n// Enhanced fetch with caching and error handling\nconst apiRequest = async (url, options = {}, useCache = false) => {\n  const cacheKey = `${url}-${JSON.stringify(options)}`;\n\n  // Check cache first\n  if (useCache && cache.has(cacheKey)) {\n    const cached = cache.get(cacheKey);\n    if (Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    cache.delete(cacheKey);\n  }\n\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n        ...options.headers,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Cache successful GET requests\n    if (useCache && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {\n      cache.set(cacheKey, {\n        data,\n        timestamp: Date.now()\n      });\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API Request failed:', error);\n    throw error;\n  }\n};\n\n// =================================================================\n// Auth API Functions\n// =================================================================\n\nexport async function login(credentials) {\n  return apiRequest(`${API_BASE_URL}/auth/login`, {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n}\n\nexport async function register(userData) {\n  const response = await fetch(`${API_BASE_URL}/auth/register`, {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(userData),\n  });\n  if (!response.ok) {\n    const errorData = await response.json();\n    throw new Error(errorData.message || 'Failed to register');\n  }\n  return response.json();\n}\n\n\n// =================================================================\n// Product API Functions\n// =================================================================\n\nexport async function getProducts() {\n  return apiRequest(`${API_BASE_URL}/products`, { method: 'GET' }, true);\n}\n\nexport async function createProduct(productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating product:', error);\n    throw error;\n  }\n}\n\nexport async function getProductById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product by ID:', error);\n    throw error;\n  }\n}\n\nexport async function updateProduct(id, productData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(productData),\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || 'Unknown error'}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating product:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProduct(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    // For 204 No Content, there's no JSON to parse\n    if (response.status === 204) {\n      return;\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting product:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Image API Functions\n// =================================================================\n\nexport async function uploadProductImage(productId, imageFile) {\n  try {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      method: 'POST',\n      headers: getAuthHeader(),\n      body: formData,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to upload image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error uploading image:', error);\n    throw error;\n  }\n}\n\nexport async function getProductImages(productId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/products/${productId}/images`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching product images:', error);\n    throw error;\n  }\n}\n\nexport async function setMainImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}/main`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to set main image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error setting main image:', error);\n    throw error;\n  }\n}\n\nexport async function deleteProductImage(imageId) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/${imageId}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete image');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting image:', error);\n    throw error;\n  }\n}\n\nexport async function reorderImages(imageOrders) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/images/reorder`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify({ imageOrders }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to reorder images');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error reordering images:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Invoice API Functions\n// =================================================================\n\nexport async function getInvoices(params = {}) {\n  try {\n    const queryParams = new URLSearchParams();\n\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {\n        queryParams.append(key, params[key]);\n      }\n    });\n\n    const response = await fetch(`${API_BASE_URL}/invoices?${queryParams}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoices:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceById(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice:', error);\n    throw error;\n  }\n}\n\nexport async function createInvoice(invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to create invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function updateInvoice(id, invoiceData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(invoiceData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to update invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error updating invoice:', error);\n    throw error;\n  }\n}\n\nexport async function deleteInvoice(id) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${id}`, {\n      method: 'DELETE',\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to delete invoice');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error deleting invoice:', error);\n    throw error;\n  }\n}\n\nexport async function addPayment(invoiceId, paymentData) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/${invoiceId}/payments`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...getAuthHeader(),\n      },\n      body: JSON.stringify(paymentData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.message || 'Failed to add payment');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error adding payment:', error);\n    throw error;\n  }\n}\n\nexport async function getInvoiceStats() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/invoices/stats`, {\n      headers: getAuthHeader(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching invoice stats:', error);\n    throw error;\n  }\n}\n\n// =================================================================\n// Customer API Functions\n// =================================================================\n\nexport async function getCustomers() {\n  return apiRequest(`${API_BASE_URL}/customers`, { method: 'GET' }, true);\n}\n\nexport async function createCustomer(customerData) {\n  return apiRequest(`${API_BASE_URL}/customers`, {\n    method: 'POST',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function getCustomerById(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, { method: 'GET' }, true);\n}\n\nexport async function updateCustomer(id, customerData) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(customerData),\n  });\n}\n\nexport async function deleteCustomer(id) {\n  return apiRequest(`${API_BASE_URL}/customers/${id}`, {\n    method: 'DELETE',\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,0BAA0B;AAC1B,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,MAAM,gBAAgB;IACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;IACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO;YAAE,eAAe,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;QAAC;IACjD;IACA,OAAO,CAAC;AACV;AAEA,iDAAiD;AACjD,MAAM,aAAa,OAAO,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAEpD,oBAAoB;IACpB,IAAI,YAAY,MAAM,GAAG,CAAC,WAAW;QACnC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;YAClD,OAAO,OAAO,IAAI;QACpB;QACA,MAAM,MAAM,CAAC;IACf;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;gBAClB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,gCAAgC;QAChC,IAAI,YAAY,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;YACpG,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAMO,eAAe,MAAM,WAAW;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,CAAC,EAAE;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,SAAS,QAAQ;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;QAC5D,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IACA,OAAO,SAAS,IAAI;AACtB;AAOO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,SAAS,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACnE;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;QAC5G;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,+CAA+C;QAC/C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B;QACF;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,eAAe,mBAAmB,SAAS,EAAE,SAAS;IAC3D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC3E,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,eAAe,mBAAmB,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;YAChE,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAMO,eAAe,YAAY,SAAS,CAAC,CAAC;IAC3C,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI;gBAC3E,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,aAAa,EAAE;YACtE,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,WAAW;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE,EAAE,WAAW;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAAE;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;YAC7D,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,WAAW,SAAS,EAAE,WAAW;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,eAAe;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;QACvC;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAMO,eAAe;IACpB,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAAE,QAAQ;IAAM,GAAG;AACpE;AAEO,eAAe,eAAe,YAAY;IAC/C,OAAO,WAAW,GAAG,aAAa,UAAU,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,gBAAgB,EAAE;IACtC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QAAE,QAAQ;IAAM,GAAG;AAC1E;AAEO,eAAe,eAAe,EAAE,EAAE,YAAY;IACnD,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAE;IACrC,OAAO,WAAW,GAAG,aAAa,WAAW,EAAE,IAAI,EAAE;QACnD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/withAuth.js"], "sourcesContent": ["// frontend/components/withAuth.js\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst withAuth = (WrappedComponent) => {\n  const Wrapper = (props) => {\n    const router = useRouter();\n\n    useEffect(() => {\n      const user = localStorage.getItem('user');\n      if (!user) {\n        router.replace('/login');\n      }\n    }, [router]);\n\n    return <WrappedComponent {...props} />;\n  };\n\n  return Wrapper;\n};\n\nexport default withAuth;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAGlC;AACA;AAHA;;;;AAKA,MAAM,WAAW,CAAC;;IAChB,MAAM,UAAU,CAAC;;QACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;gBACR,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB;YACF;yCAAG;YAAC;SAAO;QAEX,qBAAO,6LAAC;YAAkB,GAAG,KAAK;;;;;;IACpC;OAXM;;YACW,qIAAA,CAAA,YAAS;;;IAY1B,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/components/DashboardLayout.js"], "sourcesContent": ["// frontend/components/DashboardLayout.js\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\n\nexport default function DashboardLayout({ children }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    localStorage.removeItem('user');\n    router.push('/');\n  };\n\n  const navigation = [\n    {\n      name: 'لوحة التحكم',\n      href: '/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'المنتجات',\n      href: '/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'العملاء',\n      href: '/dashboard/customers',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الفواتير',\n      href: '/dashboard/invoices',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'قوالب الفواتير',\n      href: '/dashboard/invoice-templates',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'التقارير',\n      href: '/dashboard/reports',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'الإعدادات',\n      href: '/dashboard/settings',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar for desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col\">\n        <div className=\"flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n          </div>\n          <div className=\"mt-5 flex-grow flex flex-col\">\n            <nav className=\"flex-1 px-2 pb-4 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <span className={`mr-3 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* User menu */}\n            <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile sidebar */}\n      <div className={`md:hidden fixed inset-0 flex z-40 ${sidebarOpen ? '' : 'pointer-events-none'}`}>\n        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />\n\n        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition-transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام الفواتير</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href ||\n                  (item.href !== '/dashboard' && pathname.startsWith(item.href));\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-indigo-100 text-indigo-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <span className={`mr-4 ${isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}`}>\n                      {item.icon}\n                    </span>\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">المستخدم</p>\n                <button\n                  onClick={handleLogout}\n                  className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"md:hidden relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <span className=\"block w-full pl-12 pr-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none text-sm\">\n                    نظام إدارة الفواتير\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAMe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;sCAElD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;wCAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,kCACA,sDACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;8DAChG,KAAK,IAAI;;;;;;gDAEX,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,uBAAuB;;kCAC7F,6LAAC;wBAAI,WAAW,CAAC,2DAA2D,EAAE,cAAc,gBAAgB,aAAa;wBAAE,SAAS,IAAM,eAAe;;;;;;kCAEzJ,6LAAC;wBAAI,WAAW,CAAC,sFAAsF,EAAE,cAAc,kBAAkB,qBAAqB;;0CAC5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC;4CACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,gBAAgB,SAAS,UAAU,CAAC,KAAK,IAAI;4CAE9D,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,qFAAqF,EAC/F,WACI,kCACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,2CAA2C;kEAChG,KAAK,IAAI;;;;;;oDAEX,KAAK,IAAI;;+CAZL,KAAK,IAAI;;;;;wCAepB;;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAK,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvH,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA7PwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/app/dashboard/customers/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState, useMemo } from 'react';\nimport Link from 'next/link';\nimport { getCustomers, deleteCustomer } from '@/lib/api';\nimport withAuth from '@/components/withAuth';\nimport DashboardLayout from '@/components/DashboardLayout';\n\nfunction CustomersPage() {\n  const [customers, setCustomers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState('name');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [viewMode, setViewMode] = useState('grid');\n\n  const loadCustomers = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await getCustomers();\n      setCustomers(data);\n    } catch (err) {\n      setError('فشل في تحميل العملاء. يرجى المحاولة مرة أخرى.');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadCustomers();\n  }, []);\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {\n      try {\n        await deleteCustomer(id);\n        setCustomers(customers.filter(customer => customer.id !== id));\n      } catch (err) {\n        setError('فشل في حذف العميل. يرجى المحاولة مرة أخرى.');\n        console.error(err);\n      }\n    }\n  };\n\n  // Filter and sort customers\n  const filteredAndSortedCustomers = useMemo(() => {\n    let filtered = customers.filter(customer => {\n      const searchLower = searchTerm.toLowerCase();\n      return (\n        customer.name?.toLowerCase().includes(searchLower) ||\n        customer.email?.toLowerCase().includes(searchLower) ||\n        customer.phone?.includes(searchTerm) ||\n        customer.company?.toLowerCase().includes(searchLower)\n      );\n    });\n\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy] || '';\n      let bValue = b[sortBy] || '';\n      \n      if (sortBy === 'totalSpent') {\n        aValue = parseFloat(aValue) || 0;\n        bValue = parseFloat(bValue) || 0;\n      } else {\n        aValue = String(aValue).toLowerCase();\n        bValue = String(bValue).toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    return filtered;\n  }, [customers, searchTerm, sortBy, sortOrder]);\n\n  const getCustomerInitials = (name) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const getCustomerStatus = (customer) => {\n    const lastOrderDate = customer.lastOrderDate ? new Date(customer.lastOrderDate) : null;\n    const daysSinceLastOrder = lastOrderDate ? Math.floor((new Date() - lastOrderDate) / (1000 * 60 * 60 * 24)) : null;\n    \n    if (!lastOrderDate) {\n      return { status: 'new', color: 'text-blue-600 bg-blue-100', text: 'عميل جديد' };\n    } else if (daysSinceLastOrder <= 30) {\n      return { status: 'active', color: 'text-green-600 bg-green-100', text: 'نشط' };\n    } else if (daysSinceLastOrder <= 90) {\n      return { status: 'inactive', color: 'text-yellow-600 bg-yellow-100', text: 'غير نشط' };\n    } else {\n      return { status: 'dormant', color: 'text-gray-600 bg-gray-100', text: 'خامل' };\n    }\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout>\n        <div className=\"min-h-screen bg-gray-50 p-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-300 rounded w-1/4 mb-6\"></div>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n                {[...Array(8)].map((_, i) => (\n                  <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                    <div className=\"flex items-center space-x-4 mb-4\">\n                      <div className=\"w-12 h-12 bg-gray-300 rounded-full\"></div>\n                      <div className=\"flex-1\">\n                        <div className=\"h-4 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                        <div className=\"h-3 bg-gray-300 rounded w-1/2\"></div>\n                      </div>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"h-3 bg-gray-300 rounded\"></div>\n                      <div className=\"h-3 bg-gray-300 rounded w-2/3\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">إدارة العملاء</h1>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  إدارة وتنظيم عملائك وبياناتهم\n                </p>\n              </div>\n              <div className=\"mt-4 sm:mt-0\">\n                <Link\n                  href=\"/dashboard/customers/add\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  إضافة عميل جديد\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          {error && (\n            <div className=\"mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm text-red-700 font-medium\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Filters and Search */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Search */}\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"البحث في العملاء...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                />\n              </div>\n\n              {/* Sort */}\n              <select\n                value={`${sortBy}-${sortOrder}`}\n                onChange={(e) => {\n                  const [field, order] = e.target.value.split('-');\n                  setSortBy(field);\n                  setSortOrder(order);\n                }}\n                className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n              >\n                <option value=\"name-asc\">الاسم (أ-ي)</option>\n                <option value=\"name-desc\">الاسم (ي-أ)</option>\n                <option value=\"email-asc\">البريد الإلكتروني (أ-ي)</option>\n                <option value=\"email-desc\">البريد الإلكتروني (ي-أ)</option>\n                <option value=\"totalSpent-desc\">إجمالي المشتريات (عالي-منخفض)</option>\n                <option value=\"totalSpent-asc\">إجمالي المشتريات (منخفض-عالي)</option>\n              </select>\n\n              {/* View Mode */}\n              <div className=\"flex rounded-lg border border-gray-300 overflow-hidden\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`flex-1 px-3 py-2 text-sm font-medium ${\n                    viewMode === 'grid'\n                      ? 'bg-indigo-600 text-white'\n                      : 'bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <svg className=\"w-4 h-4 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n                  </svg>\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`flex-1 px-3 py-2 text-sm font-medium ${\n                    viewMode === 'list'\n                      ? 'bg-indigo-600 text-white'\n                      : 'bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <svg className=\"w-4 h-4 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Customers Count */}\n          <div className=\"mb-4\">\n            <p className=\"text-sm text-gray-600\">\n              عرض {filteredAndSortedCustomers.length} من {customers.length} عميل\n            </p>\n          </div>\n\n          {/* Customers Grid/List */}\n          {filteredAndSortedCustomers.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">لا يوجد عملاء</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">ابدأ بإضافة عميل جديد</p>\n              <div className=\"mt-6\">\n                <Link\n                  href=\"/dashboard/customers/add\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  إضافة عميل جديد\n                </Link>\n              </div>\n            </div>\n          ) : viewMode === 'grid' ? (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {filteredAndSortedCustomers.map((customer) => {\n                const customerStatus = getCustomerStatus(customer);\n                return (\n                  <div key={customer.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200\">\n                    <div className=\"p-6\">\n                      {/* Customer Header */}\n                      <div className=\"flex items-center space-x-4 mb-4\">\n                        <div className=\"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold\">\n                          {getCustomerInitials(customer.name)}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n                            {customer.name}\n                          </h3>\n                          <p className=\"text-xs text-gray-500 truncate\">\n                            {customer.email}\n                          </p>\n                        </div>\n                        <div>\n                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${customerStatus.color}`}>\n                            {customerStatus.text}\n                          </span>\n                        </div>\n                      </div>\n\n                      {/* Customer Info */}\n                      <div className=\"space-y-2 mb-4\">\n                        {customer.phone && (\n                          <div className=\"flex items-center text-sm text-gray-600\">\n                            <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                            </svg>\n                            {customer.phone}\n                          </div>\n                        )}\n                        {customer.company && (\n                          <div className=\"flex items-center text-sm text-gray-600\">\n                            <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m4 0V9a1 1 0 011-1h4a1 1 0 011 1v12m-6 0h6\" />\n                            </svg>\n                            {customer.company}\n                          </div>\n                        )}\n                        {customer.totalSpent && (\n                          <div className=\"flex items-center text-sm text-gray-600\">\n                            <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                            </svg>\n                            إجمالي المشتريات: ${parseFloat(customer.totalSpent).toFixed(2)}\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Actions */}\n                      <div className=\"flex space-x-2\">\n                        <Link\n                          href={`/dashboard/customers/edit/${customer.id}`}\n                          className=\"flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs font-medium py-2 px-3 rounded-lg text-center transition-colors\"\n                        >\n                          تعديل\n                        </Link>\n                        <button\n                          onClick={() => handleDelete(customer.id)}\n                          className=\"flex-1 bg-red-100 hover:bg-red-200 text-red-700 text-xs font-medium py-2 px-3 rounded-lg transition-colors\"\n                        >\n                          حذف\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          ) : (\n            /* List View */\n            <div className=\"bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden\">\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        العميل\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الشركة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الهاتف\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        إجمالي المشتريات\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredAndSortedCustomers.map((customer) => {\n                      const customerStatus = getCustomerStatus(customer);\n                      return (\n                        <tr key={customer.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <div className=\"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm\">\n                                {getCustomerInitials(customer.name)}\n                              </div>\n                              <div className=\"ml-4\">\n                                <div className=\"text-sm font-medium text-gray-900\">{customer.name}</div>\n                                <div className=\"text-sm text-gray-500\">{customer.email}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {customer.company || '-'}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {customer.phone || '-'}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            ${parseFloat(customer.totalSpent || 0).toFixed(2)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${customerStatus.color}`}>\n                              {customerStatus.text}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <div className=\"flex space-x-2\">\n                              <Link\n                                href={`/dashboard/customers/edit/${customer.id}`}\n                                className=\"text-indigo-600 hover:text-indigo-900\"\n                              >\n                                تعديل\n                              </Link>\n                              <button\n                                onClick={() => handleDelete(customer.id)}\n                                className=\"text-red-600 hover:text-red-900\"\n                              >\n                                حذف\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      );\n                    })}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n\nexport default withAuth(CustomersPage);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,gBAAgB;QACpB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,6GAAA,CAAA,eAAY,AAAD;YAC9B,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,oCAAoC;YACrD,IAAI;gBACF,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE;gBACrB,aAAa,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;YAC5D,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6DAAE;YACzC,IAAI,WAAW,UAAU,MAAM;8EAAC,CAAA;oBAC9B,MAAM,cAAc,WAAW,WAAW;oBAC1C,OACE,SAAS,IAAI,EAAE,cAAc,SAAS,gBACtC,SAAS,KAAK,EAAE,cAAc,SAAS,gBACvC,SAAS,KAAK,EAAE,SAAS,eACzB,SAAS,OAAO,EAAE,cAAc,SAAS;gBAE7C;;YAEA,SAAS,IAAI;qEAAC,CAAC,GAAG;oBAChB,IAAI,SAAS,CAAC,CAAC,OAAO,IAAI;oBAC1B,IAAI,SAAS,CAAC,CAAC,OAAO,IAAI;oBAE1B,IAAI,WAAW,cAAc;wBAC3B,SAAS,WAAW,WAAW;wBAC/B,SAAS,WAAW,WAAW;oBACjC,OAAO;wBACL,SAAS,OAAO,QAAQ,WAAW;wBACnC,SAAS,OAAO,QAAQ,WAAW;oBACrC;oBAEA,IAAI,cAAc,OAAO;wBACvB,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC,OAAO;wBACL,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC;gBACF;;YAEA,OAAO;QACT;4DAAG;QAAC;QAAW;QAAY;QAAQ;KAAU;IAE7C,MAAM,sBAAsB,CAAC;QAC3B,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,SAAS,aAAa,GAAG,IAAI,KAAK,SAAS,aAAa,IAAI;QAClF,MAAM,qBAAqB,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,aAAa,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK;QAE9G,IAAI,CAAC,eAAe;YAClB,OAAO;gBAAE,QAAQ;gBAAO,OAAO;gBAA6B,MAAM;YAAY;QAChF,OAAO,IAAI,sBAAsB,IAAI;YACnC,OAAO;gBAAE,QAAQ;gBAAU,OAAO;gBAA+B,MAAM;YAAM;QAC/E,OAAO,IAAI,sBAAsB,IAAI;YACnC,OAAO;gBAAE,QAAQ;gBAAY,OAAO;gBAAiC,MAAM;YAAU;QACvF,OAAO;YACL,OAAO;gBAAE,QAAQ;gBAAW,OAAO;gBAA6B,MAAM;YAAO;QAC/E;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,gIAAA,CAAA,UAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAVT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoB1B;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhB,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;sCAOzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCACC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;wCAC/B,UAAU,CAAC;4CACT,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;4CAC5C,UAAU;4CACV,aAAa;wCACf;wCACA,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAkB;;;;;;0DAChC,6LAAC;gDAAO,OAAM;0DAAiB;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,qCAAqC,EAC/C,aAAa,SACT,6BACA,2CACJ;0DAEF,cAAA,6LAAC;oDAAI,WAAU;oDAAkB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACzE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,qCAAqC,EAC/C,aAAa,SACT,6BACA,2CACJ;0DAEF,cAAA,6LAAC;oDAAI,WAAU;oDAAkB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACzE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC9B,2BAA2B,MAAM;oCAAC;oCAAK,UAAU,MAAM;oCAAC;;;;;;;;;;;;wBAKhE,2BAA2B,MAAM,KAAK,kBACrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAkC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACzF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;mCAKV,aAAa,uBACf,6LAAC;4BAAI,WAAU;sCACZ,2BAA2B,GAAG,CAAC,CAAC;gCAC/B,MAAM,iBAAiB,kBAAkB;gCACzC,qBACE,6LAAC;oCAAsB,WAAU;8CAC/B,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,oBAAoB,SAAS,IAAI;;;;;;kEAEpC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,SAAS,IAAI;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,SAAS,KAAK;;;;;;;;;;;;kEAGnB,6LAAC;kEACC,cAAA,6LAAC;4DAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,KAAK,EAAE;sEAC3G,eAAe,IAAI;;;;;;;;;;;;;;;;;0DAM1B,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACtE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DAEtE,SAAS,KAAK;;;;;;;oDAGlB,SAAS,OAAO,kBACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACtE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DAEtE,SAAS,OAAO;;;;;;;oDAGpB,SAAS,UAAU,kBAClB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACtE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DACjE;4DACc,WAAW,SAAS,UAAU,EAAE,OAAO,CAAC;;;;;;;;;;;;;0DAMlE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,0BAA0B,EAAE,SAAS,EAAE,EAAE;wDAChD,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,aAAa,SAAS,EAAE;wDACvC,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCA7DG,SAAS,EAAE;;;;;4BAoEzB;;;;;mCAGF,aAAa,iBACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,2BAA2B,GAAG,CAAC,CAAC;gDAC/B,MAAM,iBAAiB,kBAAkB;gDACzC,qBACE,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,oBAAoB,SAAS,IAAI;;;;;;kFAEpC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAqC,SAAS,IAAI;;;;;;0FACjE,6LAAC;gFAAI,WAAU;0FAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAI5D,6LAAC;4DAAG,WAAU;sEACX,SAAS,OAAO,IAAI;;;;;;sEAEvB,6LAAC;4DAAG,WAAU;sEACX,SAAS,KAAK,IAAI;;;;;;sEAErB,6LAAC;4DAAG,WAAU;;gEAAgE;gEAC1E,WAAW,SAAS,UAAU,IAAI,GAAG,OAAO,CAAC;;;;;;;sEAEjD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,KAAK,EAAE;0EAC/G,eAAe,IAAI;;;;;;;;;;;sEAGxB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,0BAA0B,EAAE,SAAS,EAAE,EAAE;wEAChD,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,aAAa,SAAS,EAAE;wEACvC,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDArCE,SAAS,EAAE;;;;;4CA4CxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;GA1aS;KAAA;6CA4aM,CAAA,GAAA,yHAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}