// frontend/components/invoice-templates/ModernTemplate.js
'use client';

import React from 'react';

export default function ModernTemplate({ invoice, preview = false }) {
  const sampleData = {
    invoiceNumber: 'INV-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customerName: 'شركة التقنية المتطورة',
    customerDetails: 'الحلول التقنية المتكاملة',
    items: [
      { id: 1, description: 'خدمات تطوير البرمجيات', quantity: 1, price: 5000.00, total: 5000.00 },
      { id: 2, description: 'استشارات تقنية', quantity: 10, price: 200.00, total: 2000.00 },
      { id: 3, description: 'صيانة وتطوير', quantity: 1, price: 1500.00, total: 1500.00 }
    ],
    subtotal: 8500.00,
    tax: 1275.00,
    total: 9775.00,
    notes: 'شكراً لثقتكم بنا',
    companyName: 'شركة التقنية المتطورة',
    companyDetails: 'الحلول التقنية المتكاملة'
  };

  const data = preview ? sampleData : invoice;

  return (
    <div className="bg-white" style={{ minHeight: '297mm', width: '210mm' }}>
      {/* Modern Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 text-white">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold mb-2">{data.companyName}</h1>
            <p className="text-blue-100 text-lg">{data.companyDetails}</p>
          </div>

          <div className="text-right">
            <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-4">
              <h2 className="text-2xl font-bold mb-2">فاتورة</h2>
              <p className="text-blue-100">#{data.invoiceNumber}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-8">
        {/* Customer Info Card */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-8">
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات العميل</h3>
              <div className="space-y-2">
                <p className="text-gray-700 font-medium">{data.customerName}</p>
                <p className="text-gray-600">{data.customerDetails}</p>
              </div>
            </div>

            <div className="text-right">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">تفاصيل الفاتورة</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">التاريخ:</span>
                  <span className="font-medium">{data.date}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">رقم الفاتورة:</span>
                  <span className="font-medium">{data.invoiceNumber}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Items Table */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">تفاصيل الخدمات</h3>

          <div className="overflow-hidden rounded-xl border border-gray-200">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                <tr>
                  <th className="p-4 text-right font-semibold">الإجمالي</th>
                  <th className="p-4 text-right font-semibold">السعر</th>
                  <th className="p-4 text-right font-semibold">الكمية</th>
                  <th className="p-4 text-right font-semibold">الوصف</th>
                  <th className="p-4 text-right font-semibold">#</th>
                </tr>
              </thead>
              <tbody>
                {data.items.map((item, index) => (
                  <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="p-4 text-right font-semibold text-blue-600">
                      {(Number(item.total) || 0).toFixed(2)} ر.س
                    </td>
                    <td className="p-4 text-right">{(Number(item.price) || 0).toFixed(2)} ر.س</td>
                    <td className="p-4 text-right">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                        {item.quantity || 0}
                      </span>
                    </td>
                    <td className="p-4 text-right font-medium">{item.description || ''}</td>
                    <td className="p-4 text-right">
                      <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm font-medium">
                        {index + 1}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modern Totals */}
        <div className="flex justify-between items-start mb-8">
          <div className="w-1/2">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-3">ملاحظات</h4>
              <p className="text-gray-700">{data.notes}</p>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <h5 className="font-semibold text-gray-800 mb-2">شروط الدفع:</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• الدفع خلال 30 يوم من تاريخ الفاتورة</li>
                  <li>• جميع الأسعار شاملة ضريبة القيمة المضافة</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="w-80">
            <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white">
              <h4 className="text-lg font-semibold mb-4">ملخص الفاتورة</h4>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>{(Number(data.subtotal) || 0).toFixed(2)} ر.س</span>
                </div>
                <div className="flex justify-between">
                  <span>ضريبة القيمة المضافة (15%):</span>
                  <span>{(Number(data.tax) || 0).toFixed(2)} ر.س</span>
                </div>
                <div className="border-t border-white border-opacity-30 pt-3">
                  <div className="flex justify-between text-xl font-bold">
                    <span>الإجمالي النهائي:</span>
                    <span>{(Number(data.total) || 0).toFixed(2)} ر.س</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Footer */}
        <div className="bg-gray-50 rounded-xl p-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">معلومات التواصل</h4>
              <div className="flex space-x-6 text-sm text-gray-600">
                <span>📞 +966 50 123 4567</span>
                <span>📧 <EMAIL></span>
                <span>🌐 www.company.com</span>
              </div>
            </div>

            <div className="text-center">
              <div className="w-32 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold">
                شعار الشركة
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
