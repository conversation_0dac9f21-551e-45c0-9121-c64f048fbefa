// frontend/components/auth/PasswordStrength.js
'use client';

export default function PasswordStrength({ password }) {
  const getPasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
  };

  const getStrengthColor = (strength) => {
    if (strength <= 2) return 'bg-red-500';
    if (strength <= 3) return 'bg-yellow-500';
    if (strength <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStrengthText = (strength) => {
    if (strength <= 2) return 'ضعيفة';
    if (strength <= 3) return 'متوسطة';
    if (strength <= 4) return 'قوية';
    return 'قوية جداً';
  };

  const getRequirements = (password) => {
    return [
      { text: 'على الأقل 8 أحرف', met: password.length >= 8 },
      { text: 'حرف كبير واحد', met: /[A-Z]/.test(password) },
      { text: 'حرف صغير واحد', met: /[a-z]/.test(password) },
      { text: 'رقم واحد', met: /[0-9]/.test(password) },
      { text: 'رمز خاص واحد', met: /[^A-Za-z0-9]/.test(password) }
    ];
  };

  if (!password) return null;

  const strength = getPasswordStrength(password);
  const requirements = getRequirements(password);

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between text-sm mb-2">
        <span className="text-gray-600">قوة كلمة المرور:</span>
        <span className={`font-medium ${
          strength <= 2 ? 'text-red-600' :
          strength <= 3 ? 'text-yellow-600' :
          strength <= 4 ? 'text-blue-600' : 'text-green-600'
        }`}>
          {getStrengthText(strength)}
        </span>
      </div>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(strength)}`}
          style={{ width: `${(strength / 5) * 100}%` }}
        ></div>
      </div>

      {/* Requirements */}
      <div className="space-y-1">
        {requirements.map((req, index) => (
          <div key={index} className="flex items-center text-xs">
            <div className={`w-3 h-3 rounded-full mr-2 flex items-center justify-center ${
              req.met ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {req.met && (
                <svg className="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
            <span className={req.met ? 'text-green-600' : 'text-gray-500'}>
              {req.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
