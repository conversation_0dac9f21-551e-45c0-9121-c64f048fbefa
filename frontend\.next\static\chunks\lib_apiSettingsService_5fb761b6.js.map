{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8/invoice-app/frontend/lib/apiSettingsService.js"], "sourcesContent": ["// frontend/lib/apiSettingsService.js\n\n// خدمة إدارة الإعدادات عبر API\nclass ApiSettingsService {\n  constructor() {\n    this.apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n    this.fallbackService = null; // للعودة إلى localStorage في حالة فشل API\n  }\n\n  // تحميل الإعدادات من API\n  async loadSettings() {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const settings = await response.json();\n      \n      // حفظ نسخة احتياطية في localStorage\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(settings));\n      \n      return settings;\n    } catch (error) {\n      console.error('Error loading settings from API:', error);\n      \n      // محاولة تحميل من localStorage كبديل\n      try {\n        const backup = localStorage.getItem('invoiceAppSettings_backup');\n        if (backup) {\n          console.log('Using backup settings from localStorage');\n          return JSON.parse(backup);\n        }\n      } catch (localError) {\n        console.error('Error loading backup settings:', localError);\n      }\n      \n      // إرجاع إعدادات افتراضية\n      return this.getDefaultSettings();\n    }\n  }\n\n  // حفظ إعدادات الشركة\n  async saveCompanySettings(companySettings) {\n    try {\n      // معالجة رفع الصورة إذا كانت موجودة\n      if (companySettings.logoFile) {\n        const logoUrl = await this.uploadCompanyLogo(companySettings.logoFile);\n        companySettings.logoUrl = logoUrl;\n        delete companySettings.logoFile;\n      }\n\n      const response = await fetch(`${this.apiUrl}/settings/company`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(companySettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      currentSettings.company = { ...currentSettings.company, ...companySettings };\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n      \n      return result;\n    } catch (error) {\n      console.error('Error saving company settings:', error);\n      throw error;\n    }\n  }\n\n  // حفظ إعدادات الفواتير العامة\n  async saveInvoiceSettings(invoiceSettings) {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings/invoice`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(invoiceSettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      currentSettings.invoice = { ...currentSettings.invoice, ...invoiceSettings };\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n      \n      return result;\n    } catch (error) {\n      console.error('Error saving invoice settings:', error);\n      throw error;\n    }\n  }\n\n  // حفظ إعدادات عرض الفاتورة\n  async saveInvoiceDisplaySettings(displaySettings) {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings/invoice/display`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(displaySettings)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      \n      // تحديث النسخة الاحتياطية\n      const currentSettings = await this.loadSettings();\n      if (!currentSettings.invoice) currentSettings.invoice = {};\n      currentSettings.invoice.display = displaySettings;\n      localStorage.setItem('invoiceAppSettings_backup', JSON.stringify(currentSettings));\n      \n      return result;\n    } catch (error) {\n      console.error('Error saving display settings:', error);\n      throw error;\n    }\n  }\n\n  // رفع شعار الشركة\n  async uploadCompanyLogo(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const logoUrl = e.target.result;\n        // حفظ في localStorage مؤقتاً\n        localStorage.setItem('companyLogo', logoUrl);\n        resolve(logoUrl);\n      };\n      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));\n      reader.readAsDataURL(file);\n    });\n  }\n\n  // الحصول على شعار الشركة\n  getCompanyLogo() {\n    return localStorage.getItem('companyLogo');\n  }\n\n  // الحصول على إعدادات الشركة\n  async getCompanySettings() {\n    const settings = await this.loadSettings();\n    return settings.company || {};\n  }\n\n  // الحصول على إعدادات الفواتير\n  async getInvoiceSettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice || {};\n  }\n\n  // الحصول على إعدادات عرض الفاتورة\n  async getInvoiceDisplaySettings() {\n    const settings = await this.loadSettings();\n    return settings.invoice?.display || {};\n  }\n\n  // الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      company: {\n        companyName: '',\n        companyAddress: '',\n        companyPhone: '',\n        companyEmail: '',\n        companyWebsite: '', // اختياري\n        taxId: '',\n        currency: 'SAR',\n        logoUrl: null\n      },\n      invoice: {\n        invoicePrefix: 'INV',\n        invoiceNumberLength: 6,\n        defaultTaxRate: 15,\n        defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n        autoGenerateInvoiceNumber: true,\n        showImagesInInvoice: true,\n        allowPartialPayments: true,\n        requireCustomerInfo: true,\n        defaultDueDays: 30,\n        invoiceFooter: '',\n        invoiceNotes: '',\n        display: {\n          // رأس الفاتورة\n          showCompanyLogo: true,\n          showCompanyName: true,\n          showCompanyAddress: true,\n          showCompanyPhone: true,\n          showCompanyEmail: true,\n          showCompanyWebsite: true,\n          showTaxId: true,\n          \n          // معلومات الفاتورة\n          showInvoiceNumber: true,\n          showInvoiceDate: true,\n          showDueDate: true,\n          showPaymentTerms: true,\n          \n          // معلومات العميل\n          showCustomerName: true,\n          showCustomerAddress: true,\n          showCustomerPhone: true,\n          showCustomerEmail: true,\n          \n          // عناصر الجدول\n          showProductImages: true,\n          showProductCode: true,\n          showProductDescription: true,\n          showQuantity: true,\n          showUnitPrice: true,\n          showDiscount: false,\n          showTotalPrice: true,\n          showItemNumbers: true,\n          \n          // المجاميع\n          showSubtotal: true,\n          showTaxAmount: true,\n          showDiscountAmount: false,\n          showTotalAmount: true,\n          \n          // التذييل\n          showNotes: true,\n          showFooter: true,\n          showSignature: false,\n          showQRCode: false,\n          showBankDetails: false,\n          showPaymentInstructions: true\n        }\n      }\n    };\n  }\n\n  // فحص حالة الاتصال بـ API\n  async checkApiConnection() {\n    try {\n      const response = await fetch(`${this.apiUrl}/settings`);\n      return response.ok;\n    } catch (error) {\n      return false;\n    }\n  }\n}\n\n// إنشاء مثيل واحد للخدمة\nconst apiSettingsService = new ApiSettingsService();\n\nexport default apiSettingsService;\n"], "names": [], "mappings": "AAAA,qCAAqC;AAErC,+BAA+B;;;;AAGb;AAFlB,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;QACjD,IAAI,CAAC,eAAe,GAAG,MAAM,0CAA0C;IACzE;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,WAAW,MAAM,SAAS,IAAI;YAEpC,oCAAoC;YACpC,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,qCAAqC;YACrC,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC;oBACZ,OAAO,KAAK,KAAK,CAAC;gBACpB;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;YAClD;YAEA,yBAAyB;YACzB,OAAO,IAAI,CAAC,kBAAkB;QAChC;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,eAAe,EAAE;QACzC,IAAI;YACF,oCAAoC;YACpC,IAAI,gBAAgB,QAAQ,EAAE;gBAC5B,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,QAAQ;gBACrE,gBAAgB,OAAO,GAAG;gBAC1B,OAAO,gBAAgB,QAAQ;YACjC;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,gBAAgB,OAAO,GAAG;gBAAE,GAAG,gBAAgB,OAAO;gBAAE,GAAG,eAAe;YAAC;YAC3E,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,oBAAoB,eAAe,EAAE;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,gBAAgB,OAAO,GAAG;gBAAE,GAAG,gBAAgB,OAAO;gBAAE,GAAG,eAAe;YAAC;YAC3E,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,2BAA2B,eAAe,EAAE;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY;YAC/C,IAAI,CAAC,gBAAgB,OAAO,EAAE,gBAAgB,OAAO,GAAG,CAAC;YACzD,gBAAgB,OAAO,CAAC,OAAO,GAAG;YAClC,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;YAEjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,CAAC,MAAM;gBAC/B,6BAA6B;gBAC7B,aAAa,OAAO,CAAC,eAAe;gBACpC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,yBAAyB;IACzB,iBAAiB;QACf,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,IAAI,CAAC;IAC9B;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB;QACzB,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,IAAI,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,4BAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,EAAE,WAAW,CAAC;IACvC;IAEA,uBAAuB;IACvB,qBAAqB;QACnB,OAAO;YACL,SAAS;gBACP,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;gBACV,SAAS;YACX;YACA,SAAS;gBACP,eAAe;gBACf,qBAAqB;gBACrB,gBAAgB;gBAChB,qBAAqB;gBACrB,2BAA2B;gBAC3B,qBAAqB;gBACrB,sBAAsB;gBACtB,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe;gBACf,cAAc;gBACd,SAAS;oBACP,eAAe;oBACf,iBAAiB;oBACjB,iBAAiB;oBACjB,oBAAoB;oBACpB,kBAAkB;oBAClB,kBAAkB;oBAClB,oBAAoB;oBACpB,WAAW;oBAEX,mBAAmB;oBACnB,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;oBACb,kBAAkB;oBAElB,iBAAiB;oBACjB,kBAAkB;oBAClB,qBAAqB;oBACrB,mBAAmB;oBACnB,mBAAmB;oBAEnB,eAAe;oBACf,mBAAmB;oBACnB,iBAAiB;oBACjB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,iBAAiB;oBAEjB,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,oBAAoB;oBACpB,iBAAiB;oBAEjB,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,YAAY;oBACZ,iBAAiB;oBACjB,yBAAyB;gBAC3B;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAEA,yBAAyB;AACzB,MAAM,qBAAqB,IAAI;uCAEhB", "debugId": null}}]}