// frontend/app/dashboard/page.js
'use client';

import { useState, useEffect } from 'react';
import { getInvoiceStats, getProducts, getCustomers } from '@/lib/api';
import withAuth from '@/components/withAuth';
import DashboardLayout from '@/components/DashboardLayout';
import StatsCard from '@/components/dashboard/StatsCard';
import ChartCard from '@/components/dashboard/ChartCard';
import RecentActivity from '@/components/dashboard/RecentActivity';
import QuickActions from '@/components/dashboard/QuickActions';
import LowStockAlert from '@/components/dashboard/LowStockAlert';

function DashboardPage() {
  const [stats, setStats] = useState({});
  const [chartData, setChartData] = useState([]);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // تحميل الإحصائيات
      const [invoiceStats, products, customers] = await Promise.all([
        getInvoiceStats(),
        getProducts(),
        getCustomers()
      ]);

      // حساب إحصائيات إضافية - التأكد من أن البيانات arrays
      const productsArray = Array.isArray(products) ? products : products?.products || [];
      const customersArray = Array.isArray(customers) ? customers : customers?.customers || [];

      const totalProducts = productsArray.length;
      const lowStockProducts = productsArray.filter(p => p.stock <= (p.minStock || 5)).length;
      const totalCustomers = customersArray.length;

      setStats({
        ...invoiceStats,
        totalProducts,
        lowStockProducts,
        totalCustomers
      });

      // بيانات الرسم البياني (مبيعات آخر 7 أيام)
      const salesData = [
        { label: 'الأحد', value: 1200 },
        { label: 'الاثنين', value: 1900 },
        { label: 'الثلاثاء', value: 800 },
        { label: 'الأربعاء', value: 1500 },
        { label: 'الخميس', value: 2100 },
        { label: 'الجمعة', value: 1800 },
        { label: 'السبت', value: 2400 }
      ];
      setChartData(salesData);

      // الأنشطة الأخيرة (بيانات تجريبية) - استخدام تواريخ ثابتة لتجنب مشاكل hydration
      const baseTime = new Date('2024-01-01T12:00:00Z').getTime();
      const recentActivities = [
        {
          type: 'invoice_created',
          title: 'فاتورة جديدة',
          description: 'تم إنشاء فاتورة INV-000123 للعميل أحمد محمد',
          createdAt: new Date(baseTime - 5 * 60 * 1000), // منذ 5 دقائق
          link: '/dashboard/invoices/123'
        },
        {
          type: 'payment_received',
          title: 'دفعة مستلمة',
          description: 'تم استلام دفعة بقيمة $500 للفاتورة INV-000122',
          createdAt: new Date(baseTime - 15 * 60 * 1000), // منذ 15 دقيقة
          link: '/dashboard/invoices/122'
        },
        {
          type: 'product_added',
          title: 'منتج جديد',
          description: 'تم إضافة منتج "لابتوب Dell XPS 13" للمخزون',
          createdAt: new Date(baseTime - 30 * 60 * 1000), // منذ 30 دقيقة
          link: '/dashboard/products'
        },
        {
          type: 'stock_low',
          title: 'تنبيه مخزون',
          description: 'مخزون منتج "ماوس لاسلكي" أصبح منخفضاً (5 قطع متبقية)',
          createdAt: new Date(baseTime - 45 * 60 * 1000), // منذ 45 دقيقة
          link: '/dashboard/products'
        },
        {
          type: 'customer_added',
          title: 'عميل جديد',
          description: 'تم إضافة عميل جديد: شركة التقنية المتقدمة',
          createdAt: new Date(baseTime - 60 * 60 * 1000), // منذ ساعة
          link: '/dashboard/customers'
        }
      ];
      setActivities(recentActivities);

    } catch (err) {
      setError('Failed to load dashboard data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
              </div>
              <div className="flex items-center space-x-4">
                <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span suppressHydrationWarning>
                    آخر تحديث: {typeof window !== 'undefined' ? new Date().toLocaleTimeString('ar-SA') : '--:--:--'}
                  </span>
                </div>
                <button
                  onClick={loadDashboardData}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  تحديث
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="إجمالي الفواتير"
            value={stats.totalInvoices || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
            color="blue"
            loading={loading}
            trend={{ direction: 'up', value: '+12%' }}
          />

          <StatsCard
            title="الإيرادات المحصلة"
            value={formatCurrency(stats.totalRevenue || 0)}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
            color="green"
            loading={loading}
            trend={{ direction: 'up', value: '+8%' }}
          />

          <StatsCard
            title="إجمالي المنتجات"
            value={stats.totalProducts || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            }
            color="purple"
            loading={loading}
          />

          <StatsCard
            title="إجمالي العملاء"
            value={stats.totalCustomers || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            }
            color="indigo"
            loading={loading}
            trend={{ direction: 'up', value: '+5%' }}
          />
        </div>

        {/* الإحصائيات الثانوية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <StatsCard
            title="في انتظار الدفع"
            value={formatCurrency(stats.pendingRevenue || 0)}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
            color="yellow"
            loading={loading}
          />

          <StatsCard
            title="فواتير متأخرة"
            value={stats.overdueInvoices || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            }
            color="red"
            loading={loading}
          />

          <StatsCard
            title="منتجات منخفضة المخزون"
            value={stats.lowStockProducts || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            }
            color="red"
            loading={loading}
          />
        </div>

        {/* الإجراءات السريعة */}
        <div className="mb-8">
          <QuickActions />
        </div>

        {/* المحتوى الرئيسي */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* العمود الأيسر - الرسوم البيانية والأنشطة */}
          <div className="lg:col-span-2 space-y-8">
            {/* الرسم البياني للمبيعات */}
            <ChartCard
              title="المبيعات الأسبوعية"
              data={chartData}
              color="blue"
              loading={loading}
            />

            {/* الأنشطة الأخيرة */}
            <RecentActivity
              activities={activities}
              loading={loading}
            />
          </div>

          {/* العمود الأيمن - التنبيهات */}
          <div className="lg:col-span-1">
            <LowStockAlert />
          </div>
        </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(DashboardPage);
