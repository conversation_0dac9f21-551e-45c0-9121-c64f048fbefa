// frontend/components/invoice-templates/TemplateSelector.js
'use client';

import React, { useState, useEffect } from 'react';
import { templates } from './TemplatePreview';

export default function TemplateSelector({ selectedTemplate, onTemplateChange, compact = false }) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(selectedTemplate || 'classic');

  useEffect(() => {
    // Load saved template from localStorage
    const savedTemplate = localStorage.getItem('selectedInvoiceTemplate');
    if (savedTemplate && templates[savedTemplate]) {
      setCurrentTemplate(savedTemplate);
      onTemplateChange?.(savedTemplate);
    }
  }, [onTemplateChange]);

  const handleTemplateSelect = (templateId) => {
    setCurrentTemplate(templateId);
    onTemplateChange?.(templateId);
    setIsOpen(false);
    
    // Save to localStorage
    localStorage.setItem('selectedInvoiceTemplate', templateId);
  };

  if (compact) {
    return (
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
          </svg>
          <span className="text-sm font-medium">{templates[currentTemplate]?.name}</span>
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="p-2">
              {Object.values(templates).map((template) => (
                <button
                  key={template.id}
                  onClick={() => handleTemplateSelect(template.id)}
                  className={`w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                    currentTemplate === template.id ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                >
                  <div className="font-medium text-sm">{template.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{template.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">اختيار قالب الفاتورة</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.values(templates).map((template) => (
          <div
            key={template.id}
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              currentTemplate === template.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
            }`}
            onClick={() => handleTemplateSelect(template.id)}
          >
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <div className={`w-4 h-4 rounded-full border-2 ${
                  currentTemplate === template.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`}>
                  {currentTemplate === template.id && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
              </div>
              
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                
                <div className="flex flex-wrap gap-1 mt-2">
                  {template.features.slice(0, 2).map((feature, index) => (
                    <span
                      key={index}
                      className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-sm text-gray-600">
        <p>💡 يمكنك تغيير القالب في أي وقت من صفحة قوالب الفواتير</p>
      </div>
    </div>
  );
}
