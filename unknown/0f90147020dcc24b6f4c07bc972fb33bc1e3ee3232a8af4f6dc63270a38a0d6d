// frontend/components/settings/invoice/sections/TotalsSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function TotalsSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const totalsFields = [
    { field: 'showSubtotal', label: 'المجموع الفرعي', description: 'عرض المجموع قبل الضريبة' },
    { field: 'showTaxAmount', label: 'قيمة الضريبة', description: 'عرض مبلغ الضريبة' },
    { field: 'showDiscountAmount', label: 'قيمة الخصم', description: 'عرض إجمالي الخصم' },
    { field: 'showTotalAmount', label: 'المبلغ الإجمالي', description: 'عرض المبلغ النهائي' }
  ];

  return (
    <SectionHeader
      title="المجاميع والحسابات"
      section="totals"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      }
    >
      {totalsFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
