// frontend/components/settings/invoice/sections/ItemsSettings.js
'use client';

import SectionHeader from '../components/SectionHeader';
import CheckboxField from '../components/CheckboxField';

export default function ItemsSettings({ formData, onInputChange, onSelectAll, onDeselectAll }) {
  const itemsFields = [
    { field: 'showItemNumbers', label: 'ترقيم العناصر', description: 'عرض أرقام تسلسلية للعناصر' },
    { field: 'showProductImages', label: 'صور المنتجات', description: 'عرض صور المنتجات' },
    { field: 'showProductCode', label: 'كود المنتج', description: 'عرض كود أو رقم المنتج' },
    { field: 'showProductDescription', label: 'وصف المنتج', description: 'عرض وصف المنتج' },
    { field: 'showQuantity', label: 'الكمية', description: 'عرض كمية المنتج' },
    { field: 'showUnitPrice', label: 'سعر الوحدة', description: 'عرض سعر الوحدة الواحدة' },
    { field: 'showDiscount', label: 'الخصم', description: 'عرض قيمة الخصم' },
    { field: 'showTotalPrice', label: 'الإجمالي', description: 'عرض إجمالي سعر العنصر' }
  ];

  return (
    <SectionHeader
      title="عناصر الفاتورة"
      section="items"
      onSelectAll={onSelectAll}
      onDeselectAll={onDeselectAll}
      icon={
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      }
    >
      {itemsFields.map(({ field, label, description }) => (
        <CheckboxField
          key={field}
          field={field}
          label={label}
          description={description}
          checked={formData[field]}
          onChange={onInputChange}
        />
      ))}
    </SectionHeader>
  );
}
