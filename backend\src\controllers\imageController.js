// backend/src/controllers/imageController.js
const { PrismaClient } = require('@prisma/client');
const { processAndSaveImage, deleteImage } = require('../middlewares/uploadMiddleware');
const prisma = new PrismaClient();

// @desc    رفع صورة للمنتج
// @route   POST /api/products/:id/images
// @access  Private
exports.uploadProductImage = async (req, res) => {
  const { id } = req.params;
  
  try {
    // التحقق من وجود المنتج
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });
    
    if (!product) {
      return res.status(404).json({ message: 'المنتج غير موجود' });
    }
    
    if (!req.file) {
      return res.status(400).json({ message: 'لم يتم رفع أي صورة' });
    }
    
    // معالجة وحفظ الصورة
    const imageData = await processAndSaveImage(req.file, id);
    
    // حفظ معلومات الصورة في قاعدة البيانات
    const savedImage = await prisma.productImage.create({
      data: {
        filename: imageData.filename,
        originalName: imageData.originalName,
        path: imageData.path,
        size: imageData.size,
        mimeType: imageData.mimeType,
        productId: parseInt(id),
        order: await getNextImageOrder(parseInt(id)),
      },
    });
    
    res.status(201).json({
      message: 'تم رفع الصورة بنجاح',
      image: savedImage,
    });
  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);
    res.status(500).json({ 
      message: 'فشل في رفع الصورة', 
      error: error.message 
    });
  }
};

// @desc    الحصول على صور المنتج
// @route   GET /api/products/:id/images
// @access  Private
exports.getProductImages = async (req, res) => {
  const { id } = req.params;
  
  try {
    const images = await prisma.productImage.findMany({
      where: { productId: parseInt(id) },
      orderBy: [
        { isMain: 'desc' },
        { order: 'asc' },
        { createdAt: 'asc' }
      ],
    });
    
    res.json(images);
  } catch (error) {
    console.error('خطأ في جلب الصور:', error);
    res.status(500).json({ 
      message: 'فشل في جلب الصور', 
      error: error.message 
    });
  }
};

// @desc    تحديد الصورة الرئيسية
// @route   PUT /api/images/:imageId/main
// @access  Private
exports.setMainImage = async (req, res) => {
  const { imageId } = req.params;
  
  try {
    // الحصول على معلومات الصورة
    const image = await prisma.productImage.findUnique({
      where: { id: parseInt(imageId) }
    });
    
    if (!image) {
      return res.status(404).json({ message: 'الصورة غير موجودة' });
    }
    
    // إزالة الصورة الرئيسية الحالية
    await prisma.productImage.updateMany({
      where: { 
        productId: image.productId,
        isMain: true 
      },
      data: { isMain: false }
    });
    
    // تحديد الصورة الجديدة كرئيسية
    const updatedImage = await prisma.productImage.update({
      where: { id: parseInt(imageId) },
      data: { isMain: true }
    });
    
    res.json({
      message: 'تم تحديد الصورة الرئيسية بنجاح',
      image: updatedImage,
    });
  } catch (error) {
    console.error('خطأ في تحديد الصورة الرئيسية:', error);
    res.status(500).json({ 
      message: 'فشل في تحديد الصورة الرئيسية', 
      error: error.message 
    });
  }
};

// @desc    تحديث ترتيب الصور
// @route   PUT /api/images/reorder
// @access  Private
exports.reorderImages = async (req, res) => {
  const { imageOrders } = req.body; // [{ id: 1, order: 0 }, { id: 2, order: 1 }]
  
  try {
    // تحديث ترتيب كل صورة
    const updatePromises = imageOrders.map(({ id, order }) =>
      prisma.productImage.update({
        where: { id: parseInt(id) },
        data: { order: parseInt(order) }
      })
    );
    
    await Promise.all(updatePromises);
    
    res.json({ message: 'تم تحديث ترتيب الصور بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث ترتيب الصور:', error);
    res.status(500).json({ 
      message: 'فشل في تحديث ترتيب الصور', 
      error: error.message 
    });
  }
};

// @desc    حذف صورة
// @route   DELETE /api/images/:imageId
// @access  Private
exports.deleteProductImage = async (req, res) => {
  const { imageId } = req.params;
  
  try {
    // الحصول على معلومات الصورة
    const image = await prisma.productImage.findUnique({
      where: { id: parseInt(imageId) }
    });
    
    if (!image) {
      return res.status(404).json({ message: 'الصورة غير موجودة' });
    }
    
    // حذف الصورة من قاعدة البيانات
    await prisma.productImage.delete({
      where: { id: parseInt(imageId) }
    });
    
    // حذف ملف الصورة
    await deleteImage(image.path);
    
    res.json({ message: 'تم حذف الصورة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الصورة:', error);
    res.status(500).json({ 
      message: 'فشل في حذف الصورة', 
      error: error.message 
    });
  }
};

// دالة مساعدة للحصول على الترتيب التالي للصورة
const getNextImageOrder = async (productId) => {
  const lastImage = await prisma.productImage.findFirst({
    where: { productId },
    orderBy: { order: 'desc' }
  });
  
  return lastImage ? lastImage.order + 1 : 0;
};
