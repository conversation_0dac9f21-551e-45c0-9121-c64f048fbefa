// backend/src/controllers/customerController.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// @desc    Get all customers
// @route   GET /api/customers
// @access  Public (for now)
exports.getCustomers = async (req, res) => {
  try {
    const customers = await prisma.customer.findMany();
    res.status(200).json(customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ message: 'Failed to fetch customers', error: error.message });
  }
};

// @desc    Create a new customer
// @route   POST /api/customers
// @access  Public (for now)
exports.createCustomer = async (req, res) => {
  const { name, email, phone, address, taxId, companyName } = req.body;

  // Basic validation
  if (!name) {
    return res.status(400).json({ message: 'Customer name is required.' });
  }

  try {
    const newCustomer = await prisma.customer.create({
      data: {
        name,
        email,
        phone,
        address,
        taxId,
        companyName,
      },
    });
    res.status(201).json(newCustomer);
  } catch (error) {
    console.error('Error creating customer:', error);
    if (error.code === 'P2002' && error.meta?.target.includes('email')) {
      return res.status(409).json({ message: 'A customer with this email already exists.' });
    }
    res.status(500).json({ message: 'Failed to create customer', error: error.message });
  }
};

// @desc    Get single customer by ID
// @route   GET /api/customers/:id
// @access  Public (for now)
exports.getCustomerById = async (req, res) => {
  const { id } = req.params;
  try {
    const customer = await prisma.customer.findUnique({
      where: { id: parseInt(id) },
    });
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }
    res.status(200).json(customer);
  } catch (error) {
    console.error('Error fetching customer by ID:', error);
    res.status(500).json({ message: 'Failed to fetch customer', error: error.message });
  }
};

// @desc    Update a customer
// @route   PUT /api/customers/:id
// @access  Public (for now)
exports.updateCustomer = async (req, res) => {
  const { id } = req.params;
  const { name, email, phone, address, taxId, companyName } = req.body;

  try {
    const updatedCustomer = await prisma.customer.update({
      where: { id: parseInt(id) },
      data: {
        name,
        email,
        phone,
        address,
        taxId,
        companyName,
      },
    });
    res.status(200).json(updatedCustomer);
  } catch (error) {
    console.error('Error updating customer:', error);
    if (error.code === 'P2002' && error.meta?.target.includes('email')) {
      return res.status(409).json({ message: 'A customer with this email already exists.' });
    }
    res.status(404).json({ message: 'Customer not found or update failed', error: error.message });
  }
};

// @desc    Delete a customer
// @route   DELETE /api/customers/:id
// @access  Public (for now)
exports.deleteCustomer = async (req, res) => {
  const { id } = req.params;
  try {
    await prisma.customer.delete({
      where: { id: parseInt(id) },
    });
    res.status(204).send(); // No Content
  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(404).json({ message: 'Customer not found or delete failed', error: error.message });
  }
};