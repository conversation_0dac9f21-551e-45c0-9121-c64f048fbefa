// backend/src/controllers/productController.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// @desc    Get all products
// @route   GET /api/products
// @access  Public (for now)
exports.getProducts = async (req, res) => {
  try {
    const products = await prisma.product.findMany({
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ message: 'Failed to fetch products', error: error.message });
  }
};

// @desc    Create a new product
// @route   POST /api/products
// @access  Public (for now)
exports.createProduct = async (req, res) => {
  const { name, description, price, stock, minStock, category, supplier, barcode } = req.body;

  // Basic validation
  if (!name || !price || stock === undefined) {
    return res.status(400).json({ message: 'Please provide name, price, and stock for the product.' });
  }

  try {
    const newProduct = await prisma.product.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        stock: parseInt(stock),
        minStock: minStock ? parseInt(minStock) : 0,
        category,
        supplier,
        barcode: barcode || null, // جعل الباركود null إذا كان فارغاً
      },
      include: {
        images: true
      }
    });
    res.status(201).json(newProduct);
  } catch (error) {
    console.error('Error creating product:', error);
    if (error.code === 'P2002') {
      if (error.meta?.target.includes('name')) {
        return res.status(409).json({ message: 'A product with this name already exists.' });
      }
      if (error.meta?.target.includes('barcode')) {
        return res.status(409).json({ message: 'A product with this barcode already exists.' });
      }
    }
    res.status(500).json({ message: 'Failed to create product', error: error.message });
  }
};

// @desc    Get single product by ID
// @route   GET /api/products/:id
// @access  Public (for now)
exports.getProductById = async (req, res) => {
  const { id } = req.params;
  try {
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        images: {
          orderBy: [
            { isMain: 'desc' },
            { order: 'asc' }
          ]
        }
      }
    });
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    res.status(200).json(product);
  } catch (error) {
    console.error('Error fetching product by ID:', error);
    res.status(500).json({ message: 'Failed to fetch product', error: error.message });
  }
};

// @desc    Update a product
// @route   PUT /api/products/:id
// @access  Public (for now)
exports.updateProduct = async (req, res) => {
  const { id } = req.params;
  const { name, description, price, stock, imageUrl, category, supplier } = req.body;

  try {
    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        price: price ? parseFloat(price) : undefined,
        stock: stock ? parseInt(stock) : undefined,
        imageUrl,
        category,
        supplier,
      },
    });
    res.status(200).json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    if (error.code === 'P2002' && error.meta?.target.includes('name')) {
      return res.status(409).json({ message: 'A product with this name already exists.' });
    }
    res.status(404).json({ message: 'Product not found or update failed', error: error.message });
  }
};

// @desc    Delete a product
// @route   DELETE /api/products/:id
// @access  Public (for now)
exports.deleteProduct = async (req, res) => {
  const { id } = req.params;
  try {
    await prisma.product.delete({
      where: { id: parseInt(id) },
    });
    res.status(204).send(); // No Content
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(404).json({ message: 'Product not found or delete failed', error: error.message });
  }
};