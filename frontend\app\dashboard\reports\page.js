// frontend/app/dashboard/reports/page.js
'use client';

import { useState, useEffect } from 'react';
import { getInvoices, getProducts, getCustomers, getInvoiceStats } from '@/lib/api';
import withAuth from '@/components/withAuth';
import StatsCard from '@/components/dashboard/StatsCard';
import ChartCard from '@/components/dashboard/ChartCard';

function ReportsPage() {
  const [reportData, setReportData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // آخر 30 يوم
    endDate: new Date().toISOString().split('T')[0]
  });
  const [reportType, setReportType] = useState('sales');

  useEffect(() => {
    loadReportData();
  }, [dateRange, reportType]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      
      const [invoices, products, customers, stats] = await Promise.all([
        getInvoices({ limit: 1000 }), // جلب جميع الفواتير
        getProducts(),
        getCustomers(),
        getInvoiceStats()
      ]);

      // فلترة البيانات حسب التاريخ
      const filteredInvoices = invoices.invoices?.filter(invoice => {
        const invoiceDate = new Date(invoice.createdAt);
        const start = new Date(dateRange.startDate);
        const end = new Date(dateRange.endDate);
        return invoiceDate >= start && invoiceDate <= end;
      }) || [];

      // حساب التقارير
      const salesReport = calculateSalesReport(filteredInvoices);
      const productReport = calculateProductReport(filteredInvoices, products);
      const customerReport = calculateCustomerReport(filteredInvoices, customers);

      setReportData({
        sales: salesReport,
        products: productReport,
        customers: customerReport,
        overview: stats
      });

    } catch (err) {
      setError('Failed to load report data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const calculateSalesReport = (invoices) => {
    const totalSales = invoices.reduce((sum, inv) => sum + parseFloat(inv.totalAmount), 0);
    const totalInvoices = invoices.length;
    const paidInvoices = invoices.filter(inv => inv.paymentStatus === 'paid').length;
    const averageInvoiceValue = totalInvoices > 0 ? totalSales / totalInvoices : 0;

    // مبيعات يومية
    const dailySales = {};
    invoices.forEach(invoice => {
      const date = new Date(invoice.createdAt).toISOString().split('T')[0];
      dailySales[date] = (dailySales[date] || 0) + parseFloat(invoice.totalAmount);
    });

    const chartData = Object.entries(dailySales)
      .sort(([a], [b]) => new Date(a) - new Date(b))
      .slice(-7) // آخر 7 أيام
      .map(([date, amount]) => ({
        label: new Date(date).toLocaleDateString('ar-SA', { weekday: 'short' }),
        value: amount
      }));

    return {
      totalSales,
      totalInvoices,
      paidInvoices,
      averageInvoiceValue,
      chartData
    };
  };

  const calculateProductReport = (invoices, products) => {
    const productSales = {};
    
    invoices.forEach(invoice => {
      invoice.items?.forEach(item => {
        const productId = item.productId;
        if (!productSales[productId]) {
          productSales[productId] = {
            product: item.product,
            quantity: 0,
            revenue: 0
          };
        }
        productSales[productId].quantity += item.quantity;
        productSales[productId].revenue += parseFloat(item.total);
      });
    });

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)
      .map(item => ({
        label: item.product?.name || 'Unknown Product',
        value: item.revenue
      }));

    return {
      topProducts,
      totalProductsSold: Object.values(productSales).reduce((sum, item) => sum + item.quantity, 0),
      uniqueProductsSold: Object.keys(productSales).length
    };
  };

  const calculateCustomerReport = (invoices, customers) => {
    const customerSales = {};
    
    invoices.forEach(invoice => {
      const customerId = invoice.customerId;
      if (!customerSales[customerId]) {
        customerSales[customerId] = {
          customer: invoice.customer,
          invoiceCount: 0,
          totalSpent: 0
        };
      }
      customerSales[customerId].invoiceCount += 1;
      customerSales[customerId].totalSpent += parseFloat(invoice.totalAmount);
    });

    const topCustomers = Object.values(customerSales)
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 5)
      .map(item => ({
        label: item.customer?.name || 'Unknown Customer',
        value: item.totalSpent
      }));

    return {
      topCustomers,
      activeCustomers: Object.keys(customerSales).length,
      averageCustomerValue: Object.values(customerSales).length > 0 
        ? Object.values(customerSales).reduce((sum, item) => sum + item.totalSpent, 0) / Object.values(customerSales).length 
        : 0
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const exportReport = () => {
    // تصدير التقرير كـ CSV أو PDF
    const csvContent = generateCSVReport();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `report_${reportType}_${dateRange.startDate}_${dateRange.endDate}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateCSVReport = () => {
    let csvContent = '';
    
    if (reportType === 'sales') {
      csvContent = 'Date,Sales Amount\n';
      reportData.sales?.chartData?.forEach(item => {
        csvContent += `${item.label},${item.value}\n`;
      });
    }
    
    return csvContent;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={exportReport}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                تصدير التقرير
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* فلاتر التقرير */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع التقرير</label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              >
                <option value="sales">تقرير المبيعات</option>
                <option value="products">تقرير المنتجات</option>
                <option value="customers">تقرير العملاء</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                className="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={loadReportData}
                className="w-full px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
              >
                تحديث التقرير
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* تقرير المبيعات */}
        {reportType === 'sales' && (
          <div className="space-y-8">
            {/* إحصائيات المبيعات */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatsCard
                title="إجمالي المبيعات"
                value={formatCurrency(reportData.sales?.totalSales || 0)}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                }
                color="green"
                loading={loading}
              />

              <StatsCard
                title="عدد الفواتير"
                value={reportData.sales?.totalInvoices || 0}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                }
                color="blue"
                loading={loading}
              />

              <StatsCard
                title="الفواتير المدفوعة"
                value={reportData.sales?.paidInvoices || 0}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
                color="green"
                loading={loading}
              />

              <StatsCard
                title="متوسط قيمة الفاتورة"
                value={formatCurrency(reportData.sales?.averageInvoiceValue || 0)}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                }
                color="purple"
                loading={loading}
              />
            </div>

            {/* رسم بياني للمبيعات */}
            <ChartCard
              title="المبيعات اليومية"
              data={reportData.sales?.chartData || []}
              color="green"
              loading={loading}
            />
          </div>
        )}

        {/* تقرير المنتجات */}
        {reportType === 'products' && (
          <div className="space-y-8">
            {/* إحصائيات المنتجات */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <StatsCard
                title="إجمالي المنتجات المباعة"
                value={reportData.products?.totalProductsSold || 0}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                }
                color="purple"
                loading={loading}
              />

              <StatsCard
                title="أنواع المنتجات المباعة"
                value={reportData.products?.uniqueProductsSold || 0}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                }
                color="indigo"
                loading={loading}
              />

              <StatsCard
                title="أفضل منتج مبيعاً"
                value={reportData.products?.topProducts?.[0]?.label || 'لا يوجد'}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                }
                color="yellow"
                loading={loading}
              />
            </div>

            {/* أفضل المنتجات */}
            <ChartCard
              title="أفضل المنتجات مبيعاً"
              data={reportData.products?.topProducts || []}
              color="purple"
              loading={loading}
            />
          </div>
        )}

        {/* تقرير العملاء */}
        {reportType === 'customers' && (
          <div className="space-y-8">
            {/* إحصائيات العملاء */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <StatsCard
                title="العملاء النشطون"
                value={reportData.customers?.activeCustomers || 0}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
                color="indigo"
                loading={loading}
              />

              <StatsCard
                title="متوسط قيمة العميل"
                value={formatCurrency(reportData.customers?.averageCustomerValue || 0)}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                }
                color="green"
                loading={loading}
              />

              <StatsCard
                title="أفضل عميل"
                value={reportData.customers?.topCustomers?.[0]?.label || 'لا يوجد'}
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                }
                color="yellow"
                loading={loading}
              />
            </div>

            {/* أفضل العملاء */}
            <ChartCard
              title="أفضل العملاء"
              data={reportData.customers?.topCustomers || []}
              color="indigo"
              loading={loading}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default withAuth(ReportsPage);
