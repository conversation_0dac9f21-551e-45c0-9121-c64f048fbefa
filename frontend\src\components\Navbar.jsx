// frontend/src/components/Navbar.jsx
import Link from 'next/link';

export default function Navbar() {
  return (
    <nav className="bg-gray-800 p-4">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-white text-2xl font-bold">
          Invoice App
        </Link>
        <div className="space-x-4">
          <Link href="/products" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
            Products
          </Link>
          {/* <Link href="/customers" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
            Customers
          </Link>
          <Link href="/invoices/create" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
            Create Invoice
          </Link> */}
        </div>
      </div>
    </nav>
  );
}
// frontend/src/app/layout.js
import './globals.css';
import Navbar from '@/components/Navbar'; // استيراد Navbar

export const metadata = {
  title: 'Invoice App',
  description: 'A comprehensive invoice management application',
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <Navbar /> {/* إضافة شريط التنقل هنا */}
        <main>{children}</main> {/* محتوى الصفحة الفعلي */}
      </body>
    </html>
  );
}